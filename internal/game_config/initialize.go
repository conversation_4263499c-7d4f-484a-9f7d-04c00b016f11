package game_config

// auto generated by tools, do not edit it manually
// Initialize 初始化游戏配置
func Initialize() {
	AIActionConfig = newAIActionConfig()
	AIConditionConfig = newAIConditionConfig()
	AINodeConfig = newAINodeConfig()
	AvatarConfig = newAvatarConfig()
	AwardConfig = newAwardConfig()
	AwardGroupConfig = newAwardGroupConfig()
	ConditionConfig = newConditionConfig()
	ConditionTriggerConfig = newConditionTriggerConfig()
	ConstConfig = newConstConfig()
	ConstShopGoodsConfig = newConstShopGoodsConfig()
	GainItemConfig = newGainItemConfig()
	GuideConfig = newGuideConfig()
	GuideGroupConfig = newGuideGroupConfig()
	HeadPortraitConfig = newHeadPortraitConfig()
	HeroConfig = newHeroConfig()
	HeroLevelConfig = newHeroLevelConfig()
	HeroQualityConfig = newHeroQualityConfig()
	HeroStarConfig = newHeroStarConfig()
	ItemConfig = newItemConfig()
	LevelConfig = newLevelConfig()
	MUnitConfig = newMUnitConfig()
	MailConfig = newMailConfig()
	MainCityScaleConfig = newMainCityScaleConfig()
	MainTaskChapterConfig = newMainTaskChapterConfig()
	MainTaskConfig = newMainTaskConfig()
	MapConfig = newMapConfig()
	MiniGameConfig = newMiniGameConfig()
	RankConfig = newRankConfig()
	RechargeConfig = newRechargeConfig()
	ShopGoodsConfig = newShopGoodsConfig()
	ShopTabConfig = newShopTabConfig()
	StoryPlotConditionConfig = newStoryPlotConditionConfig()
	StoryPlotConfig = newStoryPlotConfig()
	StoryPlotGroupConfig = newStoryPlotGroupConfig()
	StoryPlotInstructsConfig = newStoryPlotInstructsConfig()
	SystemOpenConfig = newSystemOpenConfig()
	TagConfig = newTagConfig()
	TileMapConfig = newTileMapConfig()
	UserNameConfig = newUserNameConfig()

}
