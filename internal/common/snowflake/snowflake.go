package snowflake

import (
	"fmt"
	"sync/atomic"
	"time"
)

const (
	epoch         = int64(1700000000) // 自定义时间戳（秒），约为 2023-11-15
	timestampBits = uint(32)
	machineIDBits = uint(8)
	sequenceBits  = uint(13)

	maxMachineID = int64(-1) ^ (int64(-1) << machineIDBits)
	maxSequence  = int64(-1) ^ (int64(-1) << sequenceBits)

	machineIDShift = sequenceBits
	timestampShift = sequenceBits + machineIDBits
)

type LockFreeSnowflake struct {
	machineID int64
	state     int64 // 高32位是 timestamp，低13位是 sequence
}

func NewSnowflake(machineID int64) (*LockFreeSnowflake, error) {
	if machineID < 0 || machineID > maxMachineID {
		return nil, fmt.Errorf("machineID must be between 0 and %d", maxMachineID)
	}
	return &LockFreeSnowflake{
		machineID: machineID,
	}, nil
}

func (s *LockFreeSnowflake) NextID() int64 {
	for {
		now := time.Now().Unix()
		if now < epoch {
			panic("Clock moved backwards!")
		}
		timestamp := now - epoch

		oldState := atomic.LoadInt64(&s.state)
		lastTimestamp := oldState >> 13
		sequence := oldState & maxSequence

		var newSequence int64
		if timestamp == lastTimestamp {
			newSequence = (sequence + 1) & maxSequence
			if newSequence == 0 {
				// 自旋等待下一秒
				continue
			}
		} else {
			newSequence = 0
		}

		newState := (timestamp << 13) | newSequence
		if atomic.CompareAndSwapInt64(&s.state, oldState, newState) {
			id := (timestamp << timestampShift) |
				(s.machineID << machineIDShift) |
				newSequence
			return id
		}
	}
}
