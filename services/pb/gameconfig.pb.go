// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v6.30.2
// source: gameconfig.proto

package pb

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type EGender int32

const (
	EGender_EGender_Invalid       EGender = 0
	EGender_EGender_Male          EGender = 1
	EGender_EGender_Female        EGender = 2
	EGender_EGender_Other         EGender = 4
	EGender_EGender_MaleAndFemale EGender = 3
	EGender_EGender_All           EGender = 7
)

// Enum value maps for EGender.
var (
	EGender_name = map[int32]string{
		0: "EGender_Invalid",
		1: "EGender_Male",
		2: "EGender_Female",
		4: "EGender_Other",
		3: "EGender_MaleAndFemale",
		7: "EGender_All",
	}
	EGender_value = map[string]int32{
		"EGender_Invalid":       0,
		"EGender_Male":          1,
		"EGender_Female":        2,
		"EGender_Other":         4,
		"EGender_MaleAndFemale": 3,
		"EGender_All":           7,
	}
)

func (x EGender) Enum() *EGender {
	p := new(EGender)
	*p = x
	return p
}

func (x EGender) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (EGender) Descriptor() protoreflect.EnumDescriptor {
	return file_gameconfig_proto_enumTypes[0].Descriptor()
}

func (EGender) Type() protoreflect.EnumType {
	return &file_gameconfig_proto_enumTypes[0]
}

func (x EGender) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Do not use.
func (x *EGender) UnmarshalJSON(b []byte) error {
	num, err := protoimpl.X.UnmarshalJSONEnum(x.Descriptor(), b)
	if err != nil {
		return err
	}
	*x = EGender(num)
	return nil
}

// Deprecated: Use EGender.Descriptor instead.
func (EGender) EnumDescriptor() ([]byte, []int) {
	return file_gameconfig_proto_rawDescGZIP(), []int{0}
}

type EAttr int32

const (
	EAttr_EAttr_Invalid                  EAttr = 0
	EAttr_EAttr_KitchenRecoverHungry     EAttr = 1
	EAttr_EAttr_KitchenRecoverHungryRate EAttr = 2
	EAttr_EAttr_KitchenRecoverTired      EAttr = 3
	EAttr_EAttr_KitchenRecoverTiredRate  EAttr = 4
	EAttr_EAttr_KitchenRecoverMood       EAttr = 5
	EAttr_EAttr_KitchenRecoverMoodRate   EAttr = 6
	EAttr_EAttr_HomeBuildingTired        EAttr = 7
	EAttr_EAttr_HeroPowerRate            EAttr = 8
	EAttr_EAttr_HeroEatRate              EAttr = 9
	EAttr_EAttr_HeroProdaceTimeRate      EAttr = 10
	EAttr_EAttr_HeroSleepRate            EAttr = 11
	EAttr_EAttr_ExploreCostRate          EAttr = 12
	EAttr_EAttr_HeroExpAddRate           EAttr = 13
)

// Enum value maps for EAttr.
var (
	EAttr_name = map[int32]string{
		0:  "EAttr_Invalid",
		1:  "EAttr_KitchenRecoverHungry",
		2:  "EAttr_KitchenRecoverHungryRate",
		3:  "EAttr_KitchenRecoverTired",
		4:  "EAttr_KitchenRecoverTiredRate",
		5:  "EAttr_KitchenRecoverMood",
		6:  "EAttr_KitchenRecoverMoodRate",
		7:  "EAttr_HomeBuildingTired",
		8:  "EAttr_HeroPowerRate",
		9:  "EAttr_HeroEatRate",
		10: "EAttr_HeroProdaceTimeRate",
		11: "EAttr_HeroSleepRate",
		12: "EAttr_ExploreCostRate",
		13: "EAttr_HeroExpAddRate",
	}
	EAttr_value = map[string]int32{
		"EAttr_Invalid":                  0,
		"EAttr_KitchenRecoverHungry":     1,
		"EAttr_KitchenRecoverHungryRate": 2,
		"EAttr_KitchenRecoverTired":      3,
		"EAttr_KitchenRecoverTiredRate":  4,
		"EAttr_KitchenRecoverMood":       5,
		"EAttr_KitchenRecoverMoodRate":   6,
		"EAttr_HomeBuildingTired":        7,
		"EAttr_HeroPowerRate":            8,
		"EAttr_HeroEatRate":              9,
		"EAttr_HeroProdaceTimeRate":      10,
		"EAttr_HeroSleepRate":            11,
		"EAttr_ExploreCostRate":          12,
		"EAttr_HeroExpAddRate":           13,
	}
)

func (x EAttr) Enum() *EAttr {
	p := new(EAttr)
	*p = x
	return p
}

func (x EAttr) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (EAttr) Descriptor() protoreflect.EnumDescriptor {
	return file_gameconfig_proto_enumTypes[1].Descriptor()
}

func (EAttr) Type() protoreflect.EnumType {
	return &file_gameconfig_proto_enumTypes[1]
}

func (x EAttr) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Do not use.
func (x *EAttr) UnmarshalJSON(b []byte) error {
	num, err := protoimpl.X.UnmarshalJSONEnum(x.Descriptor(), b)
	if err != nil {
		return err
	}
	*x = EAttr(num)
	return nil
}

// Deprecated: Use EAttr.Descriptor instead.
func (EAttr) EnumDescriptor() ([]byte, []int) {
	return file_gameconfig_proto_rawDescGZIP(), []int{1}
}

type EHelpUIId int32

const (
	EHelpUIId_EHelpUIId_invalid EHelpUIId = 0
	EHelpUIId_EHelpUIId_Test    EHelpUIId = 2
)

// Enum value maps for EHelpUIId.
var (
	EHelpUIId_name = map[int32]string{
		0: "EHelpUIId_invalid",
		2: "EHelpUIId_Test",
	}
	EHelpUIId_value = map[string]int32{
		"EHelpUIId_invalid": 0,
		"EHelpUIId_Test":    2,
	}
)

func (x EHelpUIId) Enum() *EHelpUIId {
	p := new(EHelpUIId)
	*p = x
	return p
}

func (x EHelpUIId) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (EHelpUIId) Descriptor() protoreflect.EnumDescriptor {
	return file_gameconfig_proto_enumTypes[2].Descriptor()
}

func (EHelpUIId) Type() protoreflect.EnumType {
	return &file_gameconfig_proto_enumTypes[2]
}

func (x EHelpUIId) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Do not use.
func (x *EHelpUIId) UnmarshalJSON(b []byte) error {
	num, err := protoimpl.X.UnmarshalJSONEnum(x.Descriptor(), b)
	if err != nil {
		return err
	}
	*x = EHelpUIId(num)
	return nil
}

// Deprecated: Use EHelpUIId.Descriptor instead.
func (EHelpUIId) EnumDescriptor() ([]byte, []int) {
	return file_gameconfig_proto_rawDescGZIP(), []int{2}
}

type ESystemId int32

const (
	ESystemId_ESystemId_Invalid     ESystemId = 0
	ESystemId_ESystemId_Base        ESystemId = 1
	ESystemId_ESystemId_Role        ESystemId = 2
	ESystemId_ESystemId_RoleReName  ESystemId = 2001
	ESystemId_ESystemId_Login       ESystemId = 3
	ESystemId_ESystemId_Lobby       ESystemId = 4
	ESystemId_ESystemId_GM          ESystemId = 5
	ESystemId_ESystemId_Bag         ESystemId = 6
	ESystemId_ESystemId_Condition   ESystemId = 7
	ESystemId_ESystemId_Task        ESystemId = 8
	ESystemId_ESystemId_Hero        ESystemId = 10
	ESystemId_ESystemId_HeroLevel   ESystemId = 1001
	ESystemId_ESystemId_HeroStar    ESystemId = 1002
	ESystemId_ESystemId_DataCache   ESystemId = 11
	ESystemId_ESystemId_SystemOpen  ESystemId = 12
	ESystemId_ESystemId_TimeSys     ESystemId = 13
	ESystemId_ESystemId_AI          ESystemId = 14
	ESystemId_ESystemId_Attr        ESystemId = 15
	ESystemId_ESystemId_HudRightBtn ESystemId = 16
	ESystemId_ESystemId_Mail        ESystemId = 17
	ESystemId_ESystemId_MainCity    ESystemId = 18
	ESystemId_ESystemId_SDK         ESystemId = 19
	ESystemId_ESystemId_WeeklyRank  ESystemId = 22
	ESystemId_ESystemId_BaseRank    ESystemId = 23
	ESystemId_ESystemId_Activity    ESystemId = 24
	ESystemId_ESystemId_StoryPlot   ESystemId = 25
	ESystemId_ESystemId_Shop        ESystemId = 26
	ESystemId_ESystemId_Tag         ESystemId = 27
	ESystemId_ESystemId_MainHudTips ESystemId = 28
	ESystemId_ESystemId_Guide       ESystemId = 29
	ESystemId_ESystemId_CondTrigger ESystemId = 30
)

// Enum value maps for ESystemId.
var (
	ESystemId_name = map[int32]string{
		0:    "ESystemId_Invalid",
		1:    "ESystemId_Base",
		2:    "ESystemId_Role",
		2001: "ESystemId_RoleReName",
		3:    "ESystemId_Login",
		4:    "ESystemId_Lobby",
		5:    "ESystemId_GM",
		6:    "ESystemId_Bag",
		7:    "ESystemId_Condition",
		8:    "ESystemId_Task",
		10:   "ESystemId_Hero",
		1001: "ESystemId_HeroLevel",
		1002: "ESystemId_HeroStar",
		11:   "ESystemId_DataCache",
		12:   "ESystemId_SystemOpen",
		13:   "ESystemId_TimeSys",
		14:   "ESystemId_AI",
		15:   "ESystemId_Attr",
		16:   "ESystemId_HudRightBtn",
		17:   "ESystemId_Mail",
		18:   "ESystemId_MainCity",
		19:   "ESystemId_SDK",
		22:   "ESystemId_WeeklyRank",
		23:   "ESystemId_BaseRank",
		24:   "ESystemId_Activity",
		25:   "ESystemId_StoryPlot",
		26:   "ESystemId_Shop",
		27:   "ESystemId_Tag",
		28:   "ESystemId_MainHudTips",
		29:   "ESystemId_Guide",
		30:   "ESystemId_CondTrigger",
	}
	ESystemId_value = map[string]int32{
		"ESystemId_Invalid":     0,
		"ESystemId_Base":        1,
		"ESystemId_Role":        2,
		"ESystemId_RoleReName":  2001,
		"ESystemId_Login":       3,
		"ESystemId_Lobby":       4,
		"ESystemId_GM":          5,
		"ESystemId_Bag":         6,
		"ESystemId_Condition":   7,
		"ESystemId_Task":        8,
		"ESystemId_Hero":        10,
		"ESystemId_HeroLevel":   1001,
		"ESystemId_HeroStar":    1002,
		"ESystemId_DataCache":   11,
		"ESystemId_SystemOpen":  12,
		"ESystemId_TimeSys":     13,
		"ESystemId_AI":          14,
		"ESystemId_Attr":        15,
		"ESystemId_HudRightBtn": 16,
		"ESystemId_Mail":        17,
		"ESystemId_MainCity":    18,
		"ESystemId_SDK":         19,
		"ESystemId_WeeklyRank":  22,
		"ESystemId_BaseRank":    23,
		"ESystemId_Activity":    24,
		"ESystemId_StoryPlot":   25,
		"ESystemId_Shop":        26,
		"ESystemId_Tag":         27,
		"ESystemId_MainHudTips": 28,
		"ESystemId_Guide":       29,
		"ESystemId_CondTrigger": 30,
	}
)

func (x ESystemId) Enum() *ESystemId {
	p := new(ESystemId)
	*p = x
	return p
}

func (x ESystemId) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ESystemId) Descriptor() protoreflect.EnumDescriptor {
	return file_gameconfig_proto_enumTypes[3].Descriptor()
}

func (ESystemId) Type() protoreflect.EnumType {
	return &file_gameconfig_proto_enumTypes[3]
}

func (x ESystemId) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Do not use.
func (x *ESystemId) UnmarshalJSON(b []byte) error {
	num, err := protoimpl.X.UnmarshalJSONEnum(x.Descriptor(), b)
	if err != nil {
		return err
	}
	*x = ESystemId(num)
	return nil
}

// Deprecated: Use ESystemId.Descriptor instead.
func (ESystemId) EnumDescriptor() ([]byte, []int) {
	return file_gameconfig_proto_rawDescGZIP(), []int{3}
}

type EConfirmRepeatType int32

const (
	EConfirmRepeatType_EConfirmRepeatType_Forever   EConfirmRepeatType = 0
	EConfirmRepeatType_EConfirmRepeatType_OnceToday EConfirmRepeatType = 1
	EConfirmRepeatType_EConfirmRepeatType_LoginTime EConfirmRepeatType = 2
	EConfirmRepeatType_EConfirmRepeatType_Once      EConfirmRepeatType = 3
)

// Enum value maps for EConfirmRepeatType.
var (
	EConfirmRepeatType_name = map[int32]string{
		0: "EConfirmRepeatType_Forever",
		1: "EConfirmRepeatType_OnceToday",
		2: "EConfirmRepeatType_LoginTime",
		3: "EConfirmRepeatType_Once",
	}
	EConfirmRepeatType_value = map[string]int32{
		"EConfirmRepeatType_Forever":   0,
		"EConfirmRepeatType_OnceToday": 1,
		"EConfirmRepeatType_LoginTime": 2,
		"EConfirmRepeatType_Once":      3,
	}
)

func (x EConfirmRepeatType) Enum() *EConfirmRepeatType {
	p := new(EConfirmRepeatType)
	*p = x
	return p
}

func (x EConfirmRepeatType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (EConfirmRepeatType) Descriptor() protoreflect.EnumDescriptor {
	return file_gameconfig_proto_enumTypes[4].Descriptor()
}

func (EConfirmRepeatType) Type() protoreflect.EnumType {
	return &file_gameconfig_proto_enumTypes[4]
}

func (x EConfirmRepeatType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Do not use.
func (x *EConfirmRepeatType) UnmarshalJSON(b []byte) error {
	num, err := protoimpl.X.UnmarshalJSONEnum(x.Descriptor(), b)
	if err != nil {
		return err
	}
	*x = EConfirmRepeatType(num)
	return nil
}

// Deprecated: Use EConfirmRepeatType.Descriptor instead.
func (EConfirmRepeatType) EnumDescriptor() ([]byte, []int) {
	return file_gameconfig_proto_rawDescGZIP(), []int{4}
}

type EItem int32

const (
	EItem_EItem_Money    EItem = 0
	EItem_EItem_Box      EItem = 1
	EItem_EItem_Building EItem = 2
	EItem_EItem_HeadIcon EItem = 3
	EItem_EItem_Title    EItem = 4
	EItem_EItem_Coupon   EItem = 9
)

// Enum value maps for EItem.
var (
	EItem_name = map[int32]string{
		0: "EItem_Money",
		1: "EItem_Box",
		2: "EItem_Building",
		3: "EItem_HeadIcon",
		4: "EItem_Title",
		9: "EItem_Coupon",
	}
	EItem_value = map[string]int32{
		"EItem_Money":    0,
		"EItem_Box":      1,
		"EItem_Building": 2,
		"EItem_HeadIcon": 3,
		"EItem_Title":    4,
		"EItem_Coupon":   9,
	}
)

func (x EItem) Enum() *EItem {
	p := new(EItem)
	*p = x
	return p
}

func (x EItem) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (EItem) Descriptor() protoreflect.EnumDescriptor {
	return file_gameconfig_proto_enumTypes[5].Descriptor()
}

func (EItem) Type() protoreflect.EnumType {
	return &file_gameconfig_proto_enumTypes[5]
}

func (x EItem) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Do not use.
func (x *EItem) UnmarshalJSON(b []byte) error {
	num, err := protoimpl.X.UnmarshalJSONEnum(x.Descriptor(), b)
	if err != nil {
		return err
	}
	*x = EItem(num)
	return nil
}

// Deprecated: Use EItem.Descriptor instead.
func (EItem) EnumDescriptor() ([]byte, []int) {
	return file_gameconfig_proto_rawDescGZIP(), []int{5}
}

type EResourceAddType int32

const (
	EResourceAddType_EResourceAddType_None                  EResourceAddType = 0
	EResourceAddType_EResourceAddType_FinishMainTask        EResourceAddType = 1
	EResourceAddType_EResourceAddType_FinishMainTaskChapter EResourceAddType = 2
	EResourceAddType_EResourceAddType_SystemOpen            EResourceAddType = 3
	EResourceAddType_EResourceAddType_System                EResourceAddType = 4
	EResourceAddType_EResourceAddType_MapEventShop          EResourceAddType = 5
	EResourceAddType_EResourceAddType_SystemMail            EResourceAddType = 6
	EResourceAddType_EResourceAddType_NewPlayerMail         EResourceAddType = 7
	EResourceAddType_EResourceAddType_WeeklyRankAward       EResourceAddType = 8
	EResourceAddType_EResourceAddType_ConstShop             EResourceAddType = 9
	EResourceAddType_EResourceAddType_GM                    EResourceAddType = 12
	EResourceAddType_EResourceAddType_TreasureBox           EResourceAddType = 13
)

// Enum value maps for EResourceAddType.
var (
	EResourceAddType_name = map[int32]string{
		0:  "EResourceAddType_None",
		1:  "EResourceAddType_FinishMainTask",
		2:  "EResourceAddType_FinishMainTaskChapter",
		3:  "EResourceAddType_SystemOpen",
		4:  "EResourceAddType_System",
		5:  "EResourceAddType_MapEventShop",
		6:  "EResourceAddType_SystemMail",
		7:  "EResourceAddType_NewPlayerMail",
		8:  "EResourceAddType_WeeklyRankAward",
		9:  "EResourceAddType_ConstShop",
		12: "EResourceAddType_GM",
		13: "EResourceAddType_TreasureBox",
	}
	EResourceAddType_value = map[string]int32{
		"EResourceAddType_None":                  0,
		"EResourceAddType_FinishMainTask":        1,
		"EResourceAddType_FinishMainTaskChapter": 2,
		"EResourceAddType_SystemOpen":            3,
		"EResourceAddType_System":                4,
		"EResourceAddType_MapEventShop":          5,
		"EResourceAddType_SystemMail":            6,
		"EResourceAddType_NewPlayerMail":         7,
		"EResourceAddType_WeeklyRankAward":       8,
		"EResourceAddType_ConstShop":             9,
		"EResourceAddType_GM":                    12,
		"EResourceAddType_TreasureBox":           13,
	}
)

func (x EResourceAddType) Enum() *EResourceAddType {
	p := new(EResourceAddType)
	*p = x
	return p
}

func (x EResourceAddType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (EResourceAddType) Descriptor() protoreflect.EnumDescriptor {
	return file_gameconfig_proto_enumTypes[6].Descriptor()
}

func (EResourceAddType) Type() protoreflect.EnumType {
	return &file_gameconfig_proto_enumTypes[6]
}

func (x EResourceAddType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Do not use.
func (x *EResourceAddType) UnmarshalJSON(b []byte) error {
	num, err := protoimpl.X.UnmarshalJSONEnum(x.Descriptor(), b)
	if err != nil {
		return err
	}
	*x = EResourceAddType(num)
	return nil
}

// Deprecated: Use EResourceAddType.Descriptor instead.
func (EResourceAddType) EnumDescriptor() ([]byte, []int) {
	return file_gameconfig_proto_rawDescGZIP(), []int{6}
}

type EResourceSubType int32

const (
	EResourceSubType_EResourceSubType_None         EResourceSubType = 0
	EResourceSubType_EResourceSubType_HeroUpgrade  EResourceSubType = 1
	EResourceSubType_EResourceSubType_HeroStar     EResourceSubType = 2
	EResourceSubType_EResourceSubType_MapEventShop EResourceSubType = 3
	EResourceSubType_EResourceSubType_ConstShop    EResourceSubType = 4
)

// Enum value maps for EResourceSubType.
var (
	EResourceSubType_name = map[int32]string{
		0: "EResourceSubType_None",
		1: "EResourceSubType_HeroUpgrade",
		2: "EResourceSubType_HeroStar",
		3: "EResourceSubType_MapEventShop",
		4: "EResourceSubType_ConstShop",
	}
	EResourceSubType_value = map[string]int32{
		"EResourceSubType_None":         0,
		"EResourceSubType_HeroUpgrade":  1,
		"EResourceSubType_HeroStar":     2,
		"EResourceSubType_MapEventShop": 3,
		"EResourceSubType_ConstShop":    4,
	}
)

func (x EResourceSubType) Enum() *EResourceSubType {
	p := new(EResourceSubType)
	*p = x
	return p
}

func (x EResourceSubType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (EResourceSubType) Descriptor() protoreflect.EnumDescriptor {
	return file_gameconfig_proto_enumTypes[7].Descriptor()
}

func (EResourceSubType) Type() protoreflect.EnumType {
	return &file_gameconfig_proto_enumTypes[7]
}

func (x EResourceSubType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Do not use.
func (x *EResourceSubType) UnmarshalJSON(b []byte) error {
	num, err := protoimpl.X.UnmarshalJSONEnum(x.Descriptor(), b)
	if err != nil {
		return err
	}
	*x = EResourceSubType(num)
	return nil
}

// Deprecated: Use EResourceSubType.Descriptor instead.
func (EResourceSubType) EnumDescriptor() ([]byte, []int) {
	return file_gameconfig_proto_rawDescGZIP(), []int{7}
}

type EConditionType int32

const (
	EConditionType_EConditionType_None              EConditionType = 0
	EConditionType_EConditionType_Level             EConditionType = 1
	EConditionType_EConditionType_SystemOpen        EConditionType = 2
	EConditionType_EConditionType_GainItem          EConditionType = 3
	EConditionType_EConditionType_FinishTask        EConditionType = 4
	EConditionType_EConditionType_CostItem          EConditionType = 5
	EConditionType_EConditionType_GainNewItem       EConditionType = 6
	EConditionType_EConditionType_HeroUpgradeCount  EConditionType = 7
	EConditionType_EConditionType_MainCityShopEvent EConditionType = 8
	EConditionType_EConditionType_HourOfDay12       EConditionType = 9
	EConditionType_EConditionType_HourOfDay24       EConditionType = 10
	EConditionType_EConditionType_WeeklyRankOpen    EConditionType = 11
	EConditionType_EConditionType_TagIdsTotal       EConditionType = 12
	EConditionType_EConditionType_TagIds            EConditionType = 13
	EConditionType_EConditionType_CreateRoleDay     EConditionType = 14
	EConditionType_EConditionType_TriggerGuideGroup EConditionType = 15
	EConditionType_EConditionType_DoneGuideGroup    EConditionType = 16
	EConditionType_EConditionType_TotalPower        EConditionType = 17
	EConditionType_EConditionType_HeroPower         EConditionType = 18
)

// Enum value maps for EConditionType.
var (
	EConditionType_name = map[int32]string{
		0:  "EConditionType_None",
		1:  "EConditionType_Level",
		2:  "EConditionType_SystemOpen",
		3:  "EConditionType_GainItem",
		4:  "EConditionType_FinishTask",
		5:  "EConditionType_CostItem",
		6:  "EConditionType_GainNewItem",
		7:  "EConditionType_HeroUpgradeCount",
		8:  "EConditionType_MainCityShopEvent",
		9:  "EConditionType_HourOfDay12",
		10: "EConditionType_HourOfDay24",
		11: "EConditionType_WeeklyRankOpen",
		12: "EConditionType_TagIdsTotal",
		13: "EConditionType_TagIds",
		14: "EConditionType_CreateRoleDay",
		15: "EConditionType_TriggerGuideGroup",
		16: "EConditionType_DoneGuideGroup",
		17: "EConditionType_TotalPower",
		18: "EConditionType_HeroPower",
	}
	EConditionType_value = map[string]int32{
		"EConditionType_None":              0,
		"EConditionType_Level":             1,
		"EConditionType_SystemOpen":        2,
		"EConditionType_GainItem":          3,
		"EConditionType_FinishTask":        4,
		"EConditionType_CostItem":          5,
		"EConditionType_GainNewItem":       6,
		"EConditionType_HeroUpgradeCount":  7,
		"EConditionType_MainCityShopEvent": 8,
		"EConditionType_HourOfDay12":       9,
		"EConditionType_HourOfDay24":       10,
		"EConditionType_WeeklyRankOpen":    11,
		"EConditionType_TagIdsTotal":       12,
		"EConditionType_TagIds":            13,
		"EConditionType_CreateRoleDay":     14,
		"EConditionType_TriggerGuideGroup": 15,
		"EConditionType_DoneGuideGroup":    16,
		"EConditionType_TotalPower":        17,
		"EConditionType_HeroPower":         18,
	}
)

func (x EConditionType) Enum() *EConditionType {
	p := new(EConditionType)
	*p = x
	return p
}

func (x EConditionType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (EConditionType) Descriptor() protoreflect.EnumDescriptor {
	return file_gameconfig_proto_enumTypes[8].Descriptor()
}

func (EConditionType) Type() protoreflect.EnumType {
	return &file_gameconfig_proto_enumTypes[8]
}

func (x EConditionType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Do not use.
func (x *EConditionType) UnmarshalJSON(b []byte) error {
	num, err := protoimpl.X.UnmarshalJSONEnum(x.Descriptor(), b)
	if err != nil {
		return err
	}
	*x = EConditionType(num)
	return nil
}

// Deprecated: Use EConditionType.Descriptor instead.
func (EConditionType) EnumDescriptor() ([]byte, []int) {
	return file_gameconfig_proto_rawDescGZIP(), []int{8}
}

type ETaskStatus int32

const (
	ETaskStatus_ETaskStatus_None        ETaskStatus = 0
	ETaskStatus_ETaskStatus_NotComplete ETaskStatus = 1
	ETaskStatus_ETaskStatus_Complete    ETaskStatus = 2
)

// Enum value maps for ETaskStatus.
var (
	ETaskStatus_name = map[int32]string{
		0: "ETaskStatus_None",
		1: "ETaskStatus_NotComplete",
		2: "ETaskStatus_Complete",
	}
	ETaskStatus_value = map[string]int32{
		"ETaskStatus_None":        0,
		"ETaskStatus_NotComplete": 1,
		"ETaskStatus_Complete":    2,
	}
)

func (x ETaskStatus) Enum() *ETaskStatus {
	p := new(ETaskStatus)
	*p = x
	return p
}

func (x ETaskStatus) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ETaskStatus) Descriptor() protoreflect.EnumDescriptor {
	return file_gameconfig_proto_enumTypes[9].Descriptor()
}

func (ETaskStatus) Type() protoreflect.EnumType {
	return &file_gameconfig_proto_enumTypes[9]
}

func (x ETaskStatus) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Do not use.
func (x *ETaskStatus) UnmarshalJSON(b []byte) error {
	num, err := protoimpl.X.UnmarshalJSONEnum(x.Descriptor(), b)
	if err != nil {
		return err
	}
	*x = ETaskStatus(num)
	return nil
}

// Deprecated: Use ETaskStatus.Descriptor instead.
func (ETaskStatus) EnumDescriptor() ([]byte, []int) {
	return file_gameconfig_proto_rawDescGZIP(), []int{9}
}

type EQuality int32

const (
	EQuality_EQuality_None   EQuality = 0
	EQuality_EQuality_White  EQuality = 1
	EQuality_EQuality_Green  EQuality = 2
	EQuality_EQuality_Blue   EQuality = 3
	EQuality_EQuality_Purple EQuality = 4
	EQuality_EQuality_Orange EQuality = 5
	EQuality_EQuality_Red    EQuality = 6
	EQuality_EQuality_Pink   EQuality = 7
)

// Enum value maps for EQuality.
var (
	EQuality_name = map[int32]string{
		0: "EQuality_None",
		1: "EQuality_White",
		2: "EQuality_Green",
		3: "EQuality_Blue",
		4: "EQuality_Purple",
		5: "EQuality_Orange",
		6: "EQuality_Red",
		7: "EQuality_Pink",
	}
	EQuality_value = map[string]int32{
		"EQuality_None":   0,
		"EQuality_White":  1,
		"EQuality_Green":  2,
		"EQuality_Blue":   3,
		"EQuality_Purple": 4,
		"EQuality_Orange": 5,
		"EQuality_Red":    6,
		"EQuality_Pink":   7,
	}
)

func (x EQuality) Enum() *EQuality {
	p := new(EQuality)
	*p = x
	return p
}

func (x EQuality) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (EQuality) Descriptor() protoreflect.EnumDescriptor {
	return file_gameconfig_proto_enumTypes[10].Descriptor()
}

func (EQuality) Type() protoreflect.EnumType {
	return &file_gameconfig_proto_enumTypes[10]
}

func (x EQuality) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Do not use.
func (x *EQuality) UnmarshalJSON(b []byte) error {
	num, err := protoimpl.X.UnmarshalJSONEnum(x.Descriptor(), b)
	if err != nil {
		return err
	}
	*x = EQuality(num)
	return nil
}

// Deprecated: Use EQuality.Descriptor instead.
func (EQuality) EnumDescriptor() ([]byte, []int) {
	return file_gameconfig_proto_rawDescGZIP(), []int{10}
}

type EHeroGain int32

const (
	EHeroGain_EHeroGain_None       EHeroGain = 0
	EHeroGain_EHeroGain_SysGive    EHeroGain = 1
	EHeroGain_EHeroGain_HeroCard   EHeroGain = 2
	EHeroGain_EHeroGain_ChipsMerge EHeroGain = 3
	EHeroGain_EHeroGain_GM         EHeroGain = 4
	EHeroGain_EHeroGain_Recruit    EHeroGain = 5
)

// Enum value maps for EHeroGain.
var (
	EHeroGain_name = map[int32]string{
		0: "EHeroGain_None",
		1: "EHeroGain_SysGive",
		2: "EHeroGain_HeroCard",
		3: "EHeroGain_ChipsMerge",
		4: "EHeroGain_GM",
		5: "EHeroGain_Recruit",
	}
	EHeroGain_value = map[string]int32{
		"EHeroGain_None":       0,
		"EHeroGain_SysGive":    1,
		"EHeroGain_HeroCard":   2,
		"EHeroGain_ChipsMerge": 3,
		"EHeroGain_GM":         4,
		"EHeroGain_Recruit":    5,
	}
)

func (x EHeroGain) Enum() *EHeroGain {
	p := new(EHeroGain)
	*p = x
	return p
}

func (x EHeroGain) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (EHeroGain) Descriptor() protoreflect.EnumDescriptor {
	return file_gameconfig_proto_enumTypes[11].Descriptor()
}

func (EHeroGain) Type() protoreflect.EnumType {
	return &file_gameconfig_proto_enumTypes[11]
}

func (x EHeroGain) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Do not use.
func (x *EHeroGain) UnmarshalJSON(b []byte) error {
	num, err := protoimpl.X.UnmarshalJSONEnum(x.Descriptor(), b)
	if err != nil {
		return err
	}
	*x = EHeroGain(num)
	return nil
}

// Deprecated: Use EHeroGain.Descriptor instead.
func (EHeroGain) EnumDescriptor() ([]byte, []int) {
	return file_gameconfig_proto_rawDescGZIP(), []int{11}
}

type EHeroAttr int32

const (
	EHeroAttr_EHeroAttr_Invalid                     EHeroAttr = 0
	EHeroAttr_EHeroAttr_Power                       EHeroAttr = 1
	EHeroAttr_EHeroAttr_HumanTimeMS                 EHeroAttr = 2
	EHeroAttr_EHeroAttr_HumanTime                   EHeroAttr = 3
	EHeroAttr_EHeroAttr_TotalPurification           EHeroAttr = 4
	EHeroAttr_EHeroAttr_BasePurification            EHeroAttr = 5
	EHeroAttr_EHeroAttr_PurificationAddition        EHeroAttr = 6
	EHeroAttr_EHeroAttr_PurificationFix             EHeroAttr = 7
	EHeroAttr_EHeroAttr_Mind                        EHeroAttr = 8
	EHeroAttr_EHeroAttr_Charm                       EHeroAttr = 9
	EHeroAttr_EHeroAttr_Body                        EHeroAttr = 10
	EHeroAttr_EHeroAttr_Vigor                       EHeroAttr = 11
	EHeroAttr_EHeroAttr_Knowledge                   EHeroAttr = 12
	EHeroAttr_EHeroAttr_CriticalRate                EHeroAttr = 13
	EHeroAttr_EHeroAttr_CriticalDamage              EHeroAttr = 14
	EHeroAttr_EHeroAttr_ChastityAddition            EHeroAttr = 15
	EHeroAttr_EHeroAttr_TemperanceAddition          EHeroAttr = 16
	EHeroAttr_EHeroAttr_CharityAddition             EHeroAttr = 17
	EHeroAttr_EHeroAttr_HopeAddition                EHeroAttr = 18
	EHeroAttr_EHeroAttr_FortitudeAddition           EHeroAttr = 19
	EHeroAttr_EHeroAttr_KindnessAddition            EHeroAttr = 20
	EHeroAttr_EHeroAttr_JusticeAddition             EHeroAttr = 21
	EHeroAttr_EHeroAttr_SkillFactor                 EHeroAttr = 22
	EHeroAttr_EHeroAttr_DamageAddition              EHeroAttr = 23
	EHeroAttr_EHeroAttr_DamageReduction             EHeroAttr = 24
	EHeroAttr_EHeroAttr_RestrainAddition            EHeroAttr = 25
	EHeroAttr_EHeroAttr_ChastityInjured             EHeroAttr = 26
	EHeroAttr_EHeroAttr_TemperanceInjured           EHeroAttr = 27
	EHeroAttr_EHeroAttr_CharityInjured              EHeroAttr = 28
	EHeroAttr_EHeroAttr_HopeInjured                 EHeroAttr = 29
	EHeroAttr_EHeroAttr_FortitudeInjured            EHeroAttr = 30
	EHeroAttr_EHeroAttr_KindnessInjured             EHeroAttr = 31
	EHeroAttr_EHeroAttr_JusticeInjured              EHeroAttr = 32
	EHeroAttr_EHeroAttr_ChastityReduction           EHeroAttr = 33
	EHeroAttr_EHeroAttr_TemperanceReduction         EHeroAttr = 34
	EHeroAttr_EHeroAttr_CharityReduction            EHeroAttr = 35
	EHeroAttr_EHeroAttr_HopeReduction               EHeroAttr = 36
	EHeroAttr_EHeroAttr_FortitudeReduction          EHeroAttr = 37
	EHeroAttr_EHeroAttr_KindnessReduction           EHeroAttr = 38
	EHeroAttr_EHeroAttr_JusticeReduction            EHeroAttr = 39
	EHeroAttr_EHeroAttr_StunRate                    EHeroAttr = 40
	EHeroAttr_EHeroAttr_MultiHitRate                EHeroAttr = 41
	EHeroAttr_EHeroAttr_SkillPowerFactor            EHeroAttr = 42
	EHeroAttr_EHeroAttr_GrownAttrPurificationFactor EHeroAttr = 43
)

// Enum value maps for EHeroAttr.
var (
	EHeroAttr_name = map[int32]string{
		0:  "EHeroAttr_Invalid",
		1:  "EHeroAttr_Power",
		2:  "EHeroAttr_HumanTimeMS",
		3:  "EHeroAttr_HumanTime",
		4:  "EHeroAttr_TotalPurification",
		5:  "EHeroAttr_BasePurification",
		6:  "EHeroAttr_PurificationAddition",
		7:  "EHeroAttr_PurificationFix",
		8:  "EHeroAttr_Mind",
		9:  "EHeroAttr_Charm",
		10: "EHeroAttr_Body",
		11: "EHeroAttr_Vigor",
		12: "EHeroAttr_Knowledge",
		13: "EHeroAttr_CriticalRate",
		14: "EHeroAttr_CriticalDamage",
		15: "EHeroAttr_ChastityAddition",
		16: "EHeroAttr_TemperanceAddition",
		17: "EHeroAttr_CharityAddition",
		18: "EHeroAttr_HopeAddition",
		19: "EHeroAttr_FortitudeAddition",
		20: "EHeroAttr_KindnessAddition",
		21: "EHeroAttr_JusticeAddition",
		22: "EHeroAttr_SkillFactor",
		23: "EHeroAttr_DamageAddition",
		24: "EHeroAttr_DamageReduction",
		25: "EHeroAttr_RestrainAddition",
		26: "EHeroAttr_ChastityInjured",
		27: "EHeroAttr_TemperanceInjured",
		28: "EHeroAttr_CharityInjured",
		29: "EHeroAttr_HopeInjured",
		30: "EHeroAttr_FortitudeInjured",
		31: "EHeroAttr_KindnessInjured",
		32: "EHeroAttr_JusticeInjured",
		33: "EHeroAttr_ChastityReduction",
		34: "EHeroAttr_TemperanceReduction",
		35: "EHeroAttr_CharityReduction",
		36: "EHeroAttr_HopeReduction",
		37: "EHeroAttr_FortitudeReduction",
		38: "EHeroAttr_KindnessReduction",
		39: "EHeroAttr_JusticeReduction",
		40: "EHeroAttr_StunRate",
		41: "EHeroAttr_MultiHitRate",
		42: "EHeroAttr_SkillPowerFactor",
		43: "EHeroAttr_GrownAttrPurificationFactor",
	}
	EHeroAttr_value = map[string]int32{
		"EHeroAttr_Invalid":                     0,
		"EHeroAttr_Power":                       1,
		"EHeroAttr_HumanTimeMS":                 2,
		"EHeroAttr_HumanTime":                   3,
		"EHeroAttr_TotalPurification":           4,
		"EHeroAttr_BasePurification":            5,
		"EHeroAttr_PurificationAddition":        6,
		"EHeroAttr_PurificationFix":             7,
		"EHeroAttr_Mind":                        8,
		"EHeroAttr_Charm":                       9,
		"EHeroAttr_Body":                        10,
		"EHeroAttr_Vigor":                       11,
		"EHeroAttr_Knowledge":                   12,
		"EHeroAttr_CriticalRate":                13,
		"EHeroAttr_CriticalDamage":              14,
		"EHeroAttr_ChastityAddition":            15,
		"EHeroAttr_TemperanceAddition":          16,
		"EHeroAttr_CharityAddition":             17,
		"EHeroAttr_HopeAddition":                18,
		"EHeroAttr_FortitudeAddition":           19,
		"EHeroAttr_KindnessAddition":            20,
		"EHeroAttr_JusticeAddition":             21,
		"EHeroAttr_SkillFactor":                 22,
		"EHeroAttr_DamageAddition":              23,
		"EHeroAttr_DamageReduction":             24,
		"EHeroAttr_RestrainAddition":            25,
		"EHeroAttr_ChastityInjured":             26,
		"EHeroAttr_TemperanceInjured":           27,
		"EHeroAttr_CharityInjured":              28,
		"EHeroAttr_HopeInjured":                 29,
		"EHeroAttr_FortitudeInjured":            30,
		"EHeroAttr_KindnessInjured":             31,
		"EHeroAttr_JusticeInjured":              32,
		"EHeroAttr_ChastityReduction":           33,
		"EHeroAttr_TemperanceReduction":         34,
		"EHeroAttr_CharityReduction":            35,
		"EHeroAttr_HopeReduction":               36,
		"EHeroAttr_FortitudeReduction":          37,
		"EHeroAttr_KindnessReduction":           38,
		"EHeroAttr_JusticeReduction":            39,
		"EHeroAttr_StunRate":                    40,
		"EHeroAttr_MultiHitRate":                41,
		"EHeroAttr_SkillPowerFactor":            42,
		"EHeroAttr_GrownAttrPurificationFactor": 43,
	}
)

func (x EHeroAttr) Enum() *EHeroAttr {
	p := new(EHeroAttr)
	*p = x
	return p
}

func (x EHeroAttr) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (EHeroAttr) Descriptor() protoreflect.EnumDescriptor {
	return file_gameconfig_proto_enumTypes[12].Descriptor()
}

func (EHeroAttr) Type() protoreflect.EnumType {
	return &file_gameconfig_proto_enumTypes[12]
}

func (x EHeroAttr) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Do not use.
func (x *EHeroAttr) UnmarshalJSON(b []byte) error {
	num, err := protoimpl.X.UnmarshalJSONEnum(x.Descriptor(), b)
	if err != nil {
		return err
	}
	*x = EHeroAttr(num)
	return nil
}

// Deprecated: Use EHeroAttr.Descriptor instead.
func (EHeroAttr) EnumDescriptor() ([]byte, []int) {
	return file_gameconfig_proto_rawDescGZIP(), []int{12}
}

type EGainItemEffect int32

const (
	EGainItemEffect_EGainItemEffect_None         EGainItemEffect = 0
	EGainItemEffect_EGainItemEffect_Popup        EGainItemEffect = 1
	EGainItemEffect_EGainItemEffect_IconFly      EGainItemEffect = 2
	EGainItemEffect_EGainItemEffect_IconFlyMulti EGainItemEffect = 3
	EGainItemEffect_EGainItemEffect_IconFlyTouch EGainItemEffect = 4
)

// Enum value maps for EGainItemEffect.
var (
	EGainItemEffect_name = map[int32]string{
		0: "EGainItemEffect_None",
		1: "EGainItemEffect_Popup",
		2: "EGainItemEffect_IconFly",
		3: "EGainItemEffect_IconFlyMulti",
		4: "EGainItemEffect_IconFlyTouch",
	}
	EGainItemEffect_value = map[string]int32{
		"EGainItemEffect_None":         0,
		"EGainItemEffect_Popup":        1,
		"EGainItemEffect_IconFly":      2,
		"EGainItemEffect_IconFlyMulti": 3,
		"EGainItemEffect_IconFlyTouch": 4,
	}
)

func (x EGainItemEffect) Enum() *EGainItemEffect {
	p := new(EGainItemEffect)
	*p = x
	return p
}

func (x EGainItemEffect) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (EGainItemEffect) Descriptor() protoreflect.EnumDescriptor {
	return file_gameconfig_proto_enumTypes[13].Descriptor()
}

func (EGainItemEffect) Type() protoreflect.EnumType {
	return &file_gameconfig_proto_enumTypes[13]
}

func (x EGainItemEffect) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Do not use.
func (x *EGainItemEffect) UnmarshalJSON(b []byte) error {
	num, err := protoimpl.X.UnmarshalJSONEnum(x.Descriptor(), b)
	if err != nil {
		return err
	}
	*x = EGainItemEffect(num)
	return nil
}

// Deprecated: Use EGainItemEffect.Descriptor instead.
func (EGainItemEffect) EnumDescriptor() ([]byte, []int) {
	return file_gameconfig_proto_rawDescGZIP(), []int{13}
}

type ERankType int32

const (
	ERankType_ERankType_None            ERankType = 0
	ERankType_ERankType_CivilProsperity ERankType = 1
	ERankType_ERankType_LineUpPower     ERankType = 2
	ERankType_ERankType_TotalPower      ERankType = 3
	ERankType_ERankType_BossCopper      ERankType = 9981
	ERankType_ERankType_BossSilver      ERankType = 9982
	ERankType_ERankType_BossGold        ERankType = 9983
	ERankType_ERankType_MiniGameBegin   ERankType = 1000
)

// Enum value maps for ERankType.
var (
	ERankType_name = map[int32]string{
		0:    "ERankType_None",
		1:    "ERankType_CivilProsperity",
		2:    "ERankType_LineUpPower",
		3:    "ERankType_TotalPower",
		9981: "ERankType_BossCopper",
		9982: "ERankType_BossSilver",
		9983: "ERankType_BossGold",
		1000: "ERankType_MiniGameBegin",
	}
	ERankType_value = map[string]int32{
		"ERankType_None":            0,
		"ERankType_CivilProsperity": 1,
		"ERankType_LineUpPower":     2,
		"ERankType_TotalPower":      3,
		"ERankType_BossCopper":      9981,
		"ERankType_BossSilver":      9982,
		"ERankType_BossGold":        9983,
		"ERankType_MiniGameBegin":   1000,
	}
)

func (x ERankType) Enum() *ERankType {
	p := new(ERankType)
	*p = x
	return p
}

func (x ERankType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ERankType) Descriptor() protoreflect.EnumDescriptor {
	return file_gameconfig_proto_enumTypes[14].Descriptor()
}

func (ERankType) Type() protoreflect.EnumType {
	return &file_gameconfig_proto_enumTypes[14]
}

func (x ERankType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Do not use.
func (x *ERankType) UnmarshalJSON(b []byte) error {
	num, err := protoimpl.X.UnmarshalJSONEnum(x.Descriptor(), b)
	if err != nil {
		return err
	}
	*x = ERankType(num)
	return nil
}

// Deprecated: Use ERankType.Descriptor instead.
func (ERankType) EnumDescriptor() ([]byte, []int) {
	return file_gameconfig_proto_rawDescGZIP(), []int{14}
}

type EGoto int32

const (
	EGoto_EGoto_Invalid        EGoto = 0
	EGoto_EGoto_UI             EGoto = 1
	EGoto_EGoto_Scene          EGoto = 2
	EGoto_EGoto_MainBtn        EGoto = 3
	EGoto_EGoto_Web            EGoto = 4
	EGoto_EGoto_TextDes        EGoto = 5
	EGoto_EGoto_MainPlayerView EGoto = 6
	EGoto_EGoto_GuideEffect    EGoto = 7
	EGoto_EGoto_DelayOpenUI    EGoto = 8
)

// Enum value maps for EGoto.
var (
	EGoto_name = map[int32]string{
		0: "EGoto_Invalid",
		1: "EGoto_UI",
		2: "EGoto_Scene",
		3: "EGoto_MainBtn",
		4: "EGoto_Web",
		5: "EGoto_TextDes",
		6: "EGoto_MainPlayerView",
		7: "EGoto_GuideEffect",
		8: "EGoto_DelayOpenUI",
	}
	EGoto_value = map[string]int32{
		"EGoto_Invalid":        0,
		"EGoto_UI":             1,
		"EGoto_Scene":          2,
		"EGoto_MainBtn":        3,
		"EGoto_Web":            4,
		"EGoto_TextDes":        5,
		"EGoto_MainPlayerView": 6,
		"EGoto_GuideEffect":    7,
		"EGoto_DelayOpenUI":    8,
	}
)

func (x EGoto) Enum() *EGoto {
	p := new(EGoto)
	*p = x
	return p
}

func (x EGoto) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (EGoto) Descriptor() protoreflect.EnumDescriptor {
	return file_gameconfig_proto_enumTypes[15].Descriptor()
}

func (EGoto) Type() protoreflect.EnumType {
	return &file_gameconfig_proto_enumTypes[15]
}

func (x EGoto) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Do not use.
func (x *EGoto) UnmarshalJSON(b []byte) error {
	num, err := protoimpl.X.UnmarshalJSONEnum(x.Descriptor(), b)
	if err != nil {
		return err
	}
	*x = EGoto(num)
	return nil
}

// Deprecated: Use EGoto.Descriptor instead.
func (EGoto) EnumDescriptor() ([]byte, []int) {
	return file_gameconfig_proto_rawDescGZIP(), []int{15}
}

type EMailStatus int32

const (
	EMailStatus_EMailStatus_None   EMailStatus = 0
	EMailStatus_EMailStatus_Unread EMailStatus = 1
	EMailStatus_EMailStatus_Read   EMailStatus = 2
	EMailStatus_EMailStatus_Gain   EMailStatus = 3
)

// Enum value maps for EMailStatus.
var (
	EMailStatus_name = map[int32]string{
		0: "EMailStatus_None",
		1: "EMailStatus_Unread",
		2: "EMailStatus_Read",
		3: "EMailStatus_Gain",
	}
	EMailStatus_value = map[string]int32{
		"EMailStatus_None":   0,
		"EMailStatus_Unread": 1,
		"EMailStatus_Read":   2,
		"EMailStatus_Gain":   3,
	}
)

func (x EMailStatus) Enum() *EMailStatus {
	p := new(EMailStatus)
	*p = x
	return p
}

func (x EMailStatus) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (EMailStatus) Descriptor() protoreflect.EnumDescriptor {
	return file_gameconfig_proto_enumTypes[16].Descriptor()
}

func (EMailStatus) Type() protoreflect.EnumType {
	return &file_gameconfig_proto_enumTypes[16]
}

func (x EMailStatus) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Do not use.
func (x *EMailStatus) UnmarshalJSON(b []byte) error {
	num, err := protoimpl.X.UnmarshalJSONEnum(x.Descriptor(), b)
	if err != nil {
		return err
	}
	*x = EMailStatus(num)
	return nil
}

// Deprecated: Use EMailStatus.Descriptor instead.
func (EMailStatus) EnumDescriptor() ([]byte, []int) {
	return file_gameconfig_proto_rawDescGZIP(), []int{16}
}

type EAINodeType int32

const (
	EAINodeType_EAINodeType_None    EAINodeType = 0
	EAINodeType_EAINodeType_Execute EAINodeType = 1
	EAINodeType_EAINodeType_Loop    EAINodeType = 2
	EAINodeType_EAINodeType_Select  EAINodeType = 3
)

// Enum value maps for EAINodeType.
var (
	EAINodeType_name = map[int32]string{
		0: "EAINodeType_None",
		1: "EAINodeType_Execute",
		2: "EAINodeType_Loop",
		3: "EAINodeType_Select",
	}
	EAINodeType_value = map[string]int32{
		"EAINodeType_None":    0,
		"EAINodeType_Execute": 1,
		"EAINodeType_Loop":    2,
		"EAINodeType_Select":  3,
	}
)

func (x EAINodeType) Enum() *EAINodeType {
	p := new(EAINodeType)
	*p = x
	return p
}

func (x EAINodeType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (EAINodeType) Descriptor() protoreflect.EnumDescriptor {
	return file_gameconfig_proto_enumTypes[17].Descriptor()
}

func (EAINodeType) Type() protoreflect.EnumType {
	return &file_gameconfig_proto_enumTypes[17]
}

func (x EAINodeType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Do not use.
func (x *EAINodeType) UnmarshalJSON(b []byte) error {
	num, err := protoimpl.X.UnmarshalJSONEnum(x.Descriptor(), b)
	if err != nil {
		return err
	}
	*x = EAINodeType(num)
	return nil
}

// Deprecated: Use EAINodeType.Descriptor instead.
func (EAINodeType) EnumDescriptor() ([]byte, []int) {
	return file_gameconfig_proto_rawDescGZIP(), []int{17}
}

type EAIConditionType int32

const (
	EAIConditionType_EAIConditionType_None            EAIConditionType = 0
	EAIConditionType_EAIConditionType_And             EAIConditionType = 1
	EAIConditionType_EAIConditionType_Or              EAIConditionType = 2
	EAIConditionType_EAIConditionType_CheckCurAITime  EAIConditionType = 3
	EAIConditionType_EAIConditionType_CheckVariable   EAIConditionType = 4
	EAIConditionType_EAIConditionType_CheckLastAITime EAIConditionType = 5
	EAIConditionType_EAIConditionType_CheckNextAITime EAIConditionType = 6
	EAIConditionType_EAIConditionType_CheckBuildType  EAIConditionType = 7
)

// Enum value maps for EAIConditionType.
var (
	EAIConditionType_name = map[int32]string{
		0: "EAIConditionType_None",
		1: "EAIConditionType_And",
		2: "EAIConditionType_Or",
		3: "EAIConditionType_CheckCurAITime",
		4: "EAIConditionType_CheckVariable",
		5: "EAIConditionType_CheckLastAITime",
		6: "EAIConditionType_CheckNextAITime",
		7: "EAIConditionType_CheckBuildType",
	}
	EAIConditionType_value = map[string]int32{
		"EAIConditionType_None":            0,
		"EAIConditionType_And":             1,
		"EAIConditionType_Or":              2,
		"EAIConditionType_CheckCurAITime":  3,
		"EAIConditionType_CheckVariable":   4,
		"EAIConditionType_CheckLastAITime": 5,
		"EAIConditionType_CheckNextAITime": 6,
		"EAIConditionType_CheckBuildType":  7,
	}
)

func (x EAIConditionType) Enum() *EAIConditionType {
	p := new(EAIConditionType)
	*p = x
	return p
}

func (x EAIConditionType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (EAIConditionType) Descriptor() protoreflect.EnumDescriptor {
	return file_gameconfig_proto_enumTypes[18].Descriptor()
}

func (EAIConditionType) Type() protoreflect.EnumType {
	return &file_gameconfig_proto_enumTypes[18]
}

func (x EAIConditionType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Do not use.
func (x *EAIConditionType) UnmarshalJSON(b []byte) error {
	num, err := protoimpl.X.UnmarshalJSONEnum(x.Descriptor(), b)
	if err != nil {
		return err
	}
	*x = EAIConditionType(num)
	return nil
}

// Deprecated: Use EAIConditionType.Descriptor instead.
func (EAIConditionType) EnumDescriptor() ([]byte, []int) {
	return file_gameconfig_proto_rawDescGZIP(), []int{18}
}

type EAIActionType int32

const (
	EAIActionType_EAIActionType_None        EAIActionType = 0
	EAIActionType_EAIActionType_Delay       EAIActionType = 1
	EAIActionType_EAIActionType_SetVariable EAIActionType = 2
	EAIActionType_EAIActionType_ExecuteNode EAIActionType = 3
	EAIActionType_EAIActionType_Finish      EAIActionType = 4
)

// Enum value maps for EAIActionType.
var (
	EAIActionType_name = map[int32]string{
		0: "EAIActionType_None",
		1: "EAIActionType_Delay",
		2: "EAIActionType_SetVariable",
		3: "EAIActionType_ExecuteNode",
		4: "EAIActionType_Finish",
	}
	EAIActionType_value = map[string]int32{
		"EAIActionType_None":        0,
		"EAIActionType_Delay":       1,
		"EAIActionType_SetVariable": 2,
		"EAIActionType_ExecuteNode": 3,
		"EAIActionType_Finish":      4,
	}
)

func (x EAIActionType) Enum() *EAIActionType {
	p := new(EAIActionType)
	*p = x
	return p
}

func (x EAIActionType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (EAIActionType) Descriptor() protoreflect.EnumDescriptor {
	return file_gameconfig_proto_enumTypes[19].Descriptor()
}

func (EAIActionType) Type() protoreflect.EnumType {
	return &file_gameconfig_proto_enumTypes[19]
}

func (x EAIActionType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Do not use.
func (x *EAIActionType) UnmarshalJSON(b []byte) error {
	num, err := protoimpl.X.UnmarshalJSONEnum(x.Descriptor(), b)
	if err != nil {
		return err
	}
	*x = EAIActionType(num)
	return nil
}

// Deprecated: Use EAIActionType.Descriptor instead.
func (EAIActionType) EnumDescriptor() ([]byte, []int) {
	return file_gameconfig_proto_rawDescGZIP(), []int{19}
}

type EAwardType int32

const (
	EAwardType_EAwardType_None   EAwardType = 0
	EAwardType_EAwardType_Fixed  EAwardType = 1
	EAwardType_EAwardType_Weight EAwardType = 2
	EAwardType_EAwardType_Random EAwardType = 3
)

// Enum value maps for EAwardType.
var (
	EAwardType_name = map[int32]string{
		0: "EAwardType_None",
		1: "EAwardType_Fixed",
		2: "EAwardType_Weight",
		3: "EAwardType_Random",
	}
	EAwardType_value = map[string]int32{
		"EAwardType_None":   0,
		"EAwardType_Fixed":  1,
		"EAwardType_Weight": 2,
		"EAwardType_Random": 3,
	}
)

func (x EAwardType) Enum() *EAwardType {
	p := new(EAwardType)
	*p = x
	return p
}

func (x EAwardType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (EAwardType) Descriptor() protoreflect.EnumDescriptor {
	return file_gameconfig_proto_enumTypes[20].Descriptor()
}

func (EAwardType) Type() protoreflect.EnumType {
	return &file_gameconfig_proto_enumTypes[20]
}

func (x EAwardType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Do not use.
func (x *EAwardType) UnmarshalJSON(b []byte) error {
	num, err := protoimpl.X.UnmarshalJSONEnum(x.Descriptor(), b)
	if err != nil {
		return err
	}
	*x = EAwardType(num)
	return nil
}

// Deprecated: Use EAwardType.Descriptor instead.
func (EAwardType) EnumDescriptor() ([]byte, []int) {
	return file_gameconfig_proto_rawDescGZIP(), []int{20}
}

type EAwardGroupType int32

const (
	EAwardGroupType_EAwardGroupType_None   EAwardGroupType = 0
	EAwardGroupType_EAwardGroupType_Random EAwardGroupType = 1
	EAwardGroupType_EAwardGroupType_Weight EAwardGroupType = 2
)

// Enum value maps for EAwardGroupType.
var (
	EAwardGroupType_name = map[int32]string{
		0: "EAwardGroupType_None",
		1: "EAwardGroupType_Random",
		2: "EAwardGroupType_Weight",
	}
	EAwardGroupType_value = map[string]int32{
		"EAwardGroupType_None":   0,
		"EAwardGroupType_Random": 1,
		"EAwardGroupType_Weight": 2,
	}
)

func (x EAwardGroupType) Enum() *EAwardGroupType {
	p := new(EAwardGroupType)
	*p = x
	return p
}

func (x EAwardGroupType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (EAwardGroupType) Descriptor() protoreflect.EnumDescriptor {
	return file_gameconfig_proto_enumTypes[21].Descriptor()
}

func (EAwardGroupType) Type() protoreflect.EnumType {
	return &file_gameconfig_proto_enumTypes[21]
}

func (x EAwardGroupType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Do not use.
func (x *EAwardGroupType) UnmarshalJSON(b []byte) error {
	num, err := protoimpl.X.UnmarshalJSONEnum(x.Descriptor(), b)
	if err != nil {
		return err
	}
	*x = EAwardGroupType(num)
	return nil
}

// Deprecated: Use EAwardGroupType.Descriptor instead.
func (EAwardGroupType) EnumDescriptor() ([]byte, []int) {
	return file_gameconfig_proto_rawDescGZIP(), []int{21}
}

type EStoryPlotConditionType int32

const (
	EStoryPlotConditionType_EStoryPlotConditionType_None      EStoryPlotConditionType = 0
	EStoryPlotConditionType_EStoryPlotConditionType_EventDone EStoryPlotConditionType = 1
)

// Enum value maps for EStoryPlotConditionType.
var (
	EStoryPlotConditionType_name = map[int32]string{
		0: "EStoryPlotConditionType_None",
		1: "EStoryPlotConditionType_EventDone",
	}
	EStoryPlotConditionType_value = map[string]int32{
		"EStoryPlotConditionType_None":      0,
		"EStoryPlotConditionType_EventDone": 1,
	}
)

func (x EStoryPlotConditionType) Enum() *EStoryPlotConditionType {
	p := new(EStoryPlotConditionType)
	*p = x
	return p
}

func (x EStoryPlotConditionType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (EStoryPlotConditionType) Descriptor() protoreflect.EnumDescriptor {
	return file_gameconfig_proto_enumTypes[22].Descriptor()
}

func (EStoryPlotConditionType) Type() protoreflect.EnumType {
	return &file_gameconfig_proto_enumTypes[22]
}

func (x EStoryPlotConditionType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Do not use.
func (x *EStoryPlotConditionType) UnmarshalJSON(b []byte) error {
	num, err := protoimpl.X.UnmarshalJSONEnum(x.Descriptor(), b)
	if err != nil {
		return err
	}
	*x = EStoryPlotConditionType(num)
	return nil
}

// Deprecated: Use EStoryPlotConditionType.Descriptor instead.
func (EStoryPlotConditionType) EnumDescriptor() ([]byte, []int) {
	return file_gameconfig_proto_rawDescGZIP(), []int{22}
}

type EStoryPlotInstructType int32

const (
	EStoryPlotInstructType_EStoryPlotInstructType_None                   EStoryPlotInstructType = 0
	EStoryPlotInstructType_EStoryPlotInstructType_Wait                   EStoryPlotInstructType = 3
	EStoryPlotInstructType_EStoryPlotInstructType_AddNpc                 EStoryPlotInstructType = 4
	EStoryPlotInstructType_EStoryPlotInstructType_RemoveNpc              EStoryPlotInstructType = 5
	EStoryPlotInstructType_EStoryPlotInstructType_NpcMove                EStoryPlotInstructType = 6
	EStoryPlotInstructType_EStoryPlotInstructType_NpcBubble              EStoryPlotInstructType = 7
	EStoryPlotInstructType_EStoryPlotInstructType_NpcAction              EStoryPlotInstructType = 8
	EStoryPlotInstructType_EStoryPlotInstructType_NpcEvent               EStoryPlotInstructType = 9
	EStoryPlotInstructType_EStoryPlotInstructType_AddPlayer              EStoryPlotInstructType = 10
	EStoryPlotInstructType_EStoryPlotInstructType_PlayerMove             EStoryPlotInstructType = 11
	EStoryPlotInstructType_EStoryPlotInstructType_PlayerAction           EStoryPlotInstructType = 12
	EStoryPlotInstructType_EStoryPlotInstructType_PlayerBubble           EStoryPlotInstructType = 13
	EStoryPlotInstructType_EStoryPlotInstructType_FocusPlayer            EStoryPlotInstructType = 14
	EStoryPlotInstructType_EStoryPlotInstructType_FocusNpc               EStoryPlotInstructType = 15
	EStoryPlotInstructType_EStoryPlotInstructType_FocusPoint             EStoryPlotInstructType = 16
	EStoryPlotInstructType_EStoryPlotInstructType_ReleaseFocus           EStoryPlotInstructType = 17
	EStoryPlotInstructType_EStoryPlotInstructType_Finish                 EStoryPlotInstructType = 18
	EStoryPlotInstructType_EStoryPlotInstructType_HideUI                 EStoryPlotInstructType = 19
	EStoryPlotInstructType_EStoryPlotInstructType_ShowUI                 EStoryPlotInstructType = 20
	EStoryPlotInstructType_EStoryPlotInstructType_EventBubble            EStoryPlotInstructType = 21
	EStoryPlotInstructType_EStoryPlotInstructType_NpcDialog              EStoryPlotInstructType = 22
	EStoryPlotInstructType_EStoryPlotInstructType_HeroDialog             EStoryPlotInstructType = 23
	EStoryPlotInstructType_EStoryPlotInstructType_Black                  EStoryPlotInstructType = 24
	EStoryPlotInstructType_EStoryPlotInstructType_GuidePointer           EStoryPlotInstructType = 25
	EStoryPlotInstructType_EStoryPlotInstructType_ChangeBGM              EStoryPlotInstructType = 26
	EStoryPlotInstructType_EStoryPlotInstructType_NpcAddEffect           EStoryPlotInstructType = 27
	EStoryPlotInstructType_EStoryPlotInstructType_PlaySound              EStoryPlotInstructType = 28
	EStoryPlotInstructType_EStoryPlotInstructType_AddNpcActionFlag       EStoryPlotInstructType = 29
	EStoryPlotInstructType_EStoryPlotInstructType_RemoveNpcActionFlag    EStoryPlotInstructType = 30
	EStoryPlotInstructType_EStoryPlotInstructType_AddPlayerActionFlag    EStoryPlotInstructType = 31
	EStoryPlotInstructType_EStoryPlotInstructType_RemovePlayerActionFlag EStoryPlotInstructType = 32
	EStoryPlotInstructType_EStoryPlotInstructType_AddScenePicture        EStoryPlotInstructType = 33
	EStoryPlotInstructType_EStoryPlotInstructType_RemoveSceneStaticActor EStoryPlotInstructType = 34
	EStoryPlotInstructType_EStoryPlotInstructType_AddSceneEffect         EStoryPlotInstructType = 35
	EStoryPlotInstructType_EStoryPlotInstructType_ChangeSceneEffect      EStoryPlotInstructType = 36
	EStoryPlotInstructType_EStoryPlotInstructType_RemoveNpcEffect        EStoryPlotInstructType = 37
	EStoryPlotInstructType_EStoryPlotInstructType_AddCelebrateView       EStoryPlotInstructType = 38
	EStoryPlotInstructType_EStoryPlotInstructType_PlayVideo              EStoryPlotInstructType = 39
	EStoryPlotInstructType_EStoryPlotInstructType_AddHero                EStoryPlotInstructType = 40
	EStoryPlotInstructType_EStoryPlotInstructType_RemoveHero             EStoryPlotInstructType = 41
	EStoryPlotInstructType_EStoryPlotInstructType_HeroMove               EStoryPlotInstructType = 42
	EStoryPlotInstructType_EStoryPlotInstructType_HeroBubble             EStoryPlotInstructType = 43
	EStoryPlotInstructType_EStoryPlotInstructType_HeroAction             EStoryPlotInstructType = 44
	EStoryPlotInstructType_EStoryPlotInstructType_FocusHero              EStoryPlotInstructType = 45
	EStoryPlotInstructType_EStoryPlotInstructType_AddHeroEffect          EStoryPlotInstructType = 46
	EStoryPlotInstructType_EStoryPlotInstructType_RemoveHeroEffect       EStoryPlotInstructType = 47
	EStoryPlotInstructType_EStoryPlotInstructType_AddHeroActionFlag      EStoryPlotInstructType = 48
	EStoryPlotInstructType_EStoryPlotInstructType_RemoveHeroActionFlag   EStoryPlotInstructType = 49
	EStoryPlotInstructType_EStoryPlotInstructType_LoadUIViewContent      EStoryPlotInstructType = 50
	EStoryPlotInstructType_EStoryPlotInstructType_UIViewSetHeroSpine     EStoryPlotInstructType = 51
	EStoryPlotInstructType_EStoryPlotInstructType_UIViewAddBubble        EStoryPlotInstructType = 52
	EStoryPlotInstructType_EStoryPlotInstructType_WaitPlayerInput        EStoryPlotInstructType = 53
	EStoryPlotInstructType_EStoryPlotInstructType_StoryDialog            EStoryPlotInstructType = 54
	EStoryPlotInstructType_EStoryPlotInstructType_UIViewPlayTransition   EStoryPlotInstructType = 55
	EStoryPlotInstructType_EStoryPlotInstructType_OpenUIAndWaitClose     EStoryPlotInstructType = 56
	EStoryPlotInstructType_EStoryPlotInstructType_ReportShushu           EStoryPlotInstructType = 57
)

// Enum value maps for EStoryPlotInstructType.
var (
	EStoryPlotInstructType_name = map[int32]string{
		0:  "EStoryPlotInstructType_None",
		3:  "EStoryPlotInstructType_Wait",
		4:  "EStoryPlotInstructType_AddNpc",
		5:  "EStoryPlotInstructType_RemoveNpc",
		6:  "EStoryPlotInstructType_NpcMove",
		7:  "EStoryPlotInstructType_NpcBubble",
		8:  "EStoryPlotInstructType_NpcAction",
		9:  "EStoryPlotInstructType_NpcEvent",
		10: "EStoryPlotInstructType_AddPlayer",
		11: "EStoryPlotInstructType_PlayerMove",
		12: "EStoryPlotInstructType_PlayerAction",
		13: "EStoryPlotInstructType_PlayerBubble",
		14: "EStoryPlotInstructType_FocusPlayer",
		15: "EStoryPlotInstructType_FocusNpc",
		16: "EStoryPlotInstructType_FocusPoint",
		17: "EStoryPlotInstructType_ReleaseFocus",
		18: "EStoryPlotInstructType_Finish",
		19: "EStoryPlotInstructType_HideUI",
		20: "EStoryPlotInstructType_ShowUI",
		21: "EStoryPlotInstructType_EventBubble",
		22: "EStoryPlotInstructType_NpcDialog",
		23: "EStoryPlotInstructType_HeroDialog",
		24: "EStoryPlotInstructType_Black",
		25: "EStoryPlotInstructType_GuidePointer",
		26: "EStoryPlotInstructType_ChangeBGM",
		27: "EStoryPlotInstructType_NpcAddEffect",
		28: "EStoryPlotInstructType_PlaySound",
		29: "EStoryPlotInstructType_AddNpcActionFlag",
		30: "EStoryPlotInstructType_RemoveNpcActionFlag",
		31: "EStoryPlotInstructType_AddPlayerActionFlag",
		32: "EStoryPlotInstructType_RemovePlayerActionFlag",
		33: "EStoryPlotInstructType_AddScenePicture",
		34: "EStoryPlotInstructType_RemoveSceneStaticActor",
		35: "EStoryPlotInstructType_AddSceneEffect",
		36: "EStoryPlotInstructType_ChangeSceneEffect",
		37: "EStoryPlotInstructType_RemoveNpcEffect",
		38: "EStoryPlotInstructType_AddCelebrateView",
		39: "EStoryPlotInstructType_PlayVideo",
		40: "EStoryPlotInstructType_AddHero",
		41: "EStoryPlotInstructType_RemoveHero",
		42: "EStoryPlotInstructType_HeroMove",
		43: "EStoryPlotInstructType_HeroBubble",
		44: "EStoryPlotInstructType_HeroAction",
		45: "EStoryPlotInstructType_FocusHero",
		46: "EStoryPlotInstructType_AddHeroEffect",
		47: "EStoryPlotInstructType_RemoveHeroEffect",
		48: "EStoryPlotInstructType_AddHeroActionFlag",
		49: "EStoryPlotInstructType_RemoveHeroActionFlag",
		50: "EStoryPlotInstructType_LoadUIViewContent",
		51: "EStoryPlotInstructType_UIViewSetHeroSpine",
		52: "EStoryPlotInstructType_UIViewAddBubble",
		53: "EStoryPlotInstructType_WaitPlayerInput",
		54: "EStoryPlotInstructType_StoryDialog",
		55: "EStoryPlotInstructType_UIViewPlayTransition",
		56: "EStoryPlotInstructType_OpenUIAndWaitClose",
		57: "EStoryPlotInstructType_ReportShushu",
	}
	EStoryPlotInstructType_value = map[string]int32{
		"EStoryPlotInstructType_None":                   0,
		"EStoryPlotInstructType_Wait":                   3,
		"EStoryPlotInstructType_AddNpc":                 4,
		"EStoryPlotInstructType_RemoveNpc":              5,
		"EStoryPlotInstructType_NpcMove":                6,
		"EStoryPlotInstructType_NpcBubble":              7,
		"EStoryPlotInstructType_NpcAction":              8,
		"EStoryPlotInstructType_NpcEvent":               9,
		"EStoryPlotInstructType_AddPlayer":              10,
		"EStoryPlotInstructType_PlayerMove":             11,
		"EStoryPlotInstructType_PlayerAction":           12,
		"EStoryPlotInstructType_PlayerBubble":           13,
		"EStoryPlotInstructType_FocusPlayer":            14,
		"EStoryPlotInstructType_FocusNpc":               15,
		"EStoryPlotInstructType_FocusPoint":             16,
		"EStoryPlotInstructType_ReleaseFocus":           17,
		"EStoryPlotInstructType_Finish":                 18,
		"EStoryPlotInstructType_HideUI":                 19,
		"EStoryPlotInstructType_ShowUI":                 20,
		"EStoryPlotInstructType_EventBubble":            21,
		"EStoryPlotInstructType_NpcDialog":              22,
		"EStoryPlotInstructType_HeroDialog":             23,
		"EStoryPlotInstructType_Black":                  24,
		"EStoryPlotInstructType_GuidePointer":           25,
		"EStoryPlotInstructType_ChangeBGM":              26,
		"EStoryPlotInstructType_NpcAddEffect":           27,
		"EStoryPlotInstructType_PlaySound":              28,
		"EStoryPlotInstructType_AddNpcActionFlag":       29,
		"EStoryPlotInstructType_RemoveNpcActionFlag":    30,
		"EStoryPlotInstructType_AddPlayerActionFlag":    31,
		"EStoryPlotInstructType_RemovePlayerActionFlag": 32,
		"EStoryPlotInstructType_AddScenePicture":        33,
		"EStoryPlotInstructType_RemoveSceneStaticActor": 34,
		"EStoryPlotInstructType_AddSceneEffect":         35,
		"EStoryPlotInstructType_ChangeSceneEffect":      36,
		"EStoryPlotInstructType_RemoveNpcEffect":        37,
		"EStoryPlotInstructType_AddCelebrateView":       38,
		"EStoryPlotInstructType_PlayVideo":              39,
		"EStoryPlotInstructType_AddHero":                40,
		"EStoryPlotInstructType_RemoveHero":             41,
		"EStoryPlotInstructType_HeroMove":               42,
		"EStoryPlotInstructType_HeroBubble":             43,
		"EStoryPlotInstructType_HeroAction":             44,
		"EStoryPlotInstructType_FocusHero":              45,
		"EStoryPlotInstructType_AddHeroEffect":          46,
		"EStoryPlotInstructType_RemoveHeroEffect":       47,
		"EStoryPlotInstructType_AddHeroActionFlag":      48,
		"EStoryPlotInstructType_RemoveHeroActionFlag":   49,
		"EStoryPlotInstructType_LoadUIViewContent":      50,
		"EStoryPlotInstructType_UIViewSetHeroSpine":     51,
		"EStoryPlotInstructType_UIViewAddBubble":        52,
		"EStoryPlotInstructType_WaitPlayerInput":        53,
		"EStoryPlotInstructType_StoryDialog":            54,
		"EStoryPlotInstructType_UIViewPlayTransition":   55,
		"EStoryPlotInstructType_OpenUIAndWaitClose":     56,
		"EStoryPlotInstructType_ReportShushu":           57,
	}
)

func (x EStoryPlotInstructType) Enum() *EStoryPlotInstructType {
	p := new(EStoryPlotInstructType)
	*p = x
	return p
}

func (x EStoryPlotInstructType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (EStoryPlotInstructType) Descriptor() protoreflect.EnumDescriptor {
	return file_gameconfig_proto_enumTypes[23].Descriptor()
}

func (EStoryPlotInstructType) Type() protoreflect.EnumType {
	return &file_gameconfig_proto_enumTypes[23]
}

func (x EStoryPlotInstructType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Do not use.
func (x *EStoryPlotInstructType) UnmarshalJSON(b []byte) error {
	num, err := protoimpl.X.UnmarshalJSONEnum(x.Descriptor(), b)
	if err != nil {
		return err
	}
	*x = EStoryPlotInstructType(num)
	return nil
}

// Deprecated: Use EStoryPlotInstructType.Descriptor instead.
func (EStoryPlotInstructType) EnumDescriptor() ([]byte, []int) {
	return file_gameconfig_proto_rawDescGZIP(), []int{23}
}

type EShopBuyLimit int32

const (
	EShopBuyLimit_EShopBuyLimit_None      EShopBuyLimit = 0
	EShopBuyLimit_EShopBuyLimit_Daily     EShopBuyLimit = 1
	EShopBuyLimit_EShopBuyLimit_Weekly    EShopBuyLimit = 2
	EShopBuyLimit_EShopBuyLimit_Permanent EShopBuyLimit = 3
)

// Enum value maps for EShopBuyLimit.
var (
	EShopBuyLimit_name = map[int32]string{
		0: "EShopBuyLimit_None",
		1: "EShopBuyLimit_Daily",
		2: "EShopBuyLimit_Weekly",
		3: "EShopBuyLimit_Permanent",
	}
	EShopBuyLimit_value = map[string]int32{
		"EShopBuyLimit_None":      0,
		"EShopBuyLimit_Daily":     1,
		"EShopBuyLimit_Weekly":    2,
		"EShopBuyLimit_Permanent": 3,
	}
)

func (x EShopBuyLimit) Enum() *EShopBuyLimit {
	p := new(EShopBuyLimit)
	*p = x
	return p
}

func (x EShopBuyLimit) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (EShopBuyLimit) Descriptor() protoreflect.EnumDescriptor {
	return file_gameconfig_proto_enumTypes[24].Descriptor()
}

func (EShopBuyLimit) Type() protoreflect.EnumType {
	return &file_gameconfig_proto_enumTypes[24]
}

func (x EShopBuyLimit) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Do not use.
func (x *EShopBuyLimit) UnmarshalJSON(b []byte) error {
	num, err := protoimpl.X.UnmarshalJSONEnum(x.Descriptor(), b)
	if err != nil {
		return err
	}
	*x = EShopBuyLimit(num)
	return nil
}

// Deprecated: Use EShopBuyLimit.Descriptor instead.
func (EShopBuyLimit) EnumDescriptor() ([]byte, []int) {
	return file_gameconfig_proto_rawDescGZIP(), []int{24}
}

type EShopType int32

const (
	EShopType_EShopType_PropShop  EShopType = 0
	EShopType_EShopType_ConstShop EShopType = 1
)

// Enum value maps for EShopType.
var (
	EShopType_name = map[int32]string{
		0: "EShopType_PropShop",
		1: "EShopType_ConstShop",
	}
	EShopType_value = map[string]int32{
		"EShopType_PropShop":  0,
		"EShopType_ConstShop": 1,
	}
)

func (x EShopType) Enum() *EShopType {
	p := new(EShopType)
	*p = x
	return p
}

func (x EShopType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (EShopType) Descriptor() protoreflect.EnumDescriptor {
	return file_gameconfig_proto_enumTypes[25].Descriptor()
}

func (EShopType) Type() protoreflect.EnumType {
	return &file_gameconfig_proto_enumTypes[25]
}

func (x EShopType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Do not use.
func (x *EShopType) UnmarshalJSON(b []byte) error {
	num, err := protoimpl.X.UnmarshalJSONEnum(x.Descriptor(), b)
	if err != nil {
		return err
	}
	*x = EShopType(num)
	return nil
}

// Deprecated: Use EShopType.Descriptor instead.
func (EShopType) EnumDescriptor() ([]byte, []int) {
	return file_gameconfig_proto_rawDescGZIP(), []int{25}
}

type EStoryPlotType int32

const (
	EStoryPlotType_EStoryPlotType_None         EStoryPlotType = 0
	EStoryPlotType_EStoryPlotType_MainScene    EStoryPlotType = 1
	EStoryPlotType_EStoryPlotType_ExploreScene EStoryPlotType = 2
	EStoryPlotType_EStoryPlotType_StoryScene   EStoryPlotType = 3
	EStoryPlotType_EStoryPlotType_StoryUIView  EStoryPlotType = 4
)

// Enum value maps for EStoryPlotType.
var (
	EStoryPlotType_name = map[int32]string{
		0: "EStoryPlotType_None",
		1: "EStoryPlotType_MainScene",
		2: "EStoryPlotType_ExploreScene",
		3: "EStoryPlotType_StoryScene",
		4: "EStoryPlotType_StoryUIView",
	}
	EStoryPlotType_value = map[string]int32{
		"EStoryPlotType_None":         0,
		"EStoryPlotType_MainScene":    1,
		"EStoryPlotType_ExploreScene": 2,
		"EStoryPlotType_StoryScene":   3,
		"EStoryPlotType_StoryUIView":  4,
	}
)

func (x EStoryPlotType) Enum() *EStoryPlotType {
	p := new(EStoryPlotType)
	*p = x
	return p
}

func (x EStoryPlotType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (EStoryPlotType) Descriptor() protoreflect.EnumDescriptor {
	return file_gameconfig_proto_enumTypes[26].Descriptor()
}

func (EStoryPlotType) Type() protoreflect.EnumType {
	return &file_gameconfig_proto_enumTypes[26]
}

func (x EStoryPlotType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Do not use.
func (x *EStoryPlotType) UnmarshalJSON(b []byte) error {
	num, err := protoimpl.X.UnmarshalJSONEnum(x.Descriptor(), b)
	if err != nil {
		return err
	}
	*x = EStoryPlotType(num)
	return nil
}

// Deprecated: Use EStoryPlotType.Descriptor instead.
func (EStoryPlotType) EnumDescriptor() ([]byte, []int) {
	return file_gameconfig_proto_rawDescGZIP(), []int{26}
}

type EStoryPlotInstructConditionType int32

const (
	EStoryPlotInstructConditionType_EStoryPlotInstructConditionType_None                   EStoryPlotInstructConditionType = 0
	EStoryPlotInstructConditionType_EStoryPlotInstructConditionType_SlimeTaskChapterFinish EStoryPlotInstructConditionType = 1
	EStoryPlotInstructConditionType_EStoryPlotInstructConditionType_HomeBuildingLevel      EStoryPlotInstructConditionType = 2
	EStoryPlotInstructConditionType_EStoryPlotInstructConditionType_HasHeroId              EStoryPlotInstructConditionType = 3
)

// Enum value maps for EStoryPlotInstructConditionType.
var (
	EStoryPlotInstructConditionType_name = map[int32]string{
		0: "EStoryPlotInstructConditionType_None",
		1: "EStoryPlotInstructConditionType_SlimeTaskChapterFinish",
		2: "EStoryPlotInstructConditionType_HomeBuildingLevel",
		3: "EStoryPlotInstructConditionType_HasHeroId",
	}
	EStoryPlotInstructConditionType_value = map[string]int32{
		"EStoryPlotInstructConditionType_None":                   0,
		"EStoryPlotInstructConditionType_SlimeTaskChapterFinish": 1,
		"EStoryPlotInstructConditionType_HomeBuildingLevel":      2,
		"EStoryPlotInstructConditionType_HasHeroId":              3,
	}
)

func (x EStoryPlotInstructConditionType) Enum() *EStoryPlotInstructConditionType {
	p := new(EStoryPlotInstructConditionType)
	*p = x
	return p
}

func (x EStoryPlotInstructConditionType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (EStoryPlotInstructConditionType) Descriptor() protoreflect.EnumDescriptor {
	return file_gameconfig_proto_enumTypes[27].Descriptor()
}

func (EStoryPlotInstructConditionType) Type() protoreflect.EnumType {
	return &file_gameconfig_proto_enumTypes[27]
}

func (x EStoryPlotInstructConditionType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Do not use.
func (x *EStoryPlotInstructConditionType) UnmarshalJSON(b []byte) error {
	num, err := protoimpl.X.UnmarshalJSONEnum(x.Descriptor(), b)
	if err != nil {
		return err
	}
	*x = EStoryPlotInstructConditionType(num)
	return nil
}

// Deprecated: Use EStoryPlotInstructConditionType.Descriptor instead.
func (EStoryPlotInstructConditionType) EnumDescriptor() ([]byte, []int) {
	return file_gameconfig_proto_rawDescGZIP(), []int{27}
}

type EMUnit int32

const (
	EMUnit_EMUnit_Root     EMUnit = 0
	EMUnit_EMUnit_Map      EMUnit = 1
	EMUnit_EMUnit_Building EMUnit = 10
	EMUnit_EMUnit_WorkSpot EMUnit = 11
	EMUnit_EMUnit_Event    EMUnit = 12
	EMUnit_EMUnit_Special  EMUnit = 13
	EMUnit_EMUnit_Cloud    EMUnit = 14
	EMUnit_EMUnit_Staff    EMUnit = 15
	EMUnit_EMUnit_Furnture EMUnit = 16
)

// Enum value maps for EMUnit.
var (
	EMUnit_name = map[int32]string{
		0:  "EMUnit_Root",
		1:  "EMUnit_Map",
		10: "EMUnit_Building",
		11: "EMUnit_WorkSpot",
		12: "EMUnit_Event",
		13: "EMUnit_Special",
		14: "EMUnit_Cloud",
		15: "EMUnit_Staff",
		16: "EMUnit_Furnture",
	}
	EMUnit_value = map[string]int32{
		"EMUnit_Root":     0,
		"EMUnit_Map":      1,
		"EMUnit_Building": 10,
		"EMUnit_WorkSpot": 11,
		"EMUnit_Event":    12,
		"EMUnit_Special":  13,
		"EMUnit_Cloud":    14,
		"EMUnit_Staff":    15,
		"EMUnit_Furnture": 16,
	}
)

func (x EMUnit) Enum() *EMUnit {
	p := new(EMUnit)
	*p = x
	return p
}

func (x EMUnit) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (EMUnit) Descriptor() protoreflect.EnumDescriptor {
	return file_gameconfig_proto_enumTypes[28].Descriptor()
}

func (EMUnit) Type() protoreflect.EnumType {
	return &file_gameconfig_proto_enumTypes[28]
}

func (x EMUnit) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Do not use.
func (x *EMUnit) UnmarshalJSON(b []byte) error {
	num, err := protoimpl.X.UnmarshalJSONEnum(x.Descriptor(), b)
	if err != nil {
		return err
	}
	*x = EMUnit(num)
	return nil
}

// Deprecated: Use EMUnit.Descriptor instead.
func (EMUnit) EnumDescriptor() ([]byte, []int) {
	return file_gameconfig_proto_rawDescGZIP(), []int{28}
}

type ETileBlock int32

const (
	ETileBlock_ETileBlock_Invalid      ETileBlock = 0
	ETileBlock_ETileBlock_Data         ETileBlock = 1
	ETileBlock_ETileBlock_BuildingArea ETileBlock = 2
	ETileBlock_ETileBlock_Cloud        ETileBlock = 3
	ETileBlock_ETileBlock_EntityTile   ETileBlock = 4
	ETileBlock_ETileBlock_LotteryDup   ETileBlock = 5
)

// Enum value maps for ETileBlock.
var (
	ETileBlock_name = map[int32]string{
		0: "ETileBlock_Invalid",
		1: "ETileBlock_Data",
		2: "ETileBlock_BuildingArea",
		3: "ETileBlock_Cloud",
		4: "ETileBlock_EntityTile",
		5: "ETileBlock_LotteryDup",
	}
	ETileBlock_value = map[string]int32{
		"ETileBlock_Invalid":      0,
		"ETileBlock_Data":         1,
		"ETileBlock_BuildingArea": 2,
		"ETileBlock_Cloud":        3,
		"ETileBlock_EntityTile":   4,
		"ETileBlock_LotteryDup":   5,
	}
)

func (x ETileBlock) Enum() *ETileBlock {
	p := new(ETileBlock)
	*p = x
	return p
}

func (x ETileBlock) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ETileBlock) Descriptor() protoreflect.EnumDescriptor {
	return file_gameconfig_proto_enumTypes[29].Descriptor()
}

func (ETileBlock) Type() protoreflect.EnumType {
	return &file_gameconfig_proto_enumTypes[29]
}

func (x ETileBlock) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Do not use.
func (x *ETileBlock) UnmarshalJSON(b []byte) error {
	num, err := protoimpl.X.UnmarshalJSONEnum(x.Descriptor(), b)
	if err != nil {
		return err
	}
	*x = ETileBlock(num)
	return nil
}

// Deprecated: Use ETileBlock.Descriptor instead.
func (ETileBlock) EnumDescriptor() ([]byte, []int) {
	return file_gameconfig_proto_rawDescGZIP(), []int{29}
}

type EMapLogicLayer int32

const (
	EMapLogicLayer_EMapLogicLayer_Invalid  EMapLogicLayer = 0
	EMapLogicLayer_EMapLogicLayer_Data     EMapLogicLayer = 1
	EMapLogicLayer_EMapLogicLayer_Building EMapLogicLayer = 2
	EMapLogicLayer_EMapLogicLayer_Clouds   EMapLogicLayer = 3
	EMapLogicLayer_EMapLogicLayer_MUnit    EMapLogicLayer = 4
)

// Enum value maps for EMapLogicLayer.
var (
	EMapLogicLayer_name = map[int32]string{
		0: "EMapLogicLayer_Invalid",
		1: "EMapLogicLayer_Data",
		2: "EMapLogicLayer_Building",
		3: "EMapLogicLayer_Clouds",
		4: "EMapLogicLayer_MUnit",
	}
	EMapLogicLayer_value = map[string]int32{
		"EMapLogicLayer_Invalid":  0,
		"EMapLogicLayer_Data":     1,
		"EMapLogicLayer_Building": 2,
		"EMapLogicLayer_Clouds":   3,
		"EMapLogicLayer_MUnit":    4,
	}
)

func (x EMapLogicLayer) Enum() *EMapLogicLayer {
	p := new(EMapLogicLayer)
	*p = x
	return p
}

func (x EMapLogicLayer) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (EMapLogicLayer) Descriptor() protoreflect.EnumDescriptor {
	return file_gameconfig_proto_enumTypes[30].Descriptor()
}

func (EMapLogicLayer) Type() protoreflect.EnumType {
	return &file_gameconfig_proto_enumTypes[30]
}

func (x EMapLogicLayer) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Do not use.
func (x *EMapLogicLayer) UnmarshalJSON(b []byte) error {
	num, err := protoimpl.X.UnmarshalJSONEnum(x.Descriptor(), b)
	if err != nil {
		return err
	}
	*x = EMapLogicLayer(num)
	return nil
}

// Deprecated: Use EMapLogicLayer.Descriptor instead.
func (EMapLogicLayer) EnumDescriptor() ([]byte, []int) {
	return file_gameconfig_proto_rawDescGZIP(), []int{30}
}

type EMapType int32

const (
	EMapType_EMapType_Invalid    EMapType = 0
	EMapType_EMapType_Scene      EMapType = 1
	EMapType_EMapType_LotteryDup EMapType = 2
)

// Enum value maps for EMapType.
var (
	EMapType_name = map[int32]string{
		0: "EMapType_Invalid",
		1: "EMapType_Scene",
		2: "EMapType_LotteryDup",
	}
	EMapType_value = map[string]int32{
		"EMapType_Invalid":    0,
		"EMapType_Scene":      1,
		"EMapType_LotteryDup": 2,
	}
)

func (x EMapType) Enum() *EMapType {
	p := new(EMapType)
	*p = x
	return p
}

func (x EMapType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (EMapType) Descriptor() protoreflect.EnumDescriptor {
	return file_gameconfig_proto_enumTypes[31].Descriptor()
}

func (EMapType) Type() protoreflect.EnumType {
	return &file_gameconfig_proto_enumTypes[31]
}

func (x EMapType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Do not use.
func (x *EMapType) UnmarshalJSON(b []byte) error {
	num, err := protoimpl.X.UnmarshalJSONEnum(x.Descriptor(), b)
	if err != nil {
		return err
	}
	*x = EMapType(num)
	return nil
}

// Deprecated: Use EMapType.Descriptor instead.
func (EMapType) EnumDescriptor() ([]byte, []int) {
	return file_gameconfig_proto_rawDescGZIP(), []int{31}
}

type EMapLayer int32

const (
	EMapLayer_EMapLayer_None                EMapLayer = 0
	EMapLayer_EMapLayer_BuildingFloorLayer  EMapLayer = 1
	EMapLayer_EMapLayer_StaffDownLayer      EMapLayer = 2
	EMapLayer_EMapLayer_HeroLayer           EMapLayer = 3
	EMapLayer_EMapLayer_StaffUpLayer        EMapLayer = 4
	EMapLayer_EMapLayer_BuildingRoofLayer   EMapLayer = 5
	EMapLayer_EMapLayer_BuildingBarLayer    EMapLayer = 6
	EMapLayer_EMapLayer_BuildingRoofLayerUp EMapLayer = 7
	EMapLayer_EMapLayer_CloudTopLayer       EMapLayer = 8
)

// Enum value maps for EMapLayer.
var (
	EMapLayer_name = map[int32]string{
		0: "EMapLayer_None",
		1: "EMapLayer_BuildingFloorLayer",
		2: "EMapLayer_StaffDownLayer",
		3: "EMapLayer_HeroLayer",
		4: "EMapLayer_StaffUpLayer",
		5: "EMapLayer_BuildingRoofLayer",
		6: "EMapLayer_BuildingBarLayer",
		7: "EMapLayer_BuildingRoofLayerUp",
		8: "EMapLayer_CloudTopLayer",
	}
	EMapLayer_value = map[string]int32{
		"EMapLayer_None":                0,
		"EMapLayer_BuildingFloorLayer":  1,
		"EMapLayer_StaffDownLayer":      2,
		"EMapLayer_HeroLayer":           3,
		"EMapLayer_StaffUpLayer":        4,
		"EMapLayer_BuildingRoofLayer":   5,
		"EMapLayer_BuildingBarLayer":    6,
		"EMapLayer_BuildingRoofLayerUp": 7,
		"EMapLayer_CloudTopLayer":       8,
	}
)

func (x EMapLayer) Enum() *EMapLayer {
	p := new(EMapLayer)
	*p = x
	return p
}

func (x EMapLayer) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (EMapLayer) Descriptor() protoreflect.EnumDescriptor {
	return file_gameconfig_proto_enumTypes[32].Descriptor()
}

func (EMapLayer) Type() protoreflect.EnumType {
	return &file_gameconfig_proto_enumTypes[32]
}

func (x EMapLayer) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Do not use.
func (x *EMapLayer) UnmarshalJSON(b []byte) error {
	num, err := protoimpl.X.UnmarshalJSONEnum(x.Descriptor(), b)
	if err != nil {
		return err
	}
	*x = EMapLayer(num)
	return nil
}

// Deprecated: Use EMapLayer.Descriptor instead.
func (EMapLayer) EnumDescriptor() ([]byte, []int) {
	return file_gameconfig_proto_rawDescGZIP(), []int{32}
}

type EGuide int32

const (
	EGuide_EGuide_None           EGuide = 0
	EGuide_EGuide_GuidePointer   EGuide = 6
	EGuide_EGuide_GuideBuilding  EGuide = 26
	EGuide_EGuide_GuideCityEvent EGuide = 27
)

// Enum value maps for EGuide.
var (
	EGuide_name = map[int32]string{
		0:  "EGuide_None",
		6:  "EGuide_GuidePointer",
		26: "EGuide_GuideBuilding",
		27: "EGuide_GuideCityEvent",
	}
	EGuide_value = map[string]int32{
		"EGuide_None":           0,
		"EGuide_GuidePointer":   6,
		"EGuide_GuideBuilding":  26,
		"EGuide_GuideCityEvent": 27,
	}
)

func (x EGuide) Enum() *EGuide {
	p := new(EGuide)
	*p = x
	return p
}

func (x EGuide) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (EGuide) Descriptor() protoreflect.EnumDescriptor {
	return file_gameconfig_proto_enumTypes[33].Descriptor()
}

func (EGuide) Type() protoreflect.EnumType {
	return &file_gameconfig_proto_enumTypes[33]
}

func (x EGuide) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Do not use.
func (x *EGuide) UnmarshalJSON(b []byte) error {
	num, err := protoimpl.X.UnmarshalJSONEnum(x.Descriptor(), b)
	if err != nil {
		return err
	}
	*x = EGuide(num)
	return nil
}

// Deprecated: Use EGuide.Descriptor instead.
func (EGuide) EnumDescriptor() ([]byte, []int) {
	return file_gameconfig_proto_rawDescGZIP(), []int{33}
}

type EAttrFormatType int32

const (
	EAttrFormatType_EAttrFormatType_None    EAttrFormatType = 0
	EAttrFormatType_EAttrFormatType_Value   EAttrFormatType = 1
	EAttrFormatType_EAttrFormatType_Percent EAttrFormatType = 2
	EAttrFormatType_EAttrFormatType_Time    EAttrFormatType = 3
)

// Enum value maps for EAttrFormatType.
var (
	EAttrFormatType_name = map[int32]string{
		0: "EAttrFormatType_None",
		1: "EAttrFormatType_Value",
		2: "EAttrFormatType_Percent",
		3: "EAttrFormatType_Time",
	}
	EAttrFormatType_value = map[string]int32{
		"EAttrFormatType_None":    0,
		"EAttrFormatType_Value":   1,
		"EAttrFormatType_Percent": 2,
		"EAttrFormatType_Time":    3,
	}
)

func (x EAttrFormatType) Enum() *EAttrFormatType {
	p := new(EAttrFormatType)
	*p = x
	return p
}

func (x EAttrFormatType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (EAttrFormatType) Descriptor() protoreflect.EnumDescriptor {
	return file_gameconfig_proto_enumTypes[34].Descriptor()
}

func (EAttrFormatType) Type() protoreflect.EnumType {
	return &file_gameconfig_proto_enumTypes[34]
}

func (x EAttrFormatType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Do not use.
func (x *EAttrFormatType) UnmarshalJSON(b []byte) error {
	num, err := protoimpl.X.UnmarshalJSONEnum(x.Descriptor(), b)
	if err != nil {
		return err
	}
	*x = EAttrFormatType(num)
	return nil
}

// Deprecated: Use EAttrFormatType.Descriptor instead.
func (EAttrFormatType) EnumDescriptor() ([]byte, []int) {
	return file_gameconfig_proto_rawDescGZIP(), []int{34}
}

type ERedPoint int32

const (
	ERedPoint_ERedPoint_invalid     ERedPoint = 0
	ERedPoint_ERedPoint_AlawaysTrue ERedPoint = 1
	ERedPoint_ERedPoint_MailSys     ERedPoint = 2
)

// Enum value maps for ERedPoint.
var (
	ERedPoint_name = map[int32]string{
		0: "ERedPoint_invalid",
		1: "ERedPoint_AlawaysTrue",
		2: "ERedPoint_MailSys",
	}
	ERedPoint_value = map[string]int32{
		"ERedPoint_invalid":     0,
		"ERedPoint_AlawaysTrue": 1,
		"ERedPoint_MailSys":     2,
	}
)

func (x ERedPoint) Enum() *ERedPoint {
	p := new(ERedPoint)
	*p = x
	return p
}

func (x ERedPoint) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ERedPoint) Descriptor() protoreflect.EnumDescriptor {
	return file_gameconfig_proto_enumTypes[35].Descriptor()
}

func (ERedPoint) Type() protoreflect.EnumType {
	return &file_gameconfig_proto_enumTypes[35]
}

func (x ERedPoint) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Do not use.
func (x *ERedPoint) UnmarshalJSON(b []byte) error {
	num, err := protoimpl.X.UnmarshalJSONEnum(x.Descriptor(), b)
	if err != nil {
		return err
	}
	*x = ERedPoint(num)
	return nil
}

// Deprecated: Use ERedPoint.Descriptor instead.
func (ERedPoint) EnumDescriptor() ([]byte, []int) {
	return file_gameconfig_proto_rawDescGZIP(), []int{35}
}

type ETileBlockWorkType int32

const (
	ETileBlockWorkType_ETileBlockWorkType_Seat  ETileBlockWorkType = 0
	ETileBlockWorkType_ETileBlockWorkType_Queue ETileBlockWorkType = 1
	ETileBlockWorkType_ETileBlockWorkType_Gate  ETileBlockWorkType = 2
	ETileBlockWorkType_ETileBlockWorkType_Rent  ETileBlockWorkType = 31
)

// Enum value maps for ETileBlockWorkType.
var (
	ETileBlockWorkType_name = map[int32]string{
		0:  "ETileBlockWorkType_Seat",
		1:  "ETileBlockWorkType_Queue",
		2:  "ETileBlockWorkType_Gate",
		31: "ETileBlockWorkType_Rent",
	}
	ETileBlockWorkType_value = map[string]int32{
		"ETileBlockWorkType_Seat":  0,
		"ETileBlockWorkType_Queue": 1,
		"ETileBlockWorkType_Gate":  2,
		"ETileBlockWorkType_Rent":  31,
	}
)

func (x ETileBlockWorkType) Enum() *ETileBlockWorkType {
	p := new(ETileBlockWorkType)
	*p = x
	return p
}

func (x ETileBlockWorkType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ETileBlockWorkType) Descriptor() protoreflect.EnumDescriptor {
	return file_gameconfig_proto_enumTypes[36].Descriptor()
}

func (ETileBlockWorkType) Type() protoreflect.EnumType {
	return &file_gameconfig_proto_enumTypes[36]
}

func (x ETileBlockWorkType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Do not use.
func (x *ETileBlockWorkType) UnmarshalJSON(b []byte) error {
	num, err := protoimpl.X.UnmarshalJSONEnum(x.Descriptor(), b)
	if err != nil {
		return err
	}
	*x = ETileBlockWorkType(num)
	return nil
}

// Deprecated: Use ETileBlockWorkType.Descriptor instead.
func (ETileBlockWorkType) EnumDescriptor() ([]byte, []int) {
	return file_gameconfig_proto_rawDescGZIP(), []int{36}
}

type ECondTriggerType int32

const (
	ECondTriggerType_ECondTriggerType_None       ECondTriggerType = 0
	ECondTriggerType_ECondTriggerType_ModifyTime ECondTriggerType = 1
)

// Enum value maps for ECondTriggerType.
var (
	ECondTriggerType_name = map[int32]string{
		0: "ECondTriggerType_None",
		1: "ECondTriggerType_ModifyTime",
	}
	ECondTriggerType_value = map[string]int32{
		"ECondTriggerType_None":       0,
		"ECondTriggerType_ModifyTime": 1,
	}
)

func (x ECondTriggerType) Enum() *ECondTriggerType {
	p := new(ECondTriggerType)
	*p = x
	return p
}

func (x ECondTriggerType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ECondTriggerType) Descriptor() protoreflect.EnumDescriptor {
	return file_gameconfig_proto_enumTypes[37].Descriptor()
}

func (ECondTriggerType) Type() protoreflect.EnumType {
	return &file_gameconfig_proto_enumTypes[37]
}

func (x ECondTriggerType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Do not use.
func (x *ECondTriggerType) UnmarshalJSON(b []byte) error {
	num, err := protoimpl.X.UnmarshalJSONEnum(x.Descriptor(), b)
	if err != nil {
		return err
	}
	*x = ECondTriggerType(num)
	return nil
}

// Deprecated: Use ECondTriggerType.Descriptor instead.
func (ECondTriggerType) EnumDescriptor() ([]byte, []int) {
	return file_gameconfig_proto_rawDescGZIP(), []int{37}
}

type EActorDirection int32

const (
	EActorDirection_EActorDirection_None  EActorDirection = 0
	EActorDirection_EActorDirection_Left  EActorDirection = 1
	EActorDirection_EActorDirection_Right EActorDirection = 2
)

// Enum value maps for EActorDirection.
var (
	EActorDirection_name = map[int32]string{
		0: "EActorDirection_None",
		1: "EActorDirection_Left",
		2: "EActorDirection_Right",
	}
	EActorDirection_value = map[string]int32{
		"EActorDirection_None":  0,
		"EActorDirection_Left":  1,
		"EActorDirection_Right": 2,
	}
)

func (x EActorDirection) Enum() *EActorDirection {
	p := new(EActorDirection)
	*p = x
	return p
}

func (x EActorDirection) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (EActorDirection) Descriptor() protoreflect.EnumDescriptor {
	return file_gameconfig_proto_enumTypes[38].Descriptor()
}

func (EActorDirection) Type() protoreflect.EnumType {
	return &file_gameconfig_proto_enumTypes[38]
}

func (x EActorDirection) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Do not use.
func (x *EActorDirection) UnmarshalJSON(b []byte) error {
	num, err := protoimpl.X.UnmarshalJSONEnum(x.Descriptor(), b)
	if err != nil {
		return err
	}
	*x = EActorDirection(num)
	return nil
}

// Deprecated: Use EActorDirection.Descriptor instead.
func (EActorDirection) EnumDescriptor() ([]byte, []int) {
	return file_gameconfig_proto_rawDescGZIP(), []int{38}
}

type Vector2 struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	X             *float32               `protobuf:"fixed32,1,opt,name=x" json:"x,omitempty"`
	Y             *float32               `protobuf:"fixed32,2,opt,name=y" json:"y,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Vector2) Reset() {
	*x = Vector2{}
	mi := &file_gameconfig_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Vector2) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Vector2) ProtoMessage() {}

func (x *Vector2) ProtoReflect() protoreflect.Message {
	mi := &file_gameconfig_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Vector2.ProtoReflect.Descriptor instead.
func (*Vector2) Descriptor() ([]byte, []int) {
	return file_gameconfig_proto_rawDescGZIP(), []int{0}
}

func (x *Vector2) GetX() float32 {
	if x != nil && x.X != nil {
		return *x.X
	}
	return 0
}

func (x *Vector2) GetY() float32 {
	if x != nil && x.Y != nil {
		return *x.Y
	}
	return 0
}

type Vector3 struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	X             *float32               `protobuf:"fixed32,1,opt,name=x" json:"x,omitempty"`
	Y             *float32               `protobuf:"fixed32,2,opt,name=y" json:"y,omitempty"`
	Z             *float32               `protobuf:"fixed32,3,opt,name=z" json:"z,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Vector3) Reset() {
	*x = Vector3{}
	mi := &file_gameconfig_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Vector3) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Vector3) ProtoMessage() {}

func (x *Vector3) ProtoReflect() protoreflect.Message {
	mi := &file_gameconfig_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Vector3.ProtoReflect.Descriptor instead.
func (*Vector3) Descriptor() ([]byte, []int) {
	return file_gameconfig_proto_rawDescGZIP(), []int{1}
}

func (x *Vector3) GetX() float32 {
	if x != nil && x.X != nil {
		return *x.X
	}
	return 0
}

func (x *Vector3) GetY() float32 {
	if x != nil && x.Y != nil {
		return *x.Y
	}
	return 0
}

func (x *Vector3) GetZ() float32 {
	if x != nil && x.Z != nil {
		return *x.Z
	}
	return 0
}

type AttrData struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Attr          *EAttr                 `protobuf:"varint,1,opt,name=Attr,enum=pb.EAttr" json:"Attr,omitempty"`
	Value         *int64                 `protobuf:"varint,2,opt,name=Value" json:"Value,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *AttrData) Reset() {
	*x = AttrData{}
	mi := &file_gameconfig_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AttrData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AttrData) ProtoMessage() {}

func (x *AttrData) ProtoReflect() protoreflect.Message {
	mi := &file_gameconfig_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AttrData.ProtoReflect.Descriptor instead.
func (*AttrData) Descriptor() ([]byte, []int) {
	return file_gameconfig_proto_rawDescGZIP(), []int{2}
}

func (x *AttrData) GetAttr() EAttr {
	if x != nil && x.Attr != nil {
		return *x.Attr
	}
	return EAttr_EAttr_Invalid
}

func (x *AttrData) GetValue() int64 {
	if x != nil && x.Value != nil {
		return *x.Value
	}
	return 0
}

type ItemData struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            *int32                 `protobuf:"varint,1,opt,name=Id" json:"Id,omitempty"`
	Count         *int32                 `protobuf:"varint,2,opt,name=Count" json:"Count,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ItemData) Reset() {
	*x = ItemData{}
	mi := &file_gameconfig_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ItemData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ItemData) ProtoMessage() {}

func (x *ItemData) ProtoReflect() protoreflect.Message {
	mi := &file_gameconfig_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ItemData.ProtoReflect.Descriptor instead.
func (*ItemData) Descriptor() ([]byte, []int) {
	return file_gameconfig_proto_rawDescGZIP(), []int{3}
}

func (x *ItemData) GetId() int32 {
	if x != nil && x.Id != nil {
		return *x.Id
	}
	return 0
}

func (x *ItemData) GetCount() int32 {
	if x != nil && x.Count != nil {
		return *x.Count
	}
	return 0
}

type HeroAttr struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Attr          *EHeroAttr             `protobuf:"varint,1,opt,name=Attr,enum=pb.EHeroAttr" json:"Attr,omitempty"`
	Value         *int64                 `protobuf:"varint,2,opt,name=Value" json:"Value,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *HeroAttr) Reset() {
	*x = HeroAttr{}
	mi := &file_gameconfig_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *HeroAttr) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HeroAttr) ProtoMessage() {}

func (x *HeroAttr) ProtoReflect() protoreflect.Message {
	mi := &file_gameconfig_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HeroAttr.ProtoReflect.Descriptor instead.
func (*HeroAttr) Descriptor() ([]byte, []int) {
	return file_gameconfig_proto_rawDescGZIP(), []int{4}
}

func (x *HeroAttr) GetAttr() EHeroAttr {
	if x != nil && x.Attr != nil {
		return *x.Attr
	}
	return EHeroAttr_EHeroAttr_Invalid
}

func (x *HeroAttr) GetValue() int64 {
	if x != nil && x.Value != nil {
		return *x.Value
	}
	return 0
}

type RandomItemData struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            *int32                 `protobuf:"varint,1,opt,name=Id" json:"Id,omitempty"`
	Count         *int64                 `protobuf:"varint,2,opt,name=Count" json:"Count,omitempty"`
	Prop          *int32                 `protobuf:"varint,3,opt,name=Prop" json:"Prop,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *RandomItemData) Reset() {
	*x = RandomItemData{}
	mi := &file_gameconfig_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RandomItemData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RandomItemData) ProtoMessage() {}

func (x *RandomItemData) ProtoReflect() protoreflect.Message {
	mi := &file_gameconfig_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RandomItemData.ProtoReflect.Descriptor instead.
func (*RandomItemData) Descriptor() ([]byte, []int) {
	return file_gameconfig_proto_rawDescGZIP(), []int{5}
}

func (x *RandomItemData) GetId() int32 {
	if x != nil && x.Id != nil {
		return *x.Id
	}
	return 0
}

func (x *RandomItemData) GetCount() int64 {
	if x != nil && x.Count != nil {
		return *x.Count
	}
	return 0
}

func (x *RandomItemData) GetProp() int32 {
	if x != nil && x.Prop != nil {
		return *x.Prop
	}
	return 0
}

type AwardGroupData struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            *int32                 `protobuf:"varint,1,opt,name=Id" json:"Id,omitempty"`
	Count         *int32                 `protobuf:"varint,2,opt,name=Count" json:"Count,omitempty"`
	Prop          *int32                 `protobuf:"varint,3,opt,name=Prop" json:"Prop,omitempty"`
	Rate          *int32                 `protobuf:"varint,4,opt,name=Rate" json:"Rate,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *AwardGroupData) Reset() {
	*x = AwardGroupData{}
	mi := &file_gameconfig_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AwardGroupData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AwardGroupData) ProtoMessage() {}

func (x *AwardGroupData) ProtoReflect() protoreflect.Message {
	mi := &file_gameconfig_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AwardGroupData.ProtoReflect.Descriptor instead.
func (*AwardGroupData) Descriptor() ([]byte, []int) {
	return file_gameconfig_proto_rawDescGZIP(), []int{6}
}

func (x *AwardGroupData) GetId() int32 {
	if x != nil && x.Id != nil {
		return *x.Id
	}
	return 0
}

func (x *AwardGroupData) GetCount() int32 {
	if x != nil && x.Count != nil {
		return *x.Count
	}
	return 0
}

func (x *AwardGroupData) GetProp() int32 {
	if x != nil && x.Prop != nil {
		return *x.Prop
	}
	return 0
}

func (x *AwardGroupData) GetRate() int32 {
	if x != nil && x.Rate != nil {
		return *x.Rate
	}
	return 0
}

type ConditionWithDesc struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ConditionId   *int32                 `protobuf:"varint,1,opt,name=ConditionId" json:"ConditionId,omitempty"`
	Desc          *string                `protobuf:"bytes,2,opt,name=Desc" json:"Desc,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ConditionWithDesc) Reset() {
	*x = ConditionWithDesc{}
	mi := &file_gameconfig_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ConditionWithDesc) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ConditionWithDesc) ProtoMessage() {}

func (x *ConditionWithDesc) ProtoReflect() protoreflect.Message {
	mi := &file_gameconfig_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ConditionWithDesc.ProtoReflect.Descriptor instead.
func (*ConditionWithDesc) Descriptor() ([]byte, []int) {
	return file_gameconfig_proto_rawDescGZIP(), []int{7}
}

func (x *ConditionWithDesc) GetConditionId() int32 {
	if x != nil && x.ConditionId != nil {
		return *x.ConditionId
	}
	return 0
}

func (x *ConditionWithDesc) GetDesc() string {
	if x != nil && x.Desc != nil {
		return *x.Desc
	}
	return ""
}

type ItemProperty struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ItemId        *int32                 `protobuf:"varint,1,opt,name=ItemId" json:"ItemId,omitempty"`
	Prop          *int32                 `protobuf:"varint,2,opt,name=Prop" json:"Prop,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ItemProperty) Reset() {
	*x = ItemProperty{}
	mi := &file_gameconfig_proto_msgTypes[8]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ItemProperty) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ItemProperty) ProtoMessage() {}

func (x *ItemProperty) ProtoReflect() protoreflect.Message {
	mi := &file_gameconfig_proto_msgTypes[8]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ItemProperty.ProtoReflect.Descriptor instead.
func (*ItemProperty) Descriptor() ([]byte, []int) {
	return file_gameconfig_proto_rawDescGZIP(), []int{8}
}

func (x *ItemProperty) GetItemId() int32 {
	if x != nil && x.ItemId != nil {
		return *x.ItemId
	}
	return 0
}

func (x *ItemProperty) GetProp() int32 {
	if x != nil && x.Prop != nil {
		return *x.Prop
	}
	return 0
}

type GridPosition struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	X             *int32                 `protobuf:"varint,1,opt,name=x" json:"x,omitempty"`
	Y             *int32                 `protobuf:"varint,2,opt,name=y" json:"y,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GridPosition) Reset() {
	*x = GridPosition{}
	mi := &file_gameconfig_proto_msgTypes[9]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GridPosition) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GridPosition) ProtoMessage() {}

func (x *GridPosition) ProtoReflect() protoreflect.Message {
	mi := &file_gameconfig_proto_msgTypes[9]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GridPosition.ProtoReflect.Descriptor instead.
func (*GridPosition) Descriptor() ([]byte, []int) {
	return file_gameconfig_proto_rawDescGZIP(), []int{9}
}

func (x *GridPosition) GetX() int32 {
	if x != nil && x.X != nil {
		return *x.X
	}
	return 0
}

func (x *GridPosition) GetY() int32 {
	if x != nil && x.Y != nil {
		return *x.Y
	}
	return 0
}

type GuideDetailTips struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Tips          *string                `protobuf:"bytes,1,opt,name=Tips" json:"Tips,omitempty"`
	Icon          *string                `protobuf:"bytes,2,opt,name=Icon" json:"Icon,omitempty"`
	OffsetX       *int32                 `protobuf:"varint,3,opt,name=OffsetX" json:"OffsetX,omitempty"`
	OffsetY       *int32                 `protobuf:"varint,4,opt,name=OffsetY" json:"OffsetY,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GuideDetailTips) Reset() {
	*x = GuideDetailTips{}
	mi := &file_gameconfig_proto_msgTypes[10]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GuideDetailTips) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GuideDetailTips) ProtoMessage() {}

func (x *GuideDetailTips) ProtoReflect() protoreflect.Message {
	mi := &file_gameconfig_proto_msgTypes[10]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GuideDetailTips.ProtoReflect.Descriptor instead.
func (*GuideDetailTips) Descriptor() ([]byte, []int) {
	return file_gameconfig_proto_rawDescGZIP(), []int{10}
}

func (x *GuideDetailTips) GetTips() string {
	if x != nil && x.Tips != nil {
		return *x.Tips
	}
	return ""
}

func (x *GuideDetailTips) GetIcon() string {
	if x != nil && x.Icon != nil {
		return *x.Icon
	}
	return ""
}

func (x *GuideDetailTips) GetOffsetX() int32 {
	if x != nil && x.OffsetX != nil {
		return *x.OffsetX
	}
	return 0
}

func (x *GuideDetailTips) GetOffsetY() int32 {
	if x != nil && x.OffsetY != nil {
		return *x.OffsetY
	}
	return 0
}

type CountPropData struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Count         *int32                 `protobuf:"varint,1,opt,name=Count" json:"Count,omitempty"`
	Prop          *int32                 `protobuf:"varint,2,opt,name=Prop" json:"Prop,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CountPropData) Reset() {
	*x = CountPropData{}
	mi := &file_gameconfig_proto_msgTypes[11]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CountPropData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CountPropData) ProtoMessage() {}

func (x *CountPropData) ProtoReflect() protoreflect.Message {
	mi := &file_gameconfig_proto_msgTypes[11]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CountPropData.ProtoReflect.Descriptor instead.
func (*CountPropData) Descriptor() ([]byte, []int) {
	return file_gameconfig_proto_rawDescGZIP(), []int{11}
}

func (x *CountPropData) GetCount() int32 {
	if x != nil && x.Count != nil {
		return *x.Count
	}
	return 0
}

func (x *CountPropData) GetProp() int32 {
	if x != nil && x.Prop != nil {
		return *x.Prop
	}
	return 0
}

type XVInt32 struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	A             []int32                `protobuf:"varint,1,rep,name=a" json:"a,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *XVInt32) Reset() {
	*x = XVInt32{}
	mi := &file_gameconfig_proto_msgTypes[12]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *XVInt32) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*XVInt32) ProtoMessage() {}

func (x *XVInt32) ProtoReflect() protoreflect.Message {
	mi := &file_gameconfig_proto_msgTypes[12]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use XVInt32.ProtoReflect.Descriptor instead.
func (*XVInt32) Descriptor() ([]byte, []int) {
	return file_gameconfig_proto_rawDescGZIP(), []int{12}
}

func (x *XVInt32) GetA() []int32 {
	if x != nil {
		return x.A
	}
	return nil
}

// sheet: AIActionConfig
type ListAIActionConfig struct {
	state         protoimpl.MessageState               `protogen:"open.v1"`
	List          []*ListAIActionConfig_AIActionConfig `protobuf:"bytes,1,rep,name=list" json:"list,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListAIActionConfig) Reset() {
	*x = ListAIActionConfig{}
	mi := &file_gameconfig_proto_msgTypes[13]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListAIActionConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListAIActionConfig) ProtoMessage() {}

func (x *ListAIActionConfig) ProtoReflect() protoreflect.Message {
	mi := &file_gameconfig_proto_msgTypes[13]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListAIActionConfig.ProtoReflect.Descriptor instead.
func (*ListAIActionConfig) Descriptor() ([]byte, []int) {
	return file_gameconfig_proto_rawDescGZIP(), []int{13}
}

func (x *ListAIActionConfig) GetList() []*ListAIActionConfig_AIActionConfig {
	if x != nil {
		return x.List
	}
	return nil
}

// sheet: AIConditionConfig
type ListAIConditionConfig struct {
	state         protoimpl.MessageState                     `protogen:"open.v1"`
	List          []*ListAIConditionConfig_AIConditionConfig `protobuf:"bytes,1,rep,name=list" json:"list,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListAIConditionConfig) Reset() {
	*x = ListAIConditionConfig{}
	mi := &file_gameconfig_proto_msgTypes[14]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListAIConditionConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListAIConditionConfig) ProtoMessage() {}

func (x *ListAIConditionConfig) ProtoReflect() protoreflect.Message {
	mi := &file_gameconfig_proto_msgTypes[14]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListAIConditionConfig.ProtoReflect.Descriptor instead.
func (*ListAIConditionConfig) Descriptor() ([]byte, []int) {
	return file_gameconfig_proto_rawDescGZIP(), []int{14}
}

func (x *ListAIConditionConfig) GetList() []*ListAIConditionConfig_AIConditionConfig {
	if x != nil {
		return x.List
	}
	return nil
}

// sheet: AINodeConfig
type ListAINodeConfig struct {
	state         protoimpl.MessageState           `protogen:"open.v1"`
	List          []*ListAINodeConfig_AINodeConfig `protobuf:"bytes,1,rep,name=list" json:"list,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListAINodeConfig) Reset() {
	*x = ListAINodeConfig{}
	mi := &file_gameconfig_proto_msgTypes[15]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListAINodeConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListAINodeConfig) ProtoMessage() {}

func (x *ListAINodeConfig) ProtoReflect() protoreflect.Message {
	mi := &file_gameconfig_proto_msgTypes[15]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListAINodeConfig.ProtoReflect.Descriptor instead.
func (*ListAINodeConfig) Descriptor() ([]byte, []int) {
	return file_gameconfig_proto_rawDescGZIP(), []int{15}
}

func (x *ListAINodeConfig) GetList() []*ListAINodeConfig_AINodeConfig {
	if x != nil {
		return x.List
	}
	return nil
}

// sheet: AvatarConfig
type ListAvatarConfig struct {
	state         protoimpl.MessageState           `protogen:"open.v1"`
	List          []*ListAvatarConfig_AvatarConfig `protobuf:"bytes,1,rep,name=list" json:"list,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListAvatarConfig) Reset() {
	*x = ListAvatarConfig{}
	mi := &file_gameconfig_proto_msgTypes[16]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListAvatarConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListAvatarConfig) ProtoMessage() {}

func (x *ListAvatarConfig) ProtoReflect() protoreflect.Message {
	mi := &file_gameconfig_proto_msgTypes[16]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListAvatarConfig.ProtoReflect.Descriptor instead.
func (*ListAvatarConfig) Descriptor() ([]byte, []int) {
	return file_gameconfig_proto_rawDescGZIP(), []int{16}
}

func (x *ListAvatarConfig) GetList() []*ListAvatarConfig_AvatarConfig {
	if x != nil {
		return x.List
	}
	return nil
}

// sheet: AwardConfig
type ListAwardConfig struct {
	state         protoimpl.MessageState         `protogen:"open.v1"`
	List          []*ListAwardConfig_AwardConfig `protobuf:"bytes,1,rep,name=list" json:"list,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListAwardConfig) Reset() {
	*x = ListAwardConfig{}
	mi := &file_gameconfig_proto_msgTypes[17]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListAwardConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListAwardConfig) ProtoMessage() {}

func (x *ListAwardConfig) ProtoReflect() protoreflect.Message {
	mi := &file_gameconfig_proto_msgTypes[17]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListAwardConfig.ProtoReflect.Descriptor instead.
func (*ListAwardConfig) Descriptor() ([]byte, []int) {
	return file_gameconfig_proto_rawDescGZIP(), []int{17}
}

func (x *ListAwardConfig) GetList() []*ListAwardConfig_AwardConfig {
	if x != nil {
		return x.List
	}
	return nil
}

// sheet: AwardGroupConfig
type ListAwardGroupConfig struct {
	state         protoimpl.MessageState                   `protogen:"open.v1"`
	List          []*ListAwardGroupConfig_AwardGroupConfig `protobuf:"bytes,1,rep,name=list" json:"list,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListAwardGroupConfig) Reset() {
	*x = ListAwardGroupConfig{}
	mi := &file_gameconfig_proto_msgTypes[18]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListAwardGroupConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListAwardGroupConfig) ProtoMessage() {}

func (x *ListAwardGroupConfig) ProtoReflect() protoreflect.Message {
	mi := &file_gameconfig_proto_msgTypes[18]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListAwardGroupConfig.ProtoReflect.Descriptor instead.
func (*ListAwardGroupConfig) Descriptor() ([]byte, []int) {
	return file_gameconfig_proto_rawDescGZIP(), []int{18}
}

func (x *ListAwardGroupConfig) GetList() []*ListAwardGroupConfig_AwardGroupConfig {
	if x != nil {
		return x.List
	}
	return nil
}

// sheet: ConditionConfig
type ListConditionConfig struct {
	state         protoimpl.MessageState                 `protogen:"open.v1"`
	List          []*ListConditionConfig_ConditionConfig `protobuf:"bytes,1,rep,name=list" json:"list,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListConditionConfig) Reset() {
	*x = ListConditionConfig{}
	mi := &file_gameconfig_proto_msgTypes[19]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListConditionConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListConditionConfig) ProtoMessage() {}

func (x *ListConditionConfig) ProtoReflect() protoreflect.Message {
	mi := &file_gameconfig_proto_msgTypes[19]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListConditionConfig.ProtoReflect.Descriptor instead.
func (*ListConditionConfig) Descriptor() ([]byte, []int) {
	return file_gameconfig_proto_rawDescGZIP(), []int{19}
}

func (x *ListConditionConfig) GetList() []*ListConditionConfig_ConditionConfig {
	if x != nil {
		return x.List
	}
	return nil
}

// sheet: ConditionTriggerConfig
type ListConditionTriggerConfig struct {
	state         protoimpl.MessageState                               `protogen:"open.v1"`
	List          []*ListConditionTriggerConfig_ConditionTriggerConfig `protobuf:"bytes,1,rep,name=list" json:"list,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListConditionTriggerConfig) Reset() {
	*x = ListConditionTriggerConfig{}
	mi := &file_gameconfig_proto_msgTypes[20]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListConditionTriggerConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListConditionTriggerConfig) ProtoMessage() {}

func (x *ListConditionTriggerConfig) ProtoReflect() protoreflect.Message {
	mi := &file_gameconfig_proto_msgTypes[20]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListConditionTriggerConfig.ProtoReflect.Descriptor instead.
func (*ListConditionTriggerConfig) Descriptor() ([]byte, []int) {
	return file_gameconfig_proto_rawDescGZIP(), []int{20}
}

func (x *ListConditionTriggerConfig) GetList() []*ListConditionTriggerConfig_ConditionTriggerConfig {
	if x != nil {
		return x.List
	}
	return nil
}

// sheet: ConstConfig
type ListConstConfig struct {
	state         protoimpl.MessageState         `protogen:"open.v1"`
	List          []*ListConstConfig_ConstConfig `protobuf:"bytes,1,rep,name=list" json:"list,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListConstConfig) Reset() {
	*x = ListConstConfig{}
	mi := &file_gameconfig_proto_msgTypes[21]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListConstConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListConstConfig) ProtoMessage() {}

func (x *ListConstConfig) ProtoReflect() protoreflect.Message {
	mi := &file_gameconfig_proto_msgTypes[21]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListConstConfig.ProtoReflect.Descriptor instead.
func (*ListConstConfig) Descriptor() ([]byte, []int) {
	return file_gameconfig_proto_rawDescGZIP(), []int{21}
}

func (x *ListConstConfig) GetList() []*ListConstConfig_ConstConfig {
	if x != nil {
		return x.List
	}
	return nil
}

// sheet: ConstShopGoodsConfig
type ListConstShopGoodsConfig struct {
	state         protoimpl.MessageState                           `protogen:"open.v1"`
	List          []*ListConstShopGoodsConfig_ConstShopGoodsConfig `protobuf:"bytes,1,rep,name=list" json:"list,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListConstShopGoodsConfig) Reset() {
	*x = ListConstShopGoodsConfig{}
	mi := &file_gameconfig_proto_msgTypes[22]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListConstShopGoodsConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListConstShopGoodsConfig) ProtoMessage() {}

func (x *ListConstShopGoodsConfig) ProtoReflect() protoreflect.Message {
	mi := &file_gameconfig_proto_msgTypes[22]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListConstShopGoodsConfig.ProtoReflect.Descriptor instead.
func (*ListConstShopGoodsConfig) Descriptor() ([]byte, []int) {
	return file_gameconfig_proto_rawDescGZIP(), []int{22}
}

func (x *ListConstShopGoodsConfig) GetList() []*ListConstShopGoodsConfig_ConstShopGoodsConfig {
	if x != nil {
		return x.List
	}
	return nil
}

// sheet: GainItemConfig
type ListGainItemConfig struct {
	state         protoimpl.MessageState               `protogen:"open.v1"`
	List          []*ListGainItemConfig_GainItemConfig `protobuf:"bytes,1,rep,name=list" json:"list,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListGainItemConfig) Reset() {
	*x = ListGainItemConfig{}
	mi := &file_gameconfig_proto_msgTypes[23]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListGainItemConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListGainItemConfig) ProtoMessage() {}

func (x *ListGainItemConfig) ProtoReflect() protoreflect.Message {
	mi := &file_gameconfig_proto_msgTypes[23]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListGainItemConfig.ProtoReflect.Descriptor instead.
func (*ListGainItemConfig) Descriptor() ([]byte, []int) {
	return file_gameconfig_proto_rawDescGZIP(), []int{23}
}

func (x *ListGainItemConfig) GetList() []*ListGainItemConfig_GainItemConfig {
	if x != nil {
		return x.List
	}
	return nil
}

// sheet: GuideConfig
type ListGuideConfig struct {
	state         protoimpl.MessageState         `protogen:"open.v1"`
	List          []*ListGuideConfig_GuideConfig `protobuf:"bytes,1,rep,name=list" json:"list,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListGuideConfig) Reset() {
	*x = ListGuideConfig{}
	mi := &file_gameconfig_proto_msgTypes[24]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListGuideConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListGuideConfig) ProtoMessage() {}

func (x *ListGuideConfig) ProtoReflect() protoreflect.Message {
	mi := &file_gameconfig_proto_msgTypes[24]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListGuideConfig.ProtoReflect.Descriptor instead.
func (*ListGuideConfig) Descriptor() ([]byte, []int) {
	return file_gameconfig_proto_rawDescGZIP(), []int{24}
}

func (x *ListGuideConfig) GetList() []*ListGuideConfig_GuideConfig {
	if x != nil {
		return x.List
	}
	return nil
}

// sheet: GuideGroupConfig
type ListGuideGroupConfig struct {
	state         protoimpl.MessageState                   `protogen:"open.v1"`
	List          []*ListGuideGroupConfig_GuideGroupConfig `protobuf:"bytes,1,rep,name=list" json:"list,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListGuideGroupConfig) Reset() {
	*x = ListGuideGroupConfig{}
	mi := &file_gameconfig_proto_msgTypes[25]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListGuideGroupConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListGuideGroupConfig) ProtoMessage() {}

func (x *ListGuideGroupConfig) ProtoReflect() protoreflect.Message {
	mi := &file_gameconfig_proto_msgTypes[25]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListGuideGroupConfig.ProtoReflect.Descriptor instead.
func (*ListGuideGroupConfig) Descriptor() ([]byte, []int) {
	return file_gameconfig_proto_rawDescGZIP(), []int{25}
}

func (x *ListGuideGroupConfig) GetList() []*ListGuideGroupConfig_GuideGroupConfig {
	if x != nil {
		return x.List
	}
	return nil
}

// sheet: HeadPortraitConfig
type ListHeadPortraitConfig struct {
	state         protoimpl.MessageState                       `protogen:"open.v1"`
	List          []*ListHeadPortraitConfig_HeadPortraitConfig `protobuf:"bytes,1,rep,name=list" json:"list,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListHeadPortraitConfig) Reset() {
	*x = ListHeadPortraitConfig{}
	mi := &file_gameconfig_proto_msgTypes[26]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListHeadPortraitConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListHeadPortraitConfig) ProtoMessage() {}

func (x *ListHeadPortraitConfig) ProtoReflect() protoreflect.Message {
	mi := &file_gameconfig_proto_msgTypes[26]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListHeadPortraitConfig.ProtoReflect.Descriptor instead.
func (*ListHeadPortraitConfig) Descriptor() ([]byte, []int) {
	return file_gameconfig_proto_rawDescGZIP(), []int{26}
}

func (x *ListHeadPortraitConfig) GetList() []*ListHeadPortraitConfig_HeadPortraitConfig {
	if x != nil {
		return x.List
	}
	return nil
}

// sheet: HeroConfig
type ListHeroConfig struct {
	state         protoimpl.MessageState       `protogen:"open.v1"`
	List          []*ListHeroConfig_HeroConfig `protobuf:"bytes,1,rep,name=list" json:"list,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListHeroConfig) Reset() {
	*x = ListHeroConfig{}
	mi := &file_gameconfig_proto_msgTypes[27]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListHeroConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListHeroConfig) ProtoMessage() {}

func (x *ListHeroConfig) ProtoReflect() protoreflect.Message {
	mi := &file_gameconfig_proto_msgTypes[27]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListHeroConfig.ProtoReflect.Descriptor instead.
func (*ListHeroConfig) Descriptor() ([]byte, []int) {
	return file_gameconfig_proto_rawDescGZIP(), []int{27}
}

func (x *ListHeroConfig) GetList() []*ListHeroConfig_HeroConfig {
	if x != nil {
		return x.List
	}
	return nil
}

// sheet: HeroLevelConfig
type ListHeroLevelConfig struct {
	state         protoimpl.MessageState                 `protogen:"open.v1"`
	List          []*ListHeroLevelConfig_HeroLevelConfig `protobuf:"bytes,1,rep,name=list" json:"list,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListHeroLevelConfig) Reset() {
	*x = ListHeroLevelConfig{}
	mi := &file_gameconfig_proto_msgTypes[28]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListHeroLevelConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListHeroLevelConfig) ProtoMessage() {}

func (x *ListHeroLevelConfig) ProtoReflect() protoreflect.Message {
	mi := &file_gameconfig_proto_msgTypes[28]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListHeroLevelConfig.ProtoReflect.Descriptor instead.
func (*ListHeroLevelConfig) Descriptor() ([]byte, []int) {
	return file_gameconfig_proto_rawDescGZIP(), []int{28}
}

func (x *ListHeroLevelConfig) GetList() []*ListHeroLevelConfig_HeroLevelConfig {
	if x != nil {
		return x.List
	}
	return nil
}

// sheet: HeroQualityConfig
type ListHeroQualityConfig struct {
	state         protoimpl.MessageState                     `protogen:"open.v1"`
	List          []*ListHeroQualityConfig_HeroQualityConfig `protobuf:"bytes,1,rep,name=list" json:"list,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListHeroQualityConfig) Reset() {
	*x = ListHeroQualityConfig{}
	mi := &file_gameconfig_proto_msgTypes[29]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListHeroQualityConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListHeroQualityConfig) ProtoMessage() {}

func (x *ListHeroQualityConfig) ProtoReflect() protoreflect.Message {
	mi := &file_gameconfig_proto_msgTypes[29]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListHeroQualityConfig.ProtoReflect.Descriptor instead.
func (*ListHeroQualityConfig) Descriptor() ([]byte, []int) {
	return file_gameconfig_proto_rawDescGZIP(), []int{29}
}

func (x *ListHeroQualityConfig) GetList() []*ListHeroQualityConfig_HeroQualityConfig {
	if x != nil {
		return x.List
	}
	return nil
}

// sheet: HeroStarConfig
type ListHeroStarConfig struct {
	state         protoimpl.MessageState               `protogen:"open.v1"`
	List          []*ListHeroStarConfig_HeroStarConfig `protobuf:"bytes,1,rep,name=list" json:"list,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListHeroStarConfig) Reset() {
	*x = ListHeroStarConfig{}
	mi := &file_gameconfig_proto_msgTypes[30]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListHeroStarConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListHeroStarConfig) ProtoMessage() {}

func (x *ListHeroStarConfig) ProtoReflect() protoreflect.Message {
	mi := &file_gameconfig_proto_msgTypes[30]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListHeroStarConfig.ProtoReflect.Descriptor instead.
func (*ListHeroStarConfig) Descriptor() ([]byte, []int) {
	return file_gameconfig_proto_rawDescGZIP(), []int{30}
}

func (x *ListHeroStarConfig) GetList() []*ListHeroStarConfig_HeroStarConfig {
	if x != nil {
		return x.List
	}
	return nil
}

// sheet: ItemConfig
type ListItemConfig struct {
	state         protoimpl.MessageState       `protogen:"open.v1"`
	List          []*ListItemConfig_ItemConfig `protobuf:"bytes,1,rep,name=list" json:"list,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListItemConfig) Reset() {
	*x = ListItemConfig{}
	mi := &file_gameconfig_proto_msgTypes[31]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListItemConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListItemConfig) ProtoMessage() {}

func (x *ListItemConfig) ProtoReflect() protoreflect.Message {
	mi := &file_gameconfig_proto_msgTypes[31]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListItemConfig.ProtoReflect.Descriptor instead.
func (*ListItemConfig) Descriptor() ([]byte, []int) {
	return file_gameconfig_proto_rawDescGZIP(), []int{31}
}

func (x *ListItemConfig) GetList() []*ListItemConfig_ItemConfig {
	if x != nil {
		return x.List
	}
	return nil
}

// sheet: LevelConfig
type ListLevelConfig struct {
	state         protoimpl.MessageState         `protogen:"open.v1"`
	List          []*ListLevelConfig_LevelConfig `protobuf:"bytes,1,rep,name=list" json:"list,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListLevelConfig) Reset() {
	*x = ListLevelConfig{}
	mi := &file_gameconfig_proto_msgTypes[32]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListLevelConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListLevelConfig) ProtoMessage() {}

func (x *ListLevelConfig) ProtoReflect() protoreflect.Message {
	mi := &file_gameconfig_proto_msgTypes[32]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListLevelConfig.ProtoReflect.Descriptor instead.
func (*ListLevelConfig) Descriptor() ([]byte, []int) {
	return file_gameconfig_proto_rawDescGZIP(), []int{32}
}

func (x *ListLevelConfig) GetList() []*ListLevelConfig_LevelConfig {
	if x != nil {
		return x.List
	}
	return nil
}

// sheet: MailConfig
type ListMailConfig struct {
	state         protoimpl.MessageState       `protogen:"open.v1"`
	List          []*ListMailConfig_MailConfig `protobuf:"bytes,1,rep,name=list" json:"list,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListMailConfig) Reset() {
	*x = ListMailConfig{}
	mi := &file_gameconfig_proto_msgTypes[33]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListMailConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListMailConfig) ProtoMessage() {}

func (x *ListMailConfig) ProtoReflect() protoreflect.Message {
	mi := &file_gameconfig_proto_msgTypes[33]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListMailConfig.ProtoReflect.Descriptor instead.
func (*ListMailConfig) Descriptor() ([]byte, []int) {
	return file_gameconfig_proto_rawDescGZIP(), []int{33}
}

func (x *ListMailConfig) GetList() []*ListMailConfig_MailConfig {
	if x != nil {
		return x.List
	}
	return nil
}

// sheet: MainCityScaleConfig
type ListMainCityScaleConfig struct {
	state         protoimpl.MessageState                         `protogen:"open.v1"`
	List          []*ListMainCityScaleConfig_MainCityScaleConfig `protobuf:"bytes,1,rep,name=list" json:"list,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListMainCityScaleConfig) Reset() {
	*x = ListMainCityScaleConfig{}
	mi := &file_gameconfig_proto_msgTypes[34]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListMainCityScaleConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListMainCityScaleConfig) ProtoMessage() {}

func (x *ListMainCityScaleConfig) ProtoReflect() protoreflect.Message {
	mi := &file_gameconfig_proto_msgTypes[34]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListMainCityScaleConfig.ProtoReflect.Descriptor instead.
func (*ListMainCityScaleConfig) Descriptor() ([]byte, []int) {
	return file_gameconfig_proto_rawDescGZIP(), []int{34}
}

func (x *ListMainCityScaleConfig) GetList() []*ListMainCityScaleConfig_MainCityScaleConfig {
	if x != nil {
		return x.List
	}
	return nil
}

// sheet: MainTaskChapterConfig
type ListMainTaskChapterConfig struct {
	state         protoimpl.MessageState                             `protogen:"open.v1"`
	List          []*ListMainTaskChapterConfig_MainTaskChapterConfig `protobuf:"bytes,1,rep,name=list" json:"list,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListMainTaskChapterConfig) Reset() {
	*x = ListMainTaskChapterConfig{}
	mi := &file_gameconfig_proto_msgTypes[35]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListMainTaskChapterConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListMainTaskChapterConfig) ProtoMessage() {}

func (x *ListMainTaskChapterConfig) ProtoReflect() protoreflect.Message {
	mi := &file_gameconfig_proto_msgTypes[35]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListMainTaskChapterConfig.ProtoReflect.Descriptor instead.
func (*ListMainTaskChapterConfig) Descriptor() ([]byte, []int) {
	return file_gameconfig_proto_rawDescGZIP(), []int{35}
}

func (x *ListMainTaskChapterConfig) GetList() []*ListMainTaskChapterConfig_MainTaskChapterConfig {
	if x != nil {
		return x.List
	}
	return nil
}

// sheet: MainTaskConfig
type ListMainTaskConfig struct {
	state         protoimpl.MessageState               `protogen:"open.v1"`
	List          []*ListMainTaskConfig_MainTaskConfig `protobuf:"bytes,1,rep,name=list" json:"list,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListMainTaskConfig) Reset() {
	*x = ListMainTaskConfig{}
	mi := &file_gameconfig_proto_msgTypes[36]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListMainTaskConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListMainTaskConfig) ProtoMessage() {}

func (x *ListMainTaskConfig) ProtoReflect() protoreflect.Message {
	mi := &file_gameconfig_proto_msgTypes[36]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListMainTaskConfig.ProtoReflect.Descriptor instead.
func (*ListMainTaskConfig) Descriptor() ([]byte, []int) {
	return file_gameconfig_proto_rawDescGZIP(), []int{36}
}

func (x *ListMainTaskConfig) GetList() []*ListMainTaskConfig_MainTaskConfig {
	if x != nil {
		return x.List
	}
	return nil
}

// sheet: MapConfig
type ListMapConfig struct {
	state         protoimpl.MessageState     `protogen:"open.v1"`
	List          []*ListMapConfig_MapConfig `protobuf:"bytes,1,rep,name=list" json:"list,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListMapConfig) Reset() {
	*x = ListMapConfig{}
	mi := &file_gameconfig_proto_msgTypes[37]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListMapConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListMapConfig) ProtoMessage() {}

func (x *ListMapConfig) ProtoReflect() protoreflect.Message {
	mi := &file_gameconfig_proto_msgTypes[37]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListMapConfig.ProtoReflect.Descriptor instead.
func (*ListMapConfig) Descriptor() ([]byte, []int) {
	return file_gameconfig_proto_rawDescGZIP(), []int{37}
}

func (x *ListMapConfig) GetList() []*ListMapConfig_MapConfig {
	if x != nil {
		return x.List
	}
	return nil
}

// sheet: MiniGameConfig
type ListMiniGameConfig struct {
	state         protoimpl.MessageState               `protogen:"open.v1"`
	List          []*ListMiniGameConfig_MiniGameConfig `protobuf:"bytes,1,rep,name=list" json:"list,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListMiniGameConfig) Reset() {
	*x = ListMiniGameConfig{}
	mi := &file_gameconfig_proto_msgTypes[38]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListMiniGameConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListMiniGameConfig) ProtoMessage() {}

func (x *ListMiniGameConfig) ProtoReflect() protoreflect.Message {
	mi := &file_gameconfig_proto_msgTypes[38]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListMiniGameConfig.ProtoReflect.Descriptor instead.
func (*ListMiniGameConfig) Descriptor() ([]byte, []int) {
	return file_gameconfig_proto_rawDescGZIP(), []int{38}
}

func (x *ListMiniGameConfig) GetList() []*ListMiniGameConfig_MiniGameConfig {
	if x != nil {
		return x.List
	}
	return nil
}

// sheet: MUnitConfig
type ListMUnitConfig struct {
	state         protoimpl.MessageState         `protogen:"open.v1"`
	List          []*ListMUnitConfig_MUnitConfig `protobuf:"bytes,1,rep,name=list" json:"list,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListMUnitConfig) Reset() {
	*x = ListMUnitConfig{}
	mi := &file_gameconfig_proto_msgTypes[39]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListMUnitConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListMUnitConfig) ProtoMessage() {}

func (x *ListMUnitConfig) ProtoReflect() protoreflect.Message {
	mi := &file_gameconfig_proto_msgTypes[39]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListMUnitConfig.ProtoReflect.Descriptor instead.
func (*ListMUnitConfig) Descriptor() ([]byte, []int) {
	return file_gameconfig_proto_rawDescGZIP(), []int{39}
}

func (x *ListMUnitConfig) GetList() []*ListMUnitConfig_MUnitConfig {
	if x != nil {
		return x.List
	}
	return nil
}

// sheet: RankConfig
type ListRankConfig struct {
	state         protoimpl.MessageState       `protogen:"open.v1"`
	List          []*ListRankConfig_RankConfig `protobuf:"bytes,1,rep,name=list" json:"list,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListRankConfig) Reset() {
	*x = ListRankConfig{}
	mi := &file_gameconfig_proto_msgTypes[40]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListRankConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListRankConfig) ProtoMessage() {}

func (x *ListRankConfig) ProtoReflect() protoreflect.Message {
	mi := &file_gameconfig_proto_msgTypes[40]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListRankConfig.ProtoReflect.Descriptor instead.
func (*ListRankConfig) Descriptor() ([]byte, []int) {
	return file_gameconfig_proto_rawDescGZIP(), []int{40}
}

func (x *ListRankConfig) GetList() []*ListRankConfig_RankConfig {
	if x != nil {
		return x.List
	}
	return nil
}

// sheet: RechargeConfig
type ListRechargeConfig struct {
	state         protoimpl.MessageState               `protogen:"open.v1"`
	List          []*ListRechargeConfig_RechargeConfig `protobuf:"bytes,1,rep,name=list" json:"list,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListRechargeConfig) Reset() {
	*x = ListRechargeConfig{}
	mi := &file_gameconfig_proto_msgTypes[41]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListRechargeConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListRechargeConfig) ProtoMessage() {}

func (x *ListRechargeConfig) ProtoReflect() protoreflect.Message {
	mi := &file_gameconfig_proto_msgTypes[41]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListRechargeConfig.ProtoReflect.Descriptor instead.
func (*ListRechargeConfig) Descriptor() ([]byte, []int) {
	return file_gameconfig_proto_rawDescGZIP(), []int{41}
}

func (x *ListRechargeConfig) GetList() []*ListRechargeConfig_RechargeConfig {
	if x != nil {
		return x.List
	}
	return nil
}

// sheet: ShopGoodsConfig
type ListShopGoodsConfig struct {
	state         protoimpl.MessageState                 `protogen:"open.v1"`
	List          []*ListShopGoodsConfig_ShopGoodsConfig `protobuf:"bytes,1,rep,name=list" json:"list,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListShopGoodsConfig) Reset() {
	*x = ListShopGoodsConfig{}
	mi := &file_gameconfig_proto_msgTypes[42]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListShopGoodsConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListShopGoodsConfig) ProtoMessage() {}

func (x *ListShopGoodsConfig) ProtoReflect() protoreflect.Message {
	mi := &file_gameconfig_proto_msgTypes[42]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListShopGoodsConfig.ProtoReflect.Descriptor instead.
func (*ListShopGoodsConfig) Descriptor() ([]byte, []int) {
	return file_gameconfig_proto_rawDescGZIP(), []int{42}
}

func (x *ListShopGoodsConfig) GetList() []*ListShopGoodsConfig_ShopGoodsConfig {
	if x != nil {
		return x.List
	}
	return nil
}

// sheet: ShopTabConfig
type ListShopTabConfig struct {
	state         protoimpl.MessageState             `protogen:"open.v1"`
	List          []*ListShopTabConfig_ShopTabConfig `protobuf:"bytes,1,rep,name=list" json:"list,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListShopTabConfig) Reset() {
	*x = ListShopTabConfig{}
	mi := &file_gameconfig_proto_msgTypes[43]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListShopTabConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListShopTabConfig) ProtoMessage() {}

func (x *ListShopTabConfig) ProtoReflect() protoreflect.Message {
	mi := &file_gameconfig_proto_msgTypes[43]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListShopTabConfig.ProtoReflect.Descriptor instead.
func (*ListShopTabConfig) Descriptor() ([]byte, []int) {
	return file_gameconfig_proto_rawDescGZIP(), []int{43}
}

func (x *ListShopTabConfig) GetList() []*ListShopTabConfig_ShopTabConfig {
	if x != nil {
		return x.List
	}
	return nil
}

// sheet: StoryPlotConditionConfig
type ListStoryPlotConditionConfig struct {
	state         protoimpl.MessageState                                   `protogen:"open.v1"`
	List          []*ListStoryPlotConditionConfig_StoryPlotConditionConfig `protobuf:"bytes,1,rep,name=list" json:"list,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListStoryPlotConditionConfig) Reset() {
	*x = ListStoryPlotConditionConfig{}
	mi := &file_gameconfig_proto_msgTypes[44]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListStoryPlotConditionConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListStoryPlotConditionConfig) ProtoMessage() {}

func (x *ListStoryPlotConditionConfig) ProtoReflect() protoreflect.Message {
	mi := &file_gameconfig_proto_msgTypes[44]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListStoryPlotConditionConfig.ProtoReflect.Descriptor instead.
func (*ListStoryPlotConditionConfig) Descriptor() ([]byte, []int) {
	return file_gameconfig_proto_rawDescGZIP(), []int{44}
}

func (x *ListStoryPlotConditionConfig) GetList() []*ListStoryPlotConditionConfig_StoryPlotConditionConfig {
	if x != nil {
		return x.List
	}
	return nil
}

// sheet: StoryPlotConfig
type ListStoryPlotConfig struct {
	state         protoimpl.MessageState                 `protogen:"open.v1"`
	List          []*ListStoryPlotConfig_StoryPlotConfig `protobuf:"bytes,1,rep,name=list" json:"list,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListStoryPlotConfig) Reset() {
	*x = ListStoryPlotConfig{}
	mi := &file_gameconfig_proto_msgTypes[45]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListStoryPlotConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListStoryPlotConfig) ProtoMessage() {}

func (x *ListStoryPlotConfig) ProtoReflect() protoreflect.Message {
	mi := &file_gameconfig_proto_msgTypes[45]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListStoryPlotConfig.ProtoReflect.Descriptor instead.
func (*ListStoryPlotConfig) Descriptor() ([]byte, []int) {
	return file_gameconfig_proto_rawDescGZIP(), []int{45}
}

func (x *ListStoryPlotConfig) GetList() []*ListStoryPlotConfig_StoryPlotConfig {
	if x != nil {
		return x.List
	}
	return nil
}

// sheet: StoryPlotGroupConfig
type ListStoryPlotGroupConfig struct {
	state         protoimpl.MessageState                           `protogen:"open.v1"`
	List          []*ListStoryPlotGroupConfig_StoryPlotGroupConfig `protobuf:"bytes,1,rep,name=list" json:"list,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListStoryPlotGroupConfig) Reset() {
	*x = ListStoryPlotGroupConfig{}
	mi := &file_gameconfig_proto_msgTypes[46]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListStoryPlotGroupConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListStoryPlotGroupConfig) ProtoMessage() {}

func (x *ListStoryPlotGroupConfig) ProtoReflect() protoreflect.Message {
	mi := &file_gameconfig_proto_msgTypes[46]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListStoryPlotGroupConfig.ProtoReflect.Descriptor instead.
func (*ListStoryPlotGroupConfig) Descriptor() ([]byte, []int) {
	return file_gameconfig_proto_rawDescGZIP(), []int{46}
}

func (x *ListStoryPlotGroupConfig) GetList() []*ListStoryPlotGroupConfig_StoryPlotGroupConfig {
	if x != nil {
		return x.List
	}
	return nil
}

// sheet: StoryPlotInstructsConfig
type ListStoryPlotInstructsConfig struct {
	state         protoimpl.MessageState                                   `protogen:"open.v1"`
	List          []*ListStoryPlotInstructsConfig_StoryPlotInstructsConfig `protobuf:"bytes,1,rep,name=list" json:"list,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListStoryPlotInstructsConfig) Reset() {
	*x = ListStoryPlotInstructsConfig{}
	mi := &file_gameconfig_proto_msgTypes[47]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListStoryPlotInstructsConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListStoryPlotInstructsConfig) ProtoMessage() {}

func (x *ListStoryPlotInstructsConfig) ProtoReflect() protoreflect.Message {
	mi := &file_gameconfig_proto_msgTypes[47]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListStoryPlotInstructsConfig.ProtoReflect.Descriptor instead.
func (*ListStoryPlotInstructsConfig) Descriptor() ([]byte, []int) {
	return file_gameconfig_proto_rawDescGZIP(), []int{47}
}

func (x *ListStoryPlotInstructsConfig) GetList() []*ListStoryPlotInstructsConfig_StoryPlotInstructsConfig {
	if x != nil {
		return x.List
	}
	return nil
}

// sheet: SystemOpenConfig
type ListSystemOpenConfig struct {
	state         protoimpl.MessageState                   `protogen:"open.v1"`
	List          []*ListSystemOpenConfig_SystemOpenConfig `protobuf:"bytes,1,rep,name=list" json:"list,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListSystemOpenConfig) Reset() {
	*x = ListSystemOpenConfig{}
	mi := &file_gameconfig_proto_msgTypes[48]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListSystemOpenConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListSystemOpenConfig) ProtoMessage() {}

func (x *ListSystemOpenConfig) ProtoReflect() protoreflect.Message {
	mi := &file_gameconfig_proto_msgTypes[48]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListSystemOpenConfig.ProtoReflect.Descriptor instead.
func (*ListSystemOpenConfig) Descriptor() ([]byte, []int) {
	return file_gameconfig_proto_rawDescGZIP(), []int{48}
}

func (x *ListSystemOpenConfig) GetList() []*ListSystemOpenConfig_SystemOpenConfig {
	if x != nil {
		return x.List
	}
	return nil
}

// sheet: TagConfig
type ListTagConfig struct {
	state         protoimpl.MessageState     `protogen:"open.v1"`
	List          []*ListTagConfig_TagConfig `protobuf:"bytes,1,rep,name=list" json:"list,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListTagConfig) Reset() {
	*x = ListTagConfig{}
	mi := &file_gameconfig_proto_msgTypes[49]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListTagConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListTagConfig) ProtoMessage() {}

func (x *ListTagConfig) ProtoReflect() protoreflect.Message {
	mi := &file_gameconfig_proto_msgTypes[49]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListTagConfig.ProtoReflect.Descriptor instead.
func (*ListTagConfig) Descriptor() ([]byte, []int) {
	return file_gameconfig_proto_rawDescGZIP(), []int{49}
}

func (x *ListTagConfig) GetList() []*ListTagConfig_TagConfig {
	if x != nil {
		return x.List
	}
	return nil
}

// sheet: TileMapConfig
type ListTileMapConfig struct {
	state         protoimpl.MessageState             `protogen:"open.v1"`
	List          []*ListTileMapConfig_TileMapConfig `protobuf:"bytes,1,rep,name=list" json:"list,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListTileMapConfig) Reset() {
	*x = ListTileMapConfig{}
	mi := &file_gameconfig_proto_msgTypes[50]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListTileMapConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListTileMapConfig) ProtoMessage() {}

func (x *ListTileMapConfig) ProtoReflect() protoreflect.Message {
	mi := &file_gameconfig_proto_msgTypes[50]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListTileMapConfig.ProtoReflect.Descriptor instead.
func (*ListTileMapConfig) Descriptor() ([]byte, []int) {
	return file_gameconfig_proto_rawDescGZIP(), []int{50}
}

func (x *ListTileMapConfig) GetList() []*ListTileMapConfig_TileMapConfig {
	if x != nil {
		return x.List
	}
	return nil
}

// sheet: UserNameConfig
type ListUserNameConfig struct {
	state         protoimpl.MessageState               `protogen:"open.v1"`
	List          []*ListUserNameConfig_UserNameConfig `protobuf:"bytes,1,rep,name=list" json:"list,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListUserNameConfig) Reset() {
	*x = ListUserNameConfig{}
	mi := &file_gameconfig_proto_msgTypes[51]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListUserNameConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListUserNameConfig) ProtoMessage() {}

func (x *ListUserNameConfig) ProtoReflect() protoreflect.Message {
	mi := &file_gameconfig_proto_msgTypes[51]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListUserNameConfig.ProtoReflect.Descriptor instead.
func (*ListUserNameConfig) Descriptor() ([]byte, []int) {
	return file_gameconfig_proto_rawDescGZIP(), []int{51}
}

func (x *ListUserNameConfig) GetList() []*ListUserNameConfig_UserNameConfig {
	if x != nil {
		return x.List
	}
	return nil
}

type ListAIActionConfig_AIActionConfig struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            *int32                 `protobuf:"varint,2,opt,name=Id" json:"Id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListAIActionConfig_AIActionConfig) Reset() {
	*x = ListAIActionConfig_AIActionConfig{}
	mi := &file_gameconfig_proto_msgTypes[52]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListAIActionConfig_AIActionConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListAIActionConfig_AIActionConfig) ProtoMessage() {}

func (x *ListAIActionConfig_AIActionConfig) ProtoReflect() protoreflect.Message {
	mi := &file_gameconfig_proto_msgTypes[52]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListAIActionConfig_AIActionConfig.ProtoReflect.Descriptor instead.
func (*ListAIActionConfig_AIActionConfig) Descriptor() ([]byte, []int) {
	return file_gameconfig_proto_rawDescGZIP(), []int{13, 0}
}

func (x *ListAIActionConfig_AIActionConfig) GetId() int32 {
	if x != nil && x.Id != nil {
		return *x.Id
	}
	return 0
}

type ListAIConditionConfig_AIConditionConfig struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            *int32                 `protobuf:"varint,2,opt,name=Id" json:"Id,omitempty"`
	Type          *EAIConditionType      `protobuf:"varint,3,opt,name=Type,enum=pb.EAIConditionType" json:"Type,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListAIConditionConfig_AIConditionConfig) Reset() {
	*x = ListAIConditionConfig_AIConditionConfig{}
	mi := &file_gameconfig_proto_msgTypes[53]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListAIConditionConfig_AIConditionConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListAIConditionConfig_AIConditionConfig) ProtoMessage() {}

func (x *ListAIConditionConfig_AIConditionConfig) ProtoReflect() protoreflect.Message {
	mi := &file_gameconfig_proto_msgTypes[53]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListAIConditionConfig_AIConditionConfig.ProtoReflect.Descriptor instead.
func (*ListAIConditionConfig_AIConditionConfig) Descriptor() ([]byte, []int) {
	return file_gameconfig_proto_rawDescGZIP(), []int{14, 0}
}

func (x *ListAIConditionConfig_AIConditionConfig) GetId() int32 {
	if x != nil && x.Id != nil {
		return *x.Id
	}
	return 0
}

func (x *ListAIConditionConfig_AIConditionConfig) GetType() EAIConditionType {
	if x != nil && x.Type != nil {
		return *x.Type
	}
	return EAIConditionType_EAIConditionType_None
}

type ListAINodeConfig_AINodeConfig struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            *int32                 `protobuf:"varint,2,opt,name=Id" json:"Id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListAINodeConfig_AINodeConfig) Reset() {
	*x = ListAINodeConfig_AINodeConfig{}
	mi := &file_gameconfig_proto_msgTypes[54]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListAINodeConfig_AINodeConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListAINodeConfig_AINodeConfig) ProtoMessage() {}

func (x *ListAINodeConfig_AINodeConfig) ProtoReflect() protoreflect.Message {
	mi := &file_gameconfig_proto_msgTypes[54]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListAINodeConfig_AINodeConfig.ProtoReflect.Descriptor instead.
func (*ListAINodeConfig_AINodeConfig) Descriptor() ([]byte, []int) {
	return file_gameconfig_proto_rawDescGZIP(), []int{15, 0}
}

func (x *ListAINodeConfig_AINodeConfig) GetId() int32 {
	if x != nil && x.Id != nil {
		return *x.Id
	}
	return 0
}

type ListAvatarConfig_AvatarConfig struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            *int32                 `protobuf:"varint,2,opt,name=Id" json:"Id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListAvatarConfig_AvatarConfig) Reset() {
	*x = ListAvatarConfig_AvatarConfig{}
	mi := &file_gameconfig_proto_msgTypes[55]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListAvatarConfig_AvatarConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListAvatarConfig_AvatarConfig) ProtoMessage() {}

func (x *ListAvatarConfig_AvatarConfig) ProtoReflect() protoreflect.Message {
	mi := &file_gameconfig_proto_msgTypes[55]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListAvatarConfig_AvatarConfig.ProtoReflect.Descriptor instead.
func (*ListAvatarConfig_AvatarConfig) Descriptor() ([]byte, []int) {
	return file_gameconfig_proto_rawDescGZIP(), []int{16, 0}
}

func (x *ListAvatarConfig_AvatarConfig) GetId() int32 {
	if x != nil && x.Id != nil {
		return *x.Id
	}
	return 0
}

type ListAwardConfig_AwardConfig struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            *int32                 `protobuf:"varint,2,opt,name=Id" json:"Id,omitempty"`
	Type          *EAwardType            `protobuf:"varint,4,opt,name=Type,enum=pb.EAwardType" json:"Type,omitempty"`
	Award         []*AwardGroupData      `protobuf:"bytes,5,rep,name=Award" json:"Award,omitempty"`
	Count         *int32                 `protobuf:"varint,6,opt,name=Count" json:"Count,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListAwardConfig_AwardConfig) Reset() {
	*x = ListAwardConfig_AwardConfig{}
	mi := &file_gameconfig_proto_msgTypes[56]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListAwardConfig_AwardConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListAwardConfig_AwardConfig) ProtoMessage() {}

func (x *ListAwardConfig_AwardConfig) ProtoReflect() protoreflect.Message {
	mi := &file_gameconfig_proto_msgTypes[56]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListAwardConfig_AwardConfig.ProtoReflect.Descriptor instead.
func (*ListAwardConfig_AwardConfig) Descriptor() ([]byte, []int) {
	return file_gameconfig_proto_rawDescGZIP(), []int{17, 0}
}

func (x *ListAwardConfig_AwardConfig) GetId() int32 {
	if x != nil && x.Id != nil {
		return *x.Id
	}
	return 0
}

func (x *ListAwardConfig_AwardConfig) GetType() EAwardType {
	if x != nil && x.Type != nil {
		return *x.Type
	}
	return EAwardType_EAwardType_None
}

func (x *ListAwardConfig_AwardConfig) GetAward() []*AwardGroupData {
	if x != nil {
		return x.Award
	}
	return nil
}

func (x *ListAwardConfig_AwardConfig) GetCount() int32 {
	if x != nil && x.Count != nil {
		return *x.Count
	}
	return 0
}

type ListAwardGroupConfig_AwardGroupConfig struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            *int32                 `protobuf:"varint,2,opt,name=Id" json:"Id,omitempty"`
	GroupId       *int32                 `protobuf:"varint,4,opt,name=GroupId" json:"GroupId,omitempty"`
	Type          *EAwardGroupType       `protobuf:"varint,5,opt,name=Type,enum=pb.EAwardGroupType" json:"Type,omitempty"`
	ItemId        *int32                 `protobuf:"varint,6,opt,name=ItemId" json:"ItemId,omitempty"`
	Count         *int64                 `protobuf:"varint,8,opt,name=Count" json:"Count,omitempty"`
	Value         *int32                 `protobuf:"varint,9,opt,name=Value" json:"Value,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListAwardGroupConfig_AwardGroupConfig) Reset() {
	*x = ListAwardGroupConfig_AwardGroupConfig{}
	mi := &file_gameconfig_proto_msgTypes[57]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListAwardGroupConfig_AwardGroupConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListAwardGroupConfig_AwardGroupConfig) ProtoMessage() {}

func (x *ListAwardGroupConfig_AwardGroupConfig) ProtoReflect() protoreflect.Message {
	mi := &file_gameconfig_proto_msgTypes[57]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListAwardGroupConfig_AwardGroupConfig.ProtoReflect.Descriptor instead.
func (*ListAwardGroupConfig_AwardGroupConfig) Descriptor() ([]byte, []int) {
	return file_gameconfig_proto_rawDescGZIP(), []int{18, 0}
}

func (x *ListAwardGroupConfig_AwardGroupConfig) GetId() int32 {
	if x != nil && x.Id != nil {
		return *x.Id
	}
	return 0
}

func (x *ListAwardGroupConfig_AwardGroupConfig) GetGroupId() int32 {
	if x != nil && x.GroupId != nil {
		return *x.GroupId
	}
	return 0
}

func (x *ListAwardGroupConfig_AwardGroupConfig) GetType() EAwardGroupType {
	if x != nil && x.Type != nil {
		return *x.Type
	}
	return EAwardGroupType_EAwardGroupType_None
}

func (x *ListAwardGroupConfig_AwardGroupConfig) GetItemId() int32 {
	if x != nil && x.ItemId != nil {
		return *x.ItemId
	}
	return 0
}

func (x *ListAwardGroupConfig_AwardGroupConfig) GetCount() int64 {
	if x != nil && x.Count != nil {
		return *x.Count
	}
	return 0
}

func (x *ListAwardGroupConfig_AwardGroupConfig) GetValue() int32 {
	if x != nil && x.Value != nil {
		return *x.Value
	}
	return 0
}

type ListConditionConfig_ConditionConfig struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            *int32                 `protobuf:"varint,2,opt,name=Id" json:"Id,omitempty"`
	Type          *EConditionType        `protobuf:"varint,5,opt,name=Type,enum=pb.EConditionType" json:"Type,omitempty"`
	Target        *int32                 `protobuf:"varint,6,opt,name=Target" json:"Target,omitempty"`
	Param1        *int32                 `protobuf:"varint,7,opt,name=Param1" json:"Param1,omitempty"`
	Param2        *int32                 `protobuf:"varint,8,opt,name=Param2" json:"Param2,omitempty"`
	Param3        *int32                 `protobuf:"varint,9,opt,name=Param3" json:"Param3,omitempty"`
	Param4        *int32                 `protobuf:"varint,10,opt,name=Param4" json:"Param4,omitempty"`
	CondVersion   *int32                 `protobuf:"varint,11,opt,name=CondVersion" json:"CondVersion,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListConditionConfig_ConditionConfig) Reset() {
	*x = ListConditionConfig_ConditionConfig{}
	mi := &file_gameconfig_proto_msgTypes[58]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListConditionConfig_ConditionConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListConditionConfig_ConditionConfig) ProtoMessage() {}

func (x *ListConditionConfig_ConditionConfig) ProtoReflect() protoreflect.Message {
	mi := &file_gameconfig_proto_msgTypes[58]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListConditionConfig_ConditionConfig.ProtoReflect.Descriptor instead.
func (*ListConditionConfig_ConditionConfig) Descriptor() ([]byte, []int) {
	return file_gameconfig_proto_rawDescGZIP(), []int{19, 0}
}

func (x *ListConditionConfig_ConditionConfig) GetId() int32 {
	if x != nil && x.Id != nil {
		return *x.Id
	}
	return 0
}

func (x *ListConditionConfig_ConditionConfig) GetType() EConditionType {
	if x != nil && x.Type != nil {
		return *x.Type
	}
	return EConditionType_EConditionType_None
}

func (x *ListConditionConfig_ConditionConfig) GetTarget() int32 {
	if x != nil && x.Target != nil {
		return *x.Target
	}
	return 0
}

func (x *ListConditionConfig_ConditionConfig) GetParam1() int32 {
	if x != nil && x.Param1 != nil {
		return *x.Param1
	}
	return 0
}

func (x *ListConditionConfig_ConditionConfig) GetParam2() int32 {
	if x != nil && x.Param2 != nil {
		return *x.Param2
	}
	return 0
}

func (x *ListConditionConfig_ConditionConfig) GetParam3() int32 {
	if x != nil && x.Param3 != nil {
		return *x.Param3
	}
	return 0
}

func (x *ListConditionConfig_ConditionConfig) GetParam4() int32 {
	if x != nil && x.Param4 != nil {
		return *x.Param4
	}
	return 0
}

func (x *ListConditionConfig_ConditionConfig) GetCondVersion() int32 {
	if x != nil && x.CondVersion != nil {
		return *x.CondVersion
	}
	return 0
}

type ListConditionTriggerConfig_ConditionTriggerConfig struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            *int32                 `protobuf:"varint,2,opt,name=Id" json:"Id,omitempty"`
	CondType      *EConditionType        `protobuf:"varint,4,opt,name=CondType,enum=pb.EConditionType" json:"CondType,omitempty"`
	CondTarget    *int32                 `protobuf:"varint,5,opt,name=CondTarget" json:"CondTarget,omitempty"`
	CondParams    []int32                `protobuf:"varint,6,rep,name=CondParams" json:"CondParams,omitempty"`
	CondVersion   *int32                 `protobuf:"varint,7,opt,name=CondVersion" json:"CondVersion,omitempty"`
	TiggerType    *ECondTriggerType      `protobuf:"varint,8,opt,name=TiggerType,enum=pb.ECondTriggerType" json:"TiggerType,omitempty"`
	TriggerParams []string               `protobuf:"bytes,9,rep,name=TriggerParams" json:"TriggerParams,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListConditionTriggerConfig_ConditionTriggerConfig) Reset() {
	*x = ListConditionTriggerConfig_ConditionTriggerConfig{}
	mi := &file_gameconfig_proto_msgTypes[59]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListConditionTriggerConfig_ConditionTriggerConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListConditionTriggerConfig_ConditionTriggerConfig) ProtoMessage() {}

func (x *ListConditionTriggerConfig_ConditionTriggerConfig) ProtoReflect() protoreflect.Message {
	mi := &file_gameconfig_proto_msgTypes[59]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListConditionTriggerConfig_ConditionTriggerConfig.ProtoReflect.Descriptor instead.
func (*ListConditionTriggerConfig_ConditionTriggerConfig) Descriptor() ([]byte, []int) {
	return file_gameconfig_proto_rawDescGZIP(), []int{20, 0}
}

func (x *ListConditionTriggerConfig_ConditionTriggerConfig) GetId() int32 {
	if x != nil && x.Id != nil {
		return *x.Id
	}
	return 0
}

func (x *ListConditionTriggerConfig_ConditionTriggerConfig) GetCondType() EConditionType {
	if x != nil && x.CondType != nil {
		return *x.CondType
	}
	return EConditionType_EConditionType_None
}

func (x *ListConditionTriggerConfig_ConditionTriggerConfig) GetCondTarget() int32 {
	if x != nil && x.CondTarget != nil {
		return *x.CondTarget
	}
	return 0
}

func (x *ListConditionTriggerConfig_ConditionTriggerConfig) GetCondParams() []int32 {
	if x != nil {
		return x.CondParams
	}
	return nil
}

func (x *ListConditionTriggerConfig_ConditionTriggerConfig) GetCondVersion() int32 {
	if x != nil && x.CondVersion != nil {
		return *x.CondVersion
	}
	return 0
}

func (x *ListConditionTriggerConfig_ConditionTriggerConfig) GetTiggerType() ECondTriggerType {
	if x != nil && x.TiggerType != nil {
		return *x.TiggerType
	}
	return ECondTriggerType_ECondTriggerType_None
}

func (x *ListConditionTriggerConfig_ConditionTriggerConfig) GetTriggerParams() []string {
	if x != nil {
		return x.TriggerParams
	}
	return nil
}

type ListConstConfig_ConstConfig struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            *int32                 `protobuf:"varint,2,opt,name=Id" json:"Id,omitempty"`
	VInt          *int32                 `protobuf:"varint,4,opt,name=VInt" json:"VInt,omitempty"`
	VLong         *int64                 `protobuf:"varint,5,opt,name=VLong" json:"VLong,omitempty"`
	VIntList      []int32                `protobuf:"varint,6,rep,name=VIntList" json:"VIntList,omitempty"`
	VIntLList     []*XVInt32             `protobuf:"bytes,7,rep,name=VIntLList" json:"VIntLList,omitempty"`
	VString       *string                `protobuf:"bytes,8,opt,name=VString" json:"VString,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListConstConfig_ConstConfig) Reset() {
	*x = ListConstConfig_ConstConfig{}
	mi := &file_gameconfig_proto_msgTypes[60]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListConstConfig_ConstConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListConstConfig_ConstConfig) ProtoMessage() {}

func (x *ListConstConfig_ConstConfig) ProtoReflect() protoreflect.Message {
	mi := &file_gameconfig_proto_msgTypes[60]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListConstConfig_ConstConfig.ProtoReflect.Descriptor instead.
func (*ListConstConfig_ConstConfig) Descriptor() ([]byte, []int) {
	return file_gameconfig_proto_rawDescGZIP(), []int{21, 0}
}

func (x *ListConstConfig_ConstConfig) GetId() int32 {
	if x != nil && x.Id != nil {
		return *x.Id
	}
	return 0
}

func (x *ListConstConfig_ConstConfig) GetVInt() int32 {
	if x != nil && x.VInt != nil {
		return *x.VInt
	}
	return 0
}

func (x *ListConstConfig_ConstConfig) GetVLong() int64 {
	if x != nil && x.VLong != nil {
		return *x.VLong
	}
	return 0
}

func (x *ListConstConfig_ConstConfig) GetVIntList() []int32 {
	if x != nil {
		return x.VIntList
	}
	return nil
}

func (x *ListConstConfig_ConstConfig) GetVIntLList() []*XVInt32 {
	if x != nil {
		return x.VIntLList
	}
	return nil
}

func (x *ListConstConfig_ConstConfig) GetVString() string {
	if x != nil && x.VString != nil {
		return *x.VString
	}
	return ""
}

type ListConstShopGoodsConfig_ConstShopGoodsConfig struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            *int32                 `protobuf:"varint,2,opt,name=Id" json:"Id,omitempty"`
	ItemData      *ItemData              `protobuf:"bytes,6,opt,name=ItemData" json:"ItemData,omitempty"`
	CostItem      *ItemData              `protobuf:"bytes,7,opt,name=CostItem" json:"CostItem,omitempty"`
	LimitType     *EShopBuyLimit         `protobuf:"varint,8,opt,name=LimitType,enum=pb.EShopBuyLimit" json:"LimitType,omitempty"`
	LimitCount    *int32                 `protobuf:"varint,9,opt,name=LimitCount" json:"LimitCount,omitempty"`
	CondType      *EConditionType        `protobuf:"varint,10,opt,name=CondType,enum=pb.EConditionType" json:"CondType,omitempty"`
	CondTarget    *int32                 `protobuf:"varint,11,opt,name=CondTarget" json:"CondTarget,omitempty"`
	CondParams    []int32                `protobuf:"varint,12,rep,name=CondParams" json:"CondParams,omitempty"`
	CondVersion   *int32                 `protobuf:"varint,13,opt,name=CondVersion" json:"CondVersion,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListConstShopGoodsConfig_ConstShopGoodsConfig) Reset() {
	*x = ListConstShopGoodsConfig_ConstShopGoodsConfig{}
	mi := &file_gameconfig_proto_msgTypes[61]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListConstShopGoodsConfig_ConstShopGoodsConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListConstShopGoodsConfig_ConstShopGoodsConfig) ProtoMessage() {}

func (x *ListConstShopGoodsConfig_ConstShopGoodsConfig) ProtoReflect() protoreflect.Message {
	mi := &file_gameconfig_proto_msgTypes[61]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListConstShopGoodsConfig_ConstShopGoodsConfig.ProtoReflect.Descriptor instead.
func (*ListConstShopGoodsConfig_ConstShopGoodsConfig) Descriptor() ([]byte, []int) {
	return file_gameconfig_proto_rawDescGZIP(), []int{22, 0}
}

func (x *ListConstShopGoodsConfig_ConstShopGoodsConfig) GetId() int32 {
	if x != nil && x.Id != nil {
		return *x.Id
	}
	return 0
}

func (x *ListConstShopGoodsConfig_ConstShopGoodsConfig) GetItemData() *ItemData {
	if x != nil {
		return x.ItemData
	}
	return nil
}

func (x *ListConstShopGoodsConfig_ConstShopGoodsConfig) GetCostItem() *ItemData {
	if x != nil {
		return x.CostItem
	}
	return nil
}

func (x *ListConstShopGoodsConfig_ConstShopGoodsConfig) GetLimitType() EShopBuyLimit {
	if x != nil && x.LimitType != nil {
		return *x.LimitType
	}
	return EShopBuyLimit_EShopBuyLimit_None
}

func (x *ListConstShopGoodsConfig_ConstShopGoodsConfig) GetLimitCount() int32 {
	if x != nil && x.LimitCount != nil {
		return *x.LimitCount
	}
	return 0
}

func (x *ListConstShopGoodsConfig_ConstShopGoodsConfig) GetCondType() EConditionType {
	if x != nil && x.CondType != nil {
		return *x.CondType
	}
	return EConditionType_EConditionType_None
}

func (x *ListConstShopGoodsConfig_ConstShopGoodsConfig) GetCondTarget() int32 {
	if x != nil && x.CondTarget != nil {
		return *x.CondTarget
	}
	return 0
}

func (x *ListConstShopGoodsConfig_ConstShopGoodsConfig) GetCondParams() []int32 {
	if x != nil {
		return x.CondParams
	}
	return nil
}

func (x *ListConstShopGoodsConfig_ConstShopGoodsConfig) GetCondVersion() int32 {
	if x != nil && x.CondVersion != nil {
		return *x.CondVersion
	}
	return 0
}

type ListGainItemConfig_GainItemConfig struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            *int32                 `protobuf:"varint,2,opt,name=Id" json:"Id,omitempty"`
	EffectType    *EGainItemEffect       `protobuf:"varint,4,opt,name=EffectType,enum=pb.EGainItemEffect" json:"EffectType,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListGainItemConfig_GainItemConfig) Reset() {
	*x = ListGainItemConfig_GainItemConfig{}
	mi := &file_gameconfig_proto_msgTypes[62]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListGainItemConfig_GainItemConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListGainItemConfig_GainItemConfig) ProtoMessage() {}

func (x *ListGainItemConfig_GainItemConfig) ProtoReflect() protoreflect.Message {
	mi := &file_gameconfig_proto_msgTypes[62]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListGainItemConfig_GainItemConfig.ProtoReflect.Descriptor instead.
func (*ListGainItemConfig_GainItemConfig) Descriptor() ([]byte, []int) {
	return file_gameconfig_proto_rawDescGZIP(), []int{23, 0}
}

func (x *ListGainItemConfig_GainItemConfig) GetId() int32 {
	if x != nil && x.Id != nil {
		return *x.Id
	}
	return 0
}

func (x *ListGainItemConfig_GainItemConfig) GetEffectType() EGainItemEffect {
	if x != nil && x.EffectType != nil {
		return *x.EffectType
	}
	return EGainItemEffect_EGainItemEffect_None
}

type ListGuideConfig_GuideConfig struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            *int32                 `protobuf:"varint,2,opt,name=Id" json:"Id,omitempty"`
	GroupId       *int32                 `protobuf:"varint,3,opt,name=GroupId" json:"GroupId,omitempty"`
	Type          *EGuide                `protobuf:"varint,6,opt,name=Type,enum=pb.EGuide" json:"Type,omitempty"`
	Params        []string               `protobuf:"bytes,9,rep,name=Params" json:"Params,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListGuideConfig_GuideConfig) Reset() {
	*x = ListGuideConfig_GuideConfig{}
	mi := &file_gameconfig_proto_msgTypes[63]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListGuideConfig_GuideConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListGuideConfig_GuideConfig) ProtoMessage() {}

func (x *ListGuideConfig_GuideConfig) ProtoReflect() protoreflect.Message {
	mi := &file_gameconfig_proto_msgTypes[63]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListGuideConfig_GuideConfig.ProtoReflect.Descriptor instead.
func (*ListGuideConfig_GuideConfig) Descriptor() ([]byte, []int) {
	return file_gameconfig_proto_rawDescGZIP(), []int{24, 0}
}

func (x *ListGuideConfig_GuideConfig) GetId() int32 {
	if x != nil && x.Id != nil {
		return *x.Id
	}
	return 0
}

func (x *ListGuideConfig_GuideConfig) GetGroupId() int32 {
	if x != nil && x.GroupId != nil {
		return *x.GroupId
	}
	return 0
}

func (x *ListGuideConfig_GuideConfig) GetType() EGuide {
	if x != nil && x.Type != nil {
		return *x.Type
	}
	return EGuide_EGuide_None
}

func (x *ListGuideConfig_GuideConfig) GetParams() []string {
	if x != nil {
		return x.Params
	}
	return nil
}

type ListGuideGroupConfig_GuideGroupConfig struct {
	state          protoimpl.MessageState `protogen:"open.v1"`
	Id             *int32                 `protobuf:"varint,2,opt,name=Id" json:"Id,omitempty"`
	RertyOnFailure *bool                  `protobuf:"varint,4,opt,name=RertyOnFailure" json:"RertyOnFailure,omitempty"`
	CondType       *EConditionType        `protobuf:"varint,6,opt,name=CondType,enum=pb.EConditionType" json:"CondType,omitempty"`
	CondTarget     *int32                 `protobuf:"varint,7,opt,name=CondTarget" json:"CondTarget,omitempty"`
	CondParams     []int32                `protobuf:"varint,8,rep,name=CondParams" json:"CondParams,omitempty"`
	CondVersion    *int32                 `protobuf:"varint,9,opt,name=CondVersion" json:"CondVersion,omitempty"`
	unknownFields  protoimpl.UnknownFields
	sizeCache      protoimpl.SizeCache
}

func (x *ListGuideGroupConfig_GuideGroupConfig) Reset() {
	*x = ListGuideGroupConfig_GuideGroupConfig{}
	mi := &file_gameconfig_proto_msgTypes[64]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListGuideGroupConfig_GuideGroupConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListGuideGroupConfig_GuideGroupConfig) ProtoMessage() {}

func (x *ListGuideGroupConfig_GuideGroupConfig) ProtoReflect() protoreflect.Message {
	mi := &file_gameconfig_proto_msgTypes[64]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListGuideGroupConfig_GuideGroupConfig.ProtoReflect.Descriptor instead.
func (*ListGuideGroupConfig_GuideGroupConfig) Descriptor() ([]byte, []int) {
	return file_gameconfig_proto_rawDescGZIP(), []int{25, 0}
}

func (x *ListGuideGroupConfig_GuideGroupConfig) GetId() int32 {
	if x != nil && x.Id != nil {
		return *x.Id
	}
	return 0
}

func (x *ListGuideGroupConfig_GuideGroupConfig) GetRertyOnFailure() bool {
	if x != nil && x.RertyOnFailure != nil {
		return *x.RertyOnFailure
	}
	return false
}

func (x *ListGuideGroupConfig_GuideGroupConfig) GetCondType() EConditionType {
	if x != nil && x.CondType != nil {
		return *x.CondType
	}
	return EConditionType_EConditionType_None
}

func (x *ListGuideGroupConfig_GuideGroupConfig) GetCondTarget() int32 {
	if x != nil && x.CondTarget != nil {
		return *x.CondTarget
	}
	return 0
}

func (x *ListGuideGroupConfig_GuideGroupConfig) GetCondParams() []int32 {
	if x != nil {
		return x.CondParams
	}
	return nil
}

func (x *ListGuideGroupConfig_GuideGroupConfig) GetCondVersion() int32 {
	if x != nil && x.CondVersion != nil {
		return *x.CondVersion
	}
	return 0
}

type ListHeadPortraitConfig_HeadPortraitConfig struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            *int32                 `protobuf:"varint,2,opt,name=Id" json:"Id,omitempty"`
	IsDefault     *bool                  `protobuf:"varint,4,opt,name=IsDefault" json:"IsDefault,omitempty"`
	CondType      *EConditionType        `protobuf:"varint,7,opt,name=CondType,enum=pb.EConditionType" json:"CondType,omitempty"`
	CondTarget    *int32                 `protobuf:"varint,8,opt,name=CondTarget" json:"CondTarget,omitempty"`
	CondParams    []int32                `protobuf:"varint,9,rep,name=CondParams" json:"CondParams,omitempty"`
	CondVersion   *int32                 `protobuf:"varint,10,opt,name=CondVersion" json:"CondVersion,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListHeadPortraitConfig_HeadPortraitConfig) Reset() {
	*x = ListHeadPortraitConfig_HeadPortraitConfig{}
	mi := &file_gameconfig_proto_msgTypes[65]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListHeadPortraitConfig_HeadPortraitConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListHeadPortraitConfig_HeadPortraitConfig) ProtoMessage() {}

func (x *ListHeadPortraitConfig_HeadPortraitConfig) ProtoReflect() protoreflect.Message {
	mi := &file_gameconfig_proto_msgTypes[65]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListHeadPortraitConfig_HeadPortraitConfig.ProtoReflect.Descriptor instead.
func (*ListHeadPortraitConfig_HeadPortraitConfig) Descriptor() ([]byte, []int) {
	return file_gameconfig_proto_rawDescGZIP(), []int{26, 0}
}

func (x *ListHeadPortraitConfig_HeadPortraitConfig) GetId() int32 {
	if x != nil && x.Id != nil {
		return *x.Id
	}
	return 0
}

func (x *ListHeadPortraitConfig_HeadPortraitConfig) GetIsDefault() bool {
	if x != nil && x.IsDefault != nil {
		return *x.IsDefault
	}
	return false
}

func (x *ListHeadPortraitConfig_HeadPortraitConfig) GetCondType() EConditionType {
	if x != nil && x.CondType != nil {
		return *x.CondType
	}
	return EConditionType_EConditionType_None
}

func (x *ListHeadPortraitConfig_HeadPortraitConfig) GetCondTarget() int32 {
	if x != nil && x.CondTarget != nil {
		return *x.CondTarget
	}
	return 0
}

func (x *ListHeadPortraitConfig_HeadPortraitConfig) GetCondParams() []int32 {
	if x != nil {
		return x.CondParams
	}
	return nil
}

func (x *ListHeadPortraitConfig_HeadPortraitConfig) GetCondVersion() int32 {
	if x != nil && x.CondVersion != nil {
		return *x.CondVersion
	}
	return 0
}

type ListHeroConfig_HeroConfig struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            *int32                 `protobuf:"varint,2,opt,name=Id" json:"Id,omitempty"`
	Quality       *EQuality              `protobuf:"varint,6,opt,name=Quality,enum=pb.EQuality" json:"Quality,omitempty"`
	Star          *int32                 `protobuf:"varint,7,opt,name=Star" json:"Star,omitempty"`
	NeedChips     *ItemData              `protobuf:"bytes,8,opt,name=NeedChips" json:"NeedChips,omitempty"`
	BaseAttr      []*HeroAttr            `protobuf:"bytes,9,rep,name=BaseAttr" json:"BaseAttr,omitempty"`
	InitEnergy    *int32                 `protobuf:"varint,10,opt,name=InitEnergy" json:"InitEnergy,omitempty"`
	LevelGroup    *int32                 `protobuf:"varint,11,opt,name=LevelGroup" json:"LevelGroup,omitempty"`
	StarGroupId   *int32                 `protobuf:"varint,12,opt,name=StarGroupId" json:"StarGroupId,omitempty"`
	Duplicate     *bool                  `protobuf:"varint,13,opt,name=Duplicate" json:"Duplicate,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListHeroConfig_HeroConfig) Reset() {
	*x = ListHeroConfig_HeroConfig{}
	mi := &file_gameconfig_proto_msgTypes[66]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListHeroConfig_HeroConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListHeroConfig_HeroConfig) ProtoMessage() {}

func (x *ListHeroConfig_HeroConfig) ProtoReflect() protoreflect.Message {
	mi := &file_gameconfig_proto_msgTypes[66]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListHeroConfig_HeroConfig.ProtoReflect.Descriptor instead.
func (*ListHeroConfig_HeroConfig) Descriptor() ([]byte, []int) {
	return file_gameconfig_proto_rawDescGZIP(), []int{27, 0}
}

func (x *ListHeroConfig_HeroConfig) GetId() int32 {
	if x != nil && x.Id != nil {
		return *x.Id
	}
	return 0
}

func (x *ListHeroConfig_HeroConfig) GetQuality() EQuality {
	if x != nil && x.Quality != nil {
		return *x.Quality
	}
	return EQuality_EQuality_None
}

func (x *ListHeroConfig_HeroConfig) GetStar() int32 {
	if x != nil && x.Star != nil {
		return *x.Star
	}
	return 0
}

func (x *ListHeroConfig_HeroConfig) GetNeedChips() *ItemData {
	if x != nil {
		return x.NeedChips
	}
	return nil
}

func (x *ListHeroConfig_HeroConfig) GetBaseAttr() []*HeroAttr {
	if x != nil {
		return x.BaseAttr
	}
	return nil
}

func (x *ListHeroConfig_HeroConfig) GetInitEnergy() int32 {
	if x != nil && x.InitEnergy != nil {
		return *x.InitEnergy
	}
	return 0
}

func (x *ListHeroConfig_HeroConfig) GetLevelGroup() int32 {
	if x != nil && x.LevelGroup != nil {
		return *x.LevelGroup
	}
	return 0
}

func (x *ListHeroConfig_HeroConfig) GetStarGroupId() int32 {
	if x != nil && x.StarGroupId != nil {
		return *x.StarGroupId
	}
	return 0
}

func (x *ListHeroConfig_HeroConfig) GetDuplicate() bool {
	if x != nil && x.Duplicate != nil {
		return *x.Duplicate
	}
	return false
}

type ListHeroLevelConfig_HeroLevelConfig struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            *int32                 `protobuf:"varint,2,opt,name=Id" json:"Id,omitempty"`
	Level         *int32                 `protobuf:"varint,3,opt,name=Level" json:"Level,omitempty"`
	GroupId       *int32                 `protobuf:"varint,4,opt,name=GroupId" json:"GroupId,omitempty"`
	Attrs         []*HeroAttr            `protobuf:"bytes,5,rep,name=Attrs" json:"Attrs,omitempty"`
	CostItem      *ItemData              `protobuf:"bytes,6,opt,name=CostItem" json:"CostItem,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListHeroLevelConfig_HeroLevelConfig) Reset() {
	*x = ListHeroLevelConfig_HeroLevelConfig{}
	mi := &file_gameconfig_proto_msgTypes[67]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListHeroLevelConfig_HeroLevelConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListHeroLevelConfig_HeroLevelConfig) ProtoMessage() {}

func (x *ListHeroLevelConfig_HeroLevelConfig) ProtoReflect() protoreflect.Message {
	mi := &file_gameconfig_proto_msgTypes[67]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListHeroLevelConfig_HeroLevelConfig.ProtoReflect.Descriptor instead.
func (*ListHeroLevelConfig_HeroLevelConfig) Descriptor() ([]byte, []int) {
	return file_gameconfig_proto_rawDescGZIP(), []int{28, 0}
}

func (x *ListHeroLevelConfig_HeroLevelConfig) GetId() int32 {
	if x != nil && x.Id != nil {
		return *x.Id
	}
	return 0
}

func (x *ListHeroLevelConfig_HeroLevelConfig) GetLevel() int32 {
	if x != nil && x.Level != nil {
		return *x.Level
	}
	return 0
}

func (x *ListHeroLevelConfig_HeroLevelConfig) GetGroupId() int32 {
	if x != nil && x.GroupId != nil {
		return *x.GroupId
	}
	return 0
}

func (x *ListHeroLevelConfig_HeroLevelConfig) GetAttrs() []*HeroAttr {
	if x != nil {
		return x.Attrs
	}
	return nil
}

func (x *ListHeroLevelConfig_HeroLevelConfig) GetCostItem() *ItemData {
	if x != nil {
		return x.CostItem
	}
	return nil
}

type ListHeroQualityConfig_HeroQualityConfig struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            *int32                 `protobuf:"varint,2,opt,name=Id" json:"Id,omitempty"`
	MaxStar       *int32                 `protobuf:"varint,4,opt,name=MaxStar" json:"MaxStar,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListHeroQualityConfig_HeroQualityConfig) Reset() {
	*x = ListHeroQualityConfig_HeroQualityConfig{}
	mi := &file_gameconfig_proto_msgTypes[68]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListHeroQualityConfig_HeroQualityConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListHeroQualityConfig_HeroQualityConfig) ProtoMessage() {}

func (x *ListHeroQualityConfig_HeroQualityConfig) ProtoReflect() protoreflect.Message {
	mi := &file_gameconfig_proto_msgTypes[68]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListHeroQualityConfig_HeroQualityConfig.ProtoReflect.Descriptor instead.
func (*ListHeroQualityConfig_HeroQualityConfig) Descriptor() ([]byte, []int) {
	return file_gameconfig_proto_rawDescGZIP(), []int{29, 0}
}

func (x *ListHeroQualityConfig_HeroQualityConfig) GetId() int32 {
	if x != nil && x.Id != nil {
		return *x.Id
	}
	return 0
}

func (x *ListHeroQualityConfig_HeroQualityConfig) GetMaxStar() int32 {
	if x != nil && x.MaxStar != nil {
		return *x.MaxStar
	}
	return 0
}

type ListHeroStarConfig_HeroStarConfig struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            *int32                 `protobuf:"varint,2,opt,name=Id" json:"Id,omitempty"`
	GroupId       *int32                 `protobuf:"varint,3,opt,name=GroupId" json:"GroupId,omitempty"`
	StarLevel     *int32                 `protobuf:"varint,4,opt,name=StarLevel" json:"StarLevel,omitempty"`
	CostChips     *int32                 `protobuf:"varint,5,opt,name=CostChips" json:"CostChips,omitempty"`
	CostItem      *ItemData              `protobuf:"bytes,6,opt,name=CostItem" json:"CostItem,omitempty"`
	Attrs         []*HeroAttr            `protobuf:"bytes,7,rep,name=Attrs" json:"Attrs,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListHeroStarConfig_HeroStarConfig) Reset() {
	*x = ListHeroStarConfig_HeroStarConfig{}
	mi := &file_gameconfig_proto_msgTypes[69]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListHeroStarConfig_HeroStarConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListHeroStarConfig_HeroStarConfig) ProtoMessage() {}

func (x *ListHeroStarConfig_HeroStarConfig) ProtoReflect() protoreflect.Message {
	mi := &file_gameconfig_proto_msgTypes[69]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListHeroStarConfig_HeroStarConfig.ProtoReflect.Descriptor instead.
func (*ListHeroStarConfig_HeroStarConfig) Descriptor() ([]byte, []int) {
	return file_gameconfig_proto_rawDescGZIP(), []int{30, 0}
}

func (x *ListHeroStarConfig_HeroStarConfig) GetId() int32 {
	if x != nil && x.Id != nil {
		return *x.Id
	}
	return 0
}

func (x *ListHeroStarConfig_HeroStarConfig) GetGroupId() int32 {
	if x != nil && x.GroupId != nil {
		return *x.GroupId
	}
	return 0
}

func (x *ListHeroStarConfig_HeroStarConfig) GetStarLevel() int32 {
	if x != nil && x.StarLevel != nil {
		return *x.StarLevel
	}
	return 0
}

func (x *ListHeroStarConfig_HeroStarConfig) GetCostChips() int32 {
	if x != nil && x.CostChips != nil {
		return *x.CostChips
	}
	return 0
}

func (x *ListHeroStarConfig_HeroStarConfig) GetCostItem() *ItemData {
	if x != nil {
		return x.CostItem
	}
	return nil
}

func (x *ListHeroStarConfig_HeroStarConfig) GetAttrs() []*HeroAttr {
	if x != nil {
		return x.Attrs
	}
	return nil
}

type ListItemConfig_ItemConfig struct {
	state              protoimpl.MessageState `protogen:"open.v1"`
	Id                 *int32                 `protobuf:"varint,2,opt,name=Id" json:"Id,omitempty"`
	Name               *string                `protobuf:"bytes,3,opt,name=Name" json:"Name,omitempty"`
	Type               *EItem                 `protobuf:"varint,4,opt,name=Type,enum=pb.EItem" json:"Type,omitempty"`
	SubType            *int32                 `protobuf:"varint,5,opt,name=SubType" json:"SubType,omitempty"`
	PersonalDailyLimit *int32                 `protobuf:"varint,6,opt,name=PersonalDailyLimit" json:"PersonalDailyLimit,omitempty"`
	GlobalDailyLimit   *int32                 `protobuf:"varint,7,opt,name=GlobalDailyLimit" json:"GlobalDailyLimit,omitempty"`
	GlobalWeekLimit    *int32                 `protobuf:"varint,8,opt,name=GlobalWeekLimit" json:"GlobalWeekLimit,omitempty"`
	GlobalMonthLimit   *int32                 `protobuf:"varint,9,opt,name=GlobalMonthLimit" json:"GlobalMonthLimit,omitempty"`
	StartTime          *int64                 `protobuf:"varint,10,opt,name=StartTime" json:"StartTime,omitempty"`
	ExpireTime         *int64                 `protobuf:"varint,11,opt,name=ExpireTime" json:"ExpireTime,omitempty"`
	ValidHours         *int32                 `protobuf:"varint,12,opt,name=ValidHours" json:"ValidHours,omitempty"`
	Overlay            *int32                 `protobuf:"varint,13,opt,name=Overlay" json:"Overlay,omitempty"`
	Bind               *int32                 `protobuf:"varint,14,opt,name=Bind" json:"Bind,omitempty"`
	Icon               *string                `protobuf:"bytes,15,opt,name=Icon" json:"Icon,omitempty"`
	TinyIcon           *string                `protobuf:"bytes,16,opt,name=TinyIcon" json:"TinyIcon,omitempty"`
	Desc               *string                `protobuf:"bytes,17,opt,name=Desc" json:"Desc,omitempty"`
	unknownFields      protoimpl.UnknownFields
	sizeCache          protoimpl.SizeCache
}

func (x *ListItemConfig_ItemConfig) Reset() {
	*x = ListItemConfig_ItemConfig{}
	mi := &file_gameconfig_proto_msgTypes[70]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListItemConfig_ItemConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListItemConfig_ItemConfig) ProtoMessage() {}

func (x *ListItemConfig_ItemConfig) ProtoReflect() protoreflect.Message {
	mi := &file_gameconfig_proto_msgTypes[70]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListItemConfig_ItemConfig.ProtoReflect.Descriptor instead.
func (*ListItemConfig_ItemConfig) Descriptor() ([]byte, []int) {
	return file_gameconfig_proto_rawDescGZIP(), []int{31, 0}
}

func (x *ListItemConfig_ItemConfig) GetId() int32 {
	if x != nil && x.Id != nil {
		return *x.Id
	}
	return 0
}

func (x *ListItemConfig_ItemConfig) GetName() string {
	if x != nil && x.Name != nil {
		return *x.Name
	}
	return ""
}

func (x *ListItemConfig_ItemConfig) GetType() EItem {
	if x != nil && x.Type != nil {
		return *x.Type
	}
	return EItem_EItem_Money
}

func (x *ListItemConfig_ItemConfig) GetSubType() int32 {
	if x != nil && x.SubType != nil {
		return *x.SubType
	}
	return 0
}

func (x *ListItemConfig_ItemConfig) GetPersonalDailyLimit() int32 {
	if x != nil && x.PersonalDailyLimit != nil {
		return *x.PersonalDailyLimit
	}
	return 0
}

func (x *ListItemConfig_ItemConfig) GetGlobalDailyLimit() int32 {
	if x != nil && x.GlobalDailyLimit != nil {
		return *x.GlobalDailyLimit
	}
	return 0
}

func (x *ListItemConfig_ItemConfig) GetGlobalWeekLimit() int32 {
	if x != nil && x.GlobalWeekLimit != nil {
		return *x.GlobalWeekLimit
	}
	return 0
}

func (x *ListItemConfig_ItemConfig) GetGlobalMonthLimit() int32 {
	if x != nil && x.GlobalMonthLimit != nil {
		return *x.GlobalMonthLimit
	}
	return 0
}

func (x *ListItemConfig_ItemConfig) GetStartTime() int64 {
	if x != nil && x.StartTime != nil {
		return *x.StartTime
	}
	return 0
}

func (x *ListItemConfig_ItemConfig) GetExpireTime() int64 {
	if x != nil && x.ExpireTime != nil {
		return *x.ExpireTime
	}
	return 0
}

func (x *ListItemConfig_ItemConfig) GetValidHours() int32 {
	if x != nil && x.ValidHours != nil {
		return *x.ValidHours
	}
	return 0
}

func (x *ListItemConfig_ItemConfig) GetOverlay() int32 {
	if x != nil && x.Overlay != nil {
		return *x.Overlay
	}
	return 0
}

func (x *ListItemConfig_ItemConfig) GetBind() int32 {
	if x != nil && x.Bind != nil {
		return *x.Bind
	}
	return 0
}

func (x *ListItemConfig_ItemConfig) GetIcon() string {
	if x != nil && x.Icon != nil {
		return *x.Icon
	}
	return ""
}

func (x *ListItemConfig_ItemConfig) GetTinyIcon() string {
	if x != nil && x.TinyIcon != nil {
		return *x.TinyIcon
	}
	return ""
}

func (x *ListItemConfig_ItemConfig) GetDesc() string {
	if x != nil && x.Desc != nil {
		return *x.Desc
	}
	return ""
}

type ListLevelConfig_LevelConfig struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            *int32                 `protobuf:"varint,2,opt,name=Id" json:"Id,omitempty"`
	Prosperity    *int32                 `protobuf:"varint,3,opt,name=Prosperity" json:"Prosperity,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListLevelConfig_LevelConfig) Reset() {
	*x = ListLevelConfig_LevelConfig{}
	mi := &file_gameconfig_proto_msgTypes[71]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListLevelConfig_LevelConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListLevelConfig_LevelConfig) ProtoMessage() {}

func (x *ListLevelConfig_LevelConfig) ProtoReflect() protoreflect.Message {
	mi := &file_gameconfig_proto_msgTypes[71]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListLevelConfig_LevelConfig.ProtoReflect.Descriptor instead.
func (*ListLevelConfig_LevelConfig) Descriptor() ([]byte, []int) {
	return file_gameconfig_proto_rawDescGZIP(), []int{32, 0}
}

func (x *ListLevelConfig_LevelConfig) GetId() int32 {
	if x != nil && x.Id != nil {
		return *x.Id
	}
	return 0
}

func (x *ListLevelConfig_LevelConfig) GetProsperity() int32 {
	if x != nil && x.Prosperity != nil {
		return *x.Prosperity
	}
	return 0
}

type ListMailConfig_MailConfig struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            *int32                 `protobuf:"varint,2,opt,name=Id" json:"Id,omitempty"`
	Title         *string                `protobuf:"bytes,4,opt,name=Title" json:"Title,omitempty"`
	Content       *string                `protobuf:"bytes,5,opt,name=Content" json:"Content,omitempty"`
	Attachments   []*ItemData            `protobuf:"bytes,6,rep,name=Attachments" json:"Attachments,omitempty"`
	Sender        *string                `protobuf:"bytes,7,opt,name=Sender" json:"Sender,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListMailConfig_MailConfig) Reset() {
	*x = ListMailConfig_MailConfig{}
	mi := &file_gameconfig_proto_msgTypes[72]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListMailConfig_MailConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListMailConfig_MailConfig) ProtoMessage() {}

func (x *ListMailConfig_MailConfig) ProtoReflect() protoreflect.Message {
	mi := &file_gameconfig_proto_msgTypes[72]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListMailConfig_MailConfig.ProtoReflect.Descriptor instead.
func (*ListMailConfig_MailConfig) Descriptor() ([]byte, []int) {
	return file_gameconfig_proto_rawDescGZIP(), []int{33, 0}
}

func (x *ListMailConfig_MailConfig) GetId() int32 {
	if x != nil && x.Id != nil {
		return *x.Id
	}
	return 0
}

func (x *ListMailConfig_MailConfig) GetTitle() string {
	if x != nil && x.Title != nil {
		return *x.Title
	}
	return ""
}

func (x *ListMailConfig_MailConfig) GetContent() string {
	if x != nil && x.Content != nil {
		return *x.Content
	}
	return ""
}

func (x *ListMailConfig_MailConfig) GetAttachments() []*ItemData {
	if x != nil {
		return x.Attachments
	}
	return nil
}

func (x *ListMailConfig_MailConfig) GetSender() string {
	if x != nil && x.Sender != nil {
		return *x.Sender
	}
	return ""
}

type ListMainCityScaleConfig_MainCityScaleConfig struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            *int32                 `protobuf:"varint,2,opt,name=Id" json:"Id,omitempty"`
	Condition     *int32                 `protobuf:"varint,6,opt,name=Condition" json:"Condition,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListMainCityScaleConfig_MainCityScaleConfig) Reset() {
	*x = ListMainCityScaleConfig_MainCityScaleConfig{}
	mi := &file_gameconfig_proto_msgTypes[73]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListMainCityScaleConfig_MainCityScaleConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListMainCityScaleConfig_MainCityScaleConfig) ProtoMessage() {}

func (x *ListMainCityScaleConfig_MainCityScaleConfig) ProtoReflect() protoreflect.Message {
	mi := &file_gameconfig_proto_msgTypes[73]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListMainCityScaleConfig_MainCityScaleConfig.ProtoReflect.Descriptor instead.
func (*ListMainCityScaleConfig_MainCityScaleConfig) Descriptor() ([]byte, []int) {
	return file_gameconfig_proto_rawDescGZIP(), []int{34, 0}
}

func (x *ListMainCityScaleConfig_MainCityScaleConfig) GetId() int32 {
	if x != nil && x.Id != nil {
		return *x.Id
	}
	return 0
}

func (x *ListMainCityScaleConfig_MainCityScaleConfig) GetCondition() int32 {
	if x != nil && x.Condition != nil {
		return *x.Condition
	}
	return 0
}

type ListMainTaskChapterConfig_MainTaskChapterConfig struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            *int32                 `protobuf:"varint,2,opt,name=Id" json:"Id,omitempty"`
	Name          *string                `protobuf:"bytes,3,opt,name=Name" json:"Name,omitempty"`
	Award         *int32                 `protobuf:"varint,6,opt,name=Award" json:"Award,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListMainTaskChapterConfig_MainTaskChapterConfig) Reset() {
	*x = ListMainTaskChapterConfig_MainTaskChapterConfig{}
	mi := &file_gameconfig_proto_msgTypes[74]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListMainTaskChapterConfig_MainTaskChapterConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListMainTaskChapterConfig_MainTaskChapterConfig) ProtoMessage() {}

func (x *ListMainTaskChapterConfig_MainTaskChapterConfig) ProtoReflect() protoreflect.Message {
	mi := &file_gameconfig_proto_msgTypes[74]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListMainTaskChapterConfig_MainTaskChapterConfig.ProtoReflect.Descriptor instead.
func (*ListMainTaskChapterConfig_MainTaskChapterConfig) Descriptor() ([]byte, []int) {
	return file_gameconfig_proto_rawDescGZIP(), []int{35, 0}
}

func (x *ListMainTaskChapterConfig_MainTaskChapterConfig) GetId() int32 {
	if x != nil && x.Id != nil {
		return *x.Id
	}
	return 0
}

func (x *ListMainTaskChapterConfig_MainTaskChapterConfig) GetName() string {
	if x != nil && x.Name != nil {
		return *x.Name
	}
	return ""
}

func (x *ListMainTaskChapterConfig_MainTaskChapterConfig) GetAward() int32 {
	if x != nil && x.Award != nil {
		return *x.Award
	}
	return 0
}

type ListMainTaskConfig_MainTaskConfig struct {
	state           protoimpl.MessageState `protogen:"open.v1"`
	Id              *int32                 `protobuf:"varint,2,opt,name=Id" json:"Id,omitempty"`
	Name            *string                `protobuf:"bytes,3,opt,name=Name" json:"Name,omitempty"`
	NameArgs        []string               `protobuf:"bytes,4,rep,name=NameArgs" json:"NameArgs,omitempty"`
	ChapterId       *int32                 `protobuf:"varint,7,opt,name=ChapterId" json:"ChapterId,omitempty"`
	PreId           *int32                 `protobuf:"varint,9,opt,name=PreId" json:"PreId,omitempty"`
	DefaultAccept   *bool                  `protobuf:"varint,10,opt,name=DefaultAccept" json:"DefaultAccept,omitempty"`
	CondType        *EConditionType        `protobuf:"varint,11,opt,name=CondType,enum=pb.EConditionType" json:"CondType,omitempty"`
	CondTarget      *int32                 `protobuf:"varint,12,opt,name=CondTarget" json:"CondTarget,omitempty"`
	CondParams      []int32                `protobuf:"varint,13,rep,name=CondParams" json:"CondParams,omitempty"`
	CondVersion     *int32                 `protobuf:"varint,14,opt,name=CondVersion" json:"CondVersion,omitempty"`
	Award           *int32                 `protobuf:"varint,15,opt,name=Award" json:"Award,omitempty"`
	TagIds          []int32                `protobuf:"varint,18,rep,name=TagIds" json:"TagIds,omitempty"`
	DefaultComplete *bool                  `protobuf:"varint,19,opt,name=DefaultComplete" json:"DefaultComplete,omitempty"`
	unknownFields   protoimpl.UnknownFields
	sizeCache       protoimpl.SizeCache
}

func (x *ListMainTaskConfig_MainTaskConfig) Reset() {
	*x = ListMainTaskConfig_MainTaskConfig{}
	mi := &file_gameconfig_proto_msgTypes[75]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListMainTaskConfig_MainTaskConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListMainTaskConfig_MainTaskConfig) ProtoMessage() {}

func (x *ListMainTaskConfig_MainTaskConfig) ProtoReflect() protoreflect.Message {
	mi := &file_gameconfig_proto_msgTypes[75]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListMainTaskConfig_MainTaskConfig.ProtoReflect.Descriptor instead.
func (*ListMainTaskConfig_MainTaskConfig) Descriptor() ([]byte, []int) {
	return file_gameconfig_proto_rawDescGZIP(), []int{36, 0}
}

func (x *ListMainTaskConfig_MainTaskConfig) GetId() int32 {
	if x != nil && x.Id != nil {
		return *x.Id
	}
	return 0
}

func (x *ListMainTaskConfig_MainTaskConfig) GetName() string {
	if x != nil && x.Name != nil {
		return *x.Name
	}
	return ""
}

func (x *ListMainTaskConfig_MainTaskConfig) GetNameArgs() []string {
	if x != nil {
		return x.NameArgs
	}
	return nil
}

func (x *ListMainTaskConfig_MainTaskConfig) GetChapterId() int32 {
	if x != nil && x.ChapterId != nil {
		return *x.ChapterId
	}
	return 0
}

func (x *ListMainTaskConfig_MainTaskConfig) GetPreId() int32 {
	if x != nil && x.PreId != nil {
		return *x.PreId
	}
	return 0
}

func (x *ListMainTaskConfig_MainTaskConfig) GetDefaultAccept() bool {
	if x != nil && x.DefaultAccept != nil {
		return *x.DefaultAccept
	}
	return false
}

func (x *ListMainTaskConfig_MainTaskConfig) GetCondType() EConditionType {
	if x != nil && x.CondType != nil {
		return *x.CondType
	}
	return EConditionType_EConditionType_None
}

func (x *ListMainTaskConfig_MainTaskConfig) GetCondTarget() int32 {
	if x != nil && x.CondTarget != nil {
		return *x.CondTarget
	}
	return 0
}

func (x *ListMainTaskConfig_MainTaskConfig) GetCondParams() []int32 {
	if x != nil {
		return x.CondParams
	}
	return nil
}

func (x *ListMainTaskConfig_MainTaskConfig) GetCondVersion() int32 {
	if x != nil && x.CondVersion != nil {
		return *x.CondVersion
	}
	return 0
}

func (x *ListMainTaskConfig_MainTaskConfig) GetAward() int32 {
	if x != nil && x.Award != nil {
		return *x.Award
	}
	return 0
}

func (x *ListMainTaskConfig_MainTaskConfig) GetTagIds() []int32 {
	if x != nil {
		return x.TagIds
	}
	return nil
}

func (x *ListMainTaskConfig_MainTaskConfig) GetDefaultComplete() bool {
	if x != nil && x.DefaultComplete != nil {
		return *x.DefaultComplete
	}
	return false
}

type ListMapConfig_MapConfig struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            *int32                 `protobuf:"varint,2,opt,name=Id" json:"Id,omitempty"`
	TileMapId     *int32                 `protobuf:"varint,3,opt,name=TileMapId" json:"TileMapId,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListMapConfig_MapConfig) Reset() {
	*x = ListMapConfig_MapConfig{}
	mi := &file_gameconfig_proto_msgTypes[76]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListMapConfig_MapConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListMapConfig_MapConfig) ProtoMessage() {}

func (x *ListMapConfig_MapConfig) ProtoReflect() protoreflect.Message {
	mi := &file_gameconfig_proto_msgTypes[76]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListMapConfig_MapConfig.ProtoReflect.Descriptor instead.
func (*ListMapConfig_MapConfig) Descriptor() ([]byte, []int) {
	return file_gameconfig_proto_rawDescGZIP(), []int{37, 0}
}

func (x *ListMapConfig_MapConfig) GetId() int32 {
	if x != nil && x.Id != nil {
		return *x.Id
	}
	return 0
}

func (x *ListMapConfig_MapConfig) GetTileMapId() int32 {
	if x != nil && x.TileMapId != nil {
		return *x.TileMapId
	}
	return 0
}

type ListMiniGameConfig_MiniGameConfig struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            *int32                 `protobuf:"varint,2,opt,name=Id" json:"Id,omitempty"`
	Identity      *string                `protobuf:"bytes,3,opt,name=Identity" json:"Identity,omitempty"`
	Name          *string                `protobuf:"bytes,4,opt,name=Name" json:"Name,omitempty"`
	Desc          *string                `protobuf:"bytes,5,opt,name=Desc" json:"Desc,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListMiniGameConfig_MiniGameConfig) Reset() {
	*x = ListMiniGameConfig_MiniGameConfig{}
	mi := &file_gameconfig_proto_msgTypes[77]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListMiniGameConfig_MiniGameConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListMiniGameConfig_MiniGameConfig) ProtoMessage() {}

func (x *ListMiniGameConfig_MiniGameConfig) ProtoReflect() protoreflect.Message {
	mi := &file_gameconfig_proto_msgTypes[77]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListMiniGameConfig_MiniGameConfig.ProtoReflect.Descriptor instead.
func (*ListMiniGameConfig_MiniGameConfig) Descriptor() ([]byte, []int) {
	return file_gameconfig_proto_rawDescGZIP(), []int{38, 0}
}

func (x *ListMiniGameConfig_MiniGameConfig) GetId() int32 {
	if x != nil && x.Id != nil {
		return *x.Id
	}
	return 0
}

func (x *ListMiniGameConfig_MiniGameConfig) GetIdentity() string {
	if x != nil && x.Identity != nil {
		return *x.Identity
	}
	return ""
}

func (x *ListMiniGameConfig_MiniGameConfig) GetName() string {
	if x != nil && x.Name != nil {
		return *x.Name
	}
	return ""
}

func (x *ListMiniGameConfig_MiniGameConfig) GetDesc() string {
	if x != nil && x.Desc != nil {
		return *x.Desc
	}
	return ""
}

type ListMUnitConfig_MUnitConfig struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            *int32                 `protobuf:"varint,2,opt,name=Id" json:"Id,omitempty"`
	Type          *EMUnit                `protobuf:"varint,4,opt,name=Type,enum=pb.EMUnit" json:"Type,omitempty"`
	Width         *int32                 `protobuf:"varint,5,opt,name=Width" json:"Width,omitempty"`
	Height        *int32                 `protobuf:"varint,6,opt,name=Height" json:"Height,omitempty"`
	AssetBundle   *string                `protobuf:"bytes,7,opt,name=AssetBundle" json:"AssetBundle,omitempty"`
	GridSize      *float32               `protobuf:"fixed32,8,opt,name=GridSize" json:"GridSize,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListMUnitConfig_MUnitConfig) Reset() {
	*x = ListMUnitConfig_MUnitConfig{}
	mi := &file_gameconfig_proto_msgTypes[78]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListMUnitConfig_MUnitConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListMUnitConfig_MUnitConfig) ProtoMessage() {}

func (x *ListMUnitConfig_MUnitConfig) ProtoReflect() protoreflect.Message {
	mi := &file_gameconfig_proto_msgTypes[78]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListMUnitConfig_MUnitConfig.ProtoReflect.Descriptor instead.
func (*ListMUnitConfig_MUnitConfig) Descriptor() ([]byte, []int) {
	return file_gameconfig_proto_rawDescGZIP(), []int{39, 0}
}

func (x *ListMUnitConfig_MUnitConfig) GetId() int32 {
	if x != nil && x.Id != nil {
		return *x.Id
	}
	return 0
}

func (x *ListMUnitConfig_MUnitConfig) GetType() EMUnit {
	if x != nil && x.Type != nil {
		return *x.Type
	}
	return EMUnit_EMUnit_Root
}

func (x *ListMUnitConfig_MUnitConfig) GetWidth() int32 {
	if x != nil && x.Width != nil {
		return *x.Width
	}
	return 0
}

func (x *ListMUnitConfig_MUnitConfig) GetHeight() int32 {
	if x != nil && x.Height != nil {
		return *x.Height
	}
	return 0
}

func (x *ListMUnitConfig_MUnitConfig) GetAssetBundle() string {
	if x != nil && x.AssetBundle != nil {
		return *x.AssetBundle
	}
	return ""
}

func (x *ListMUnitConfig_MUnitConfig) GetGridSize() float32 {
	if x != nil && x.GridSize != nil {
		return *x.GridSize
	}
	return 0
}

type ListRankConfig_RankConfig struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            *int32                 `protobuf:"varint,2,opt,name=Id" json:"Id,omitempty"`
	Name          *string                `protobuf:"bytes,3,opt,name=Name" json:"Name,omitempty"`
	MinValueLimit *int32                 `protobuf:"varint,4,opt,name=MinValueLimit" json:"MinValueLimit,omitempty"`
	MaxQueryLimit *int32                 `protobuf:"varint,5,opt,name=MaxQueryLimit" json:"MaxQueryLimit,omitempty"`
	ShowRankLimit *int32                 `protobuf:"varint,6,opt,name=ShowRankLimit" json:"ShowRankLimit,omitempty"`
	MaxRankLimit  *int32                 `protobuf:"varint,7,opt,name=MaxRankLimit" json:"MaxRankLimit,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListRankConfig_RankConfig) Reset() {
	*x = ListRankConfig_RankConfig{}
	mi := &file_gameconfig_proto_msgTypes[79]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListRankConfig_RankConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListRankConfig_RankConfig) ProtoMessage() {}

func (x *ListRankConfig_RankConfig) ProtoReflect() protoreflect.Message {
	mi := &file_gameconfig_proto_msgTypes[79]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListRankConfig_RankConfig.ProtoReflect.Descriptor instead.
func (*ListRankConfig_RankConfig) Descriptor() ([]byte, []int) {
	return file_gameconfig_proto_rawDescGZIP(), []int{40, 0}
}

func (x *ListRankConfig_RankConfig) GetId() int32 {
	if x != nil && x.Id != nil {
		return *x.Id
	}
	return 0
}

func (x *ListRankConfig_RankConfig) GetName() string {
	if x != nil && x.Name != nil {
		return *x.Name
	}
	return ""
}

func (x *ListRankConfig_RankConfig) GetMinValueLimit() int32 {
	if x != nil && x.MinValueLimit != nil {
		return *x.MinValueLimit
	}
	return 0
}

func (x *ListRankConfig_RankConfig) GetMaxQueryLimit() int32 {
	if x != nil && x.MaxQueryLimit != nil {
		return *x.MaxQueryLimit
	}
	return 0
}

func (x *ListRankConfig_RankConfig) GetShowRankLimit() int32 {
	if x != nil && x.ShowRankLimit != nil {
		return *x.ShowRankLimit
	}
	return 0
}

func (x *ListRankConfig_RankConfig) GetMaxRankLimit() int32 {
	if x != nil && x.MaxRankLimit != nil {
		return *x.MaxRankLimit
	}
	return 0
}

type ListRechargeConfig_RechargeConfig struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            *int32                 `protobuf:"varint,2,opt,name=Id" json:"Id,omitempty"`
	Identity      *string                `protobuf:"bytes,3,opt,name=Identity" json:"Identity,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListRechargeConfig_RechargeConfig) Reset() {
	*x = ListRechargeConfig_RechargeConfig{}
	mi := &file_gameconfig_proto_msgTypes[80]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListRechargeConfig_RechargeConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListRechargeConfig_RechargeConfig) ProtoMessage() {}

func (x *ListRechargeConfig_RechargeConfig) ProtoReflect() protoreflect.Message {
	mi := &file_gameconfig_proto_msgTypes[80]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListRechargeConfig_RechargeConfig.ProtoReflect.Descriptor instead.
func (*ListRechargeConfig_RechargeConfig) Descriptor() ([]byte, []int) {
	return file_gameconfig_proto_rawDescGZIP(), []int{41, 0}
}

func (x *ListRechargeConfig_RechargeConfig) GetId() int32 {
	if x != nil && x.Id != nil {
		return *x.Id
	}
	return 0
}

func (x *ListRechargeConfig_RechargeConfig) GetIdentity() string {
	if x != nil && x.Identity != nil {
		return *x.Identity
	}
	return ""
}

type ListShopGoodsConfig_ShopGoodsConfig struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            *int32                 `protobuf:"varint,2,opt,name=Id" json:"Id,omitempty"`
	GroupId       *int32                 `protobuf:"varint,3,opt,name=GroupId" json:"GroupId,omitempty"`
	ItemData      *ItemData              `protobuf:"bytes,5,opt,name=ItemData" json:"ItemData,omitempty"`
	CostItem      *ItemData              `protobuf:"bytes,6,opt,name=CostItem" json:"CostItem,omitempty"`
	Discount      *int32                 `protobuf:"varint,7,opt,name=Discount" json:"Discount,omitempty"`
	Level         []int32                `protobuf:"varint,8,rep,name=Level" json:"Level,omitempty"`
	ConditionId   *int32                 `protobuf:"varint,9,opt,name=ConditionId" json:"ConditionId,omitempty"`
	Prop          *int32                 `protobuf:"varint,10,opt,name=Prop" json:"Prop,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListShopGoodsConfig_ShopGoodsConfig) Reset() {
	*x = ListShopGoodsConfig_ShopGoodsConfig{}
	mi := &file_gameconfig_proto_msgTypes[81]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListShopGoodsConfig_ShopGoodsConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListShopGoodsConfig_ShopGoodsConfig) ProtoMessage() {}

func (x *ListShopGoodsConfig_ShopGoodsConfig) ProtoReflect() protoreflect.Message {
	mi := &file_gameconfig_proto_msgTypes[81]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListShopGoodsConfig_ShopGoodsConfig.ProtoReflect.Descriptor instead.
func (*ListShopGoodsConfig_ShopGoodsConfig) Descriptor() ([]byte, []int) {
	return file_gameconfig_proto_rawDescGZIP(), []int{42, 0}
}

func (x *ListShopGoodsConfig_ShopGoodsConfig) GetId() int32 {
	if x != nil && x.Id != nil {
		return *x.Id
	}
	return 0
}

func (x *ListShopGoodsConfig_ShopGoodsConfig) GetGroupId() int32 {
	if x != nil && x.GroupId != nil {
		return *x.GroupId
	}
	return 0
}

func (x *ListShopGoodsConfig_ShopGoodsConfig) GetItemData() *ItemData {
	if x != nil {
		return x.ItemData
	}
	return nil
}

func (x *ListShopGoodsConfig_ShopGoodsConfig) GetCostItem() *ItemData {
	if x != nil {
		return x.CostItem
	}
	return nil
}

func (x *ListShopGoodsConfig_ShopGoodsConfig) GetDiscount() int32 {
	if x != nil && x.Discount != nil {
		return *x.Discount
	}
	return 0
}

func (x *ListShopGoodsConfig_ShopGoodsConfig) GetLevel() []int32 {
	if x != nil {
		return x.Level
	}
	return nil
}

func (x *ListShopGoodsConfig_ShopGoodsConfig) GetConditionId() int32 {
	if x != nil && x.ConditionId != nil {
		return *x.ConditionId
	}
	return 0
}

func (x *ListShopGoodsConfig_ShopGoodsConfig) GetProp() int32 {
	if x != nil && x.Prop != nil {
		return *x.Prop
	}
	return 0
}

type ListShopTabConfig_ShopTabConfig struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            *int32                 `protobuf:"varint,2,opt,name=Id" json:"Id,omitempty"`
	Type          *EShopType             `protobuf:"varint,6,opt,name=Type,enum=pb.EShopType" json:"Type,omitempty"`
	GroupId       *int32                 `protobuf:"varint,7,opt,name=GroupId" json:"GroupId,omitempty"`
	GoodsCount    *int32                 `protobuf:"varint,8,opt,name=GoodsCount" json:"GoodsCount,omitempty"`
	SystemId      *ESystemId             `protobuf:"varint,9,opt,name=SystemId,enum=pb.ESystemId" json:"SystemId,omitempty"`
	RefreshCost   []*ItemData            `protobuf:"bytes,10,rep,name=RefreshCost" json:"RefreshCost,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListShopTabConfig_ShopTabConfig) Reset() {
	*x = ListShopTabConfig_ShopTabConfig{}
	mi := &file_gameconfig_proto_msgTypes[82]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListShopTabConfig_ShopTabConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListShopTabConfig_ShopTabConfig) ProtoMessage() {}

func (x *ListShopTabConfig_ShopTabConfig) ProtoReflect() protoreflect.Message {
	mi := &file_gameconfig_proto_msgTypes[82]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListShopTabConfig_ShopTabConfig.ProtoReflect.Descriptor instead.
func (*ListShopTabConfig_ShopTabConfig) Descriptor() ([]byte, []int) {
	return file_gameconfig_proto_rawDescGZIP(), []int{43, 0}
}

func (x *ListShopTabConfig_ShopTabConfig) GetId() int32 {
	if x != nil && x.Id != nil {
		return *x.Id
	}
	return 0
}

func (x *ListShopTabConfig_ShopTabConfig) GetType() EShopType {
	if x != nil && x.Type != nil {
		return *x.Type
	}
	return EShopType_EShopType_PropShop
}

func (x *ListShopTabConfig_ShopTabConfig) GetGroupId() int32 {
	if x != nil && x.GroupId != nil {
		return *x.GroupId
	}
	return 0
}

func (x *ListShopTabConfig_ShopTabConfig) GetGoodsCount() int32 {
	if x != nil && x.GoodsCount != nil {
		return *x.GoodsCount
	}
	return 0
}

func (x *ListShopTabConfig_ShopTabConfig) GetSystemId() ESystemId {
	if x != nil && x.SystemId != nil {
		return *x.SystemId
	}
	return ESystemId_ESystemId_Invalid
}

func (x *ListShopTabConfig_ShopTabConfig) GetRefreshCost() []*ItemData {
	if x != nil {
		return x.RefreshCost
	}
	return nil
}

type ListStoryPlotConditionConfig_StoryPlotConditionConfig struct {
	state         protoimpl.MessageState   `protogen:"open.v1"`
	Id            *int32                   `protobuf:"varint,2,opt,name=Id" json:"Id,omitempty"`
	Group         *int32                   `protobuf:"varint,4,opt,name=Group" json:"Group,omitempty"`
	Type          *EStoryPlotConditionType `protobuf:"varint,5,opt,name=Type,enum=pb.EStoryPlotConditionType" json:"Type,omitempty"`
	Args          []string                 `protobuf:"bytes,6,rep,name=Args" json:"Args,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListStoryPlotConditionConfig_StoryPlotConditionConfig) Reset() {
	*x = ListStoryPlotConditionConfig_StoryPlotConditionConfig{}
	mi := &file_gameconfig_proto_msgTypes[83]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListStoryPlotConditionConfig_StoryPlotConditionConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListStoryPlotConditionConfig_StoryPlotConditionConfig) ProtoMessage() {}

func (x *ListStoryPlotConditionConfig_StoryPlotConditionConfig) ProtoReflect() protoreflect.Message {
	mi := &file_gameconfig_proto_msgTypes[83]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListStoryPlotConditionConfig_StoryPlotConditionConfig.ProtoReflect.Descriptor instead.
func (*ListStoryPlotConditionConfig_StoryPlotConditionConfig) Descriptor() ([]byte, []int) {
	return file_gameconfig_proto_rawDescGZIP(), []int{44, 0}
}

func (x *ListStoryPlotConditionConfig_StoryPlotConditionConfig) GetId() int32 {
	if x != nil && x.Id != nil {
		return *x.Id
	}
	return 0
}

func (x *ListStoryPlotConditionConfig_StoryPlotConditionConfig) GetGroup() int32 {
	if x != nil && x.Group != nil {
		return *x.Group
	}
	return 0
}

func (x *ListStoryPlotConditionConfig_StoryPlotConditionConfig) GetType() EStoryPlotConditionType {
	if x != nil && x.Type != nil {
		return *x.Type
	}
	return EStoryPlotConditionType_EStoryPlotConditionType_None
}

func (x *ListStoryPlotConditionConfig_StoryPlotConditionConfig) GetArgs() []string {
	if x != nil {
		return x.Args
	}
	return nil
}

type ListStoryPlotConfig_StoryPlotConfig struct {
	state          protoimpl.MessageState `protogen:"open.v1"`
	Id             *int32                 `protobuf:"varint,2,opt,name=Id" json:"Id,omitempty"`
	Group          *int32                 `protobuf:"varint,4,opt,name=Group" json:"Group,omitempty"`
	PreId          *int32                 `protobuf:"varint,5,opt,name=PreId" json:"PreId,omitempty"`
	ConditionGroup *int32                 `protobuf:"varint,6,opt,name=ConditionGroup" json:"ConditionGroup,omitempty"`
	InstructGroup  *int32                 `protobuf:"varint,7,opt,name=InstructGroup" json:"InstructGroup,omitempty"`
	unknownFields  protoimpl.UnknownFields
	sizeCache      protoimpl.SizeCache
}

func (x *ListStoryPlotConfig_StoryPlotConfig) Reset() {
	*x = ListStoryPlotConfig_StoryPlotConfig{}
	mi := &file_gameconfig_proto_msgTypes[84]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListStoryPlotConfig_StoryPlotConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListStoryPlotConfig_StoryPlotConfig) ProtoMessage() {}

func (x *ListStoryPlotConfig_StoryPlotConfig) ProtoReflect() protoreflect.Message {
	mi := &file_gameconfig_proto_msgTypes[84]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListStoryPlotConfig_StoryPlotConfig.ProtoReflect.Descriptor instead.
func (*ListStoryPlotConfig_StoryPlotConfig) Descriptor() ([]byte, []int) {
	return file_gameconfig_proto_rawDescGZIP(), []int{45, 0}
}

func (x *ListStoryPlotConfig_StoryPlotConfig) GetId() int32 {
	if x != nil && x.Id != nil {
		return *x.Id
	}
	return 0
}

func (x *ListStoryPlotConfig_StoryPlotConfig) GetGroup() int32 {
	if x != nil && x.Group != nil {
		return *x.Group
	}
	return 0
}

func (x *ListStoryPlotConfig_StoryPlotConfig) GetPreId() int32 {
	if x != nil && x.PreId != nil {
		return *x.PreId
	}
	return 0
}

func (x *ListStoryPlotConfig_StoryPlotConfig) GetConditionGroup() int32 {
	if x != nil && x.ConditionGroup != nil {
		return *x.ConditionGroup
	}
	return 0
}

func (x *ListStoryPlotConfig_StoryPlotConfig) GetInstructGroup() int32 {
	if x != nil && x.InstructGroup != nil {
		return *x.InstructGroup
	}
	return 0
}

type ListStoryPlotGroupConfig_StoryPlotGroupConfig struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            *int32                 `protobuf:"varint,2,opt,name=Id" json:"Id,omitempty"`
	Type          *EStoryPlotType        `protobuf:"varint,4,opt,name=Type,enum=pb.EStoryPlotType" json:"Type,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListStoryPlotGroupConfig_StoryPlotGroupConfig) Reset() {
	*x = ListStoryPlotGroupConfig_StoryPlotGroupConfig{}
	mi := &file_gameconfig_proto_msgTypes[85]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListStoryPlotGroupConfig_StoryPlotGroupConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListStoryPlotGroupConfig_StoryPlotGroupConfig) ProtoMessage() {}

func (x *ListStoryPlotGroupConfig_StoryPlotGroupConfig) ProtoReflect() protoreflect.Message {
	mi := &file_gameconfig_proto_msgTypes[85]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListStoryPlotGroupConfig_StoryPlotGroupConfig.ProtoReflect.Descriptor instead.
func (*ListStoryPlotGroupConfig_StoryPlotGroupConfig) Descriptor() ([]byte, []int) {
	return file_gameconfig_proto_rawDescGZIP(), []int{46, 0}
}

func (x *ListStoryPlotGroupConfig_StoryPlotGroupConfig) GetId() int32 {
	if x != nil && x.Id != nil {
		return *x.Id
	}
	return 0
}

func (x *ListStoryPlotGroupConfig_StoryPlotGroupConfig) GetType() EStoryPlotType {
	if x != nil && x.Type != nil {
		return *x.Type
	}
	return EStoryPlotType_EStoryPlotType_None
}

type ListStoryPlotInstructsConfig_StoryPlotInstructsConfig struct {
	state         protoimpl.MessageState           `protogen:"open.v1"`
	Id            *int32                           `protobuf:"varint,2,opt,name=Id" json:"Id,omitempty"`
	Group         *int32                           `protobuf:"varint,4,opt,name=Group" json:"Group,omitempty"`
	Type          *EStoryPlotInstructType          `protobuf:"varint,5,opt,name=Type,enum=pb.EStoryPlotInstructType" json:"Type,omitempty"`
	Args          []string                         `protobuf:"bytes,6,rep,name=Args" json:"Args,omitempty"`
	Wait          *int32                           `protobuf:"varint,7,opt,name=Wait" json:"Wait,omitempty"`
	Conditon      *EStoryPlotInstructConditionType `protobuf:"varint,8,opt,name=Conditon,enum=pb.EStoryPlotInstructConditionType" json:"Conditon,omitempty"`
	ConditionArgs []string                         `protobuf:"bytes,9,rep,name=conditionArgs" json:"conditionArgs,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListStoryPlotInstructsConfig_StoryPlotInstructsConfig) Reset() {
	*x = ListStoryPlotInstructsConfig_StoryPlotInstructsConfig{}
	mi := &file_gameconfig_proto_msgTypes[86]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListStoryPlotInstructsConfig_StoryPlotInstructsConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListStoryPlotInstructsConfig_StoryPlotInstructsConfig) ProtoMessage() {}

func (x *ListStoryPlotInstructsConfig_StoryPlotInstructsConfig) ProtoReflect() protoreflect.Message {
	mi := &file_gameconfig_proto_msgTypes[86]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListStoryPlotInstructsConfig_StoryPlotInstructsConfig.ProtoReflect.Descriptor instead.
func (*ListStoryPlotInstructsConfig_StoryPlotInstructsConfig) Descriptor() ([]byte, []int) {
	return file_gameconfig_proto_rawDescGZIP(), []int{47, 0}
}

func (x *ListStoryPlotInstructsConfig_StoryPlotInstructsConfig) GetId() int32 {
	if x != nil && x.Id != nil {
		return *x.Id
	}
	return 0
}

func (x *ListStoryPlotInstructsConfig_StoryPlotInstructsConfig) GetGroup() int32 {
	if x != nil && x.Group != nil {
		return *x.Group
	}
	return 0
}

func (x *ListStoryPlotInstructsConfig_StoryPlotInstructsConfig) GetType() EStoryPlotInstructType {
	if x != nil && x.Type != nil {
		return *x.Type
	}
	return EStoryPlotInstructType_EStoryPlotInstructType_None
}

func (x *ListStoryPlotInstructsConfig_StoryPlotInstructsConfig) GetArgs() []string {
	if x != nil {
		return x.Args
	}
	return nil
}

func (x *ListStoryPlotInstructsConfig_StoryPlotInstructsConfig) GetWait() int32 {
	if x != nil && x.Wait != nil {
		return *x.Wait
	}
	return 0
}

func (x *ListStoryPlotInstructsConfig_StoryPlotInstructsConfig) GetConditon() EStoryPlotInstructConditionType {
	if x != nil && x.Conditon != nil {
		return *x.Conditon
	}
	return EStoryPlotInstructConditionType_EStoryPlotInstructConditionType_None
}

func (x *ListStoryPlotInstructsConfig_StoryPlotInstructsConfig) GetConditionArgs() []string {
	if x != nil {
		return x.ConditionArgs
	}
	return nil
}

type ListSystemOpenConfig_SystemOpenConfig struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            *int32                 `protobuf:"varint,2,opt,name=Id" json:"Id,omitempty"`
	ConditionId   *int32                 `protobuf:"varint,8,opt,name=ConditionId" json:"ConditionId,omitempty"`
	ParentId      *int32                 `protobuf:"varint,9,opt,name=ParentId" json:"ParentId,omitempty"`
	Version       *int32                 `protobuf:"varint,10,opt,name=Version" json:"Version,omitempty"`
	Disabled      *bool                  `protobuf:"varint,12,opt,name=Disabled" json:"Disabled,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListSystemOpenConfig_SystemOpenConfig) Reset() {
	*x = ListSystemOpenConfig_SystemOpenConfig{}
	mi := &file_gameconfig_proto_msgTypes[87]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListSystemOpenConfig_SystemOpenConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListSystemOpenConfig_SystemOpenConfig) ProtoMessage() {}

func (x *ListSystemOpenConfig_SystemOpenConfig) ProtoReflect() protoreflect.Message {
	mi := &file_gameconfig_proto_msgTypes[87]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListSystemOpenConfig_SystemOpenConfig.ProtoReflect.Descriptor instead.
func (*ListSystemOpenConfig_SystemOpenConfig) Descriptor() ([]byte, []int) {
	return file_gameconfig_proto_rawDescGZIP(), []int{48, 0}
}

func (x *ListSystemOpenConfig_SystemOpenConfig) GetId() int32 {
	if x != nil && x.Id != nil {
		return *x.Id
	}
	return 0
}

func (x *ListSystemOpenConfig_SystemOpenConfig) GetConditionId() int32 {
	if x != nil && x.ConditionId != nil {
		return *x.ConditionId
	}
	return 0
}

func (x *ListSystemOpenConfig_SystemOpenConfig) GetParentId() int32 {
	if x != nil && x.ParentId != nil {
		return *x.ParentId
	}
	return 0
}

func (x *ListSystemOpenConfig_SystemOpenConfig) GetVersion() int32 {
	if x != nil && x.Version != nil {
		return *x.Version
	}
	return 0
}

func (x *ListSystemOpenConfig_SystemOpenConfig) GetDisabled() bool {
	if x != nil && x.Disabled != nil {
		return *x.Disabled
	}
	return false
}

type ListTagConfig_TagConfig struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            *int32                 `protobuf:"varint,2,opt,name=Id" json:"Id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListTagConfig_TagConfig) Reset() {
	*x = ListTagConfig_TagConfig{}
	mi := &file_gameconfig_proto_msgTypes[88]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListTagConfig_TagConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListTagConfig_TagConfig) ProtoMessage() {}

func (x *ListTagConfig_TagConfig) ProtoReflect() protoreflect.Message {
	mi := &file_gameconfig_proto_msgTypes[88]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListTagConfig_TagConfig.ProtoReflect.Descriptor instead.
func (*ListTagConfig_TagConfig) Descriptor() ([]byte, []int) {
	return file_gameconfig_proto_rawDescGZIP(), []int{49, 0}
}

func (x *ListTagConfig_TagConfig) GetId() int32 {
	if x != nil && x.Id != nil {
		return *x.Id
	}
	return 0
}

type ListTileMapConfig_TileMapConfig struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            *int32                 `protobuf:"varint,2,opt,name=Id" json:"Id,omitempty"`
	MapType       *int32                 `protobuf:"varint,4,opt,name=MapType" json:"MapType,omitempty"`
	Width         *int32                 `protobuf:"varint,5,opt,name=Width" json:"Width,omitempty"`
	Height        *int32                 `protobuf:"varint,6,opt,name=Height" json:"Height,omitempty"`
	GridSize      *float32               `protobuf:"fixed32,7,opt,name=GridSize" json:"GridSize,omitempty"`
	AssetBundle   *string                `protobuf:"bytes,8,opt,name=AssetBundle" json:"AssetBundle,omitempty"`
	PrefabName    *string                `protobuf:"bytes,9,opt,name=PrefabName" json:"PrefabName,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListTileMapConfig_TileMapConfig) Reset() {
	*x = ListTileMapConfig_TileMapConfig{}
	mi := &file_gameconfig_proto_msgTypes[89]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListTileMapConfig_TileMapConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListTileMapConfig_TileMapConfig) ProtoMessage() {}

func (x *ListTileMapConfig_TileMapConfig) ProtoReflect() protoreflect.Message {
	mi := &file_gameconfig_proto_msgTypes[89]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListTileMapConfig_TileMapConfig.ProtoReflect.Descriptor instead.
func (*ListTileMapConfig_TileMapConfig) Descriptor() ([]byte, []int) {
	return file_gameconfig_proto_rawDescGZIP(), []int{50, 0}
}

func (x *ListTileMapConfig_TileMapConfig) GetId() int32 {
	if x != nil && x.Id != nil {
		return *x.Id
	}
	return 0
}

func (x *ListTileMapConfig_TileMapConfig) GetMapType() int32 {
	if x != nil && x.MapType != nil {
		return *x.MapType
	}
	return 0
}

func (x *ListTileMapConfig_TileMapConfig) GetWidth() int32 {
	if x != nil && x.Width != nil {
		return *x.Width
	}
	return 0
}

func (x *ListTileMapConfig_TileMapConfig) GetHeight() int32 {
	if x != nil && x.Height != nil {
		return *x.Height
	}
	return 0
}

func (x *ListTileMapConfig_TileMapConfig) GetGridSize() float32 {
	if x != nil && x.GridSize != nil {
		return *x.GridSize
	}
	return 0
}

func (x *ListTileMapConfig_TileMapConfig) GetAssetBundle() string {
	if x != nil && x.AssetBundle != nil {
		return *x.AssetBundle
	}
	return ""
}

func (x *ListTileMapConfig_TileMapConfig) GetPrefabName() string {
	if x != nil && x.PrefabName != nil {
		return *x.PrefabName
	}
	return ""
}

type ListUserNameConfig_UserNameConfig struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            *int32                 `protobuf:"varint,2,opt,name=Id" json:"Id,omitempty"`
	Name          *string                `protobuf:"bytes,3,opt,name=name" json:"name,omitempty"`
	Type          *int32                 `protobuf:"varint,4,opt,name=type" json:"type,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListUserNameConfig_UserNameConfig) Reset() {
	*x = ListUserNameConfig_UserNameConfig{}
	mi := &file_gameconfig_proto_msgTypes[90]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListUserNameConfig_UserNameConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListUserNameConfig_UserNameConfig) ProtoMessage() {}

func (x *ListUserNameConfig_UserNameConfig) ProtoReflect() protoreflect.Message {
	mi := &file_gameconfig_proto_msgTypes[90]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListUserNameConfig_UserNameConfig.ProtoReflect.Descriptor instead.
func (*ListUserNameConfig_UserNameConfig) Descriptor() ([]byte, []int) {
	return file_gameconfig_proto_rawDescGZIP(), []int{51, 0}
}

func (x *ListUserNameConfig_UserNameConfig) GetId() int32 {
	if x != nil && x.Id != nil {
		return *x.Id
	}
	return 0
}

func (x *ListUserNameConfig_UserNameConfig) GetName() string {
	if x != nil && x.Name != nil {
		return *x.Name
	}
	return ""
}

func (x *ListUserNameConfig_UserNameConfig) GetType() int32 {
	if x != nil && x.Type != nil {
		return *x.Type
	}
	return 0
}

var File_gameconfig_proto protoreflect.FileDescriptor

const file_gameconfig_proto_rawDesc = "" +
	"\n" +
	"\x10gameconfig.proto\x12\x02pb\"%\n" +
	"\aVector2\x12\f\n" +
	"\x01x\x18\x01 \x01(\x02R\x01x\x12\f\n" +
	"\x01y\x18\x02 \x01(\x02R\x01y\"3\n" +
	"\aVector3\x12\f\n" +
	"\x01x\x18\x01 \x01(\x02R\x01x\x12\f\n" +
	"\x01y\x18\x02 \x01(\x02R\x01y\x12\f\n" +
	"\x01z\x18\x03 \x01(\x02R\x01z\"?\n" +
	"\bAttrData\x12\x1d\n" +
	"\x04Attr\x18\x01 \x01(\x0e2\t.pb.EAttrR\x04Attr\x12\x14\n" +
	"\x05Value\x18\x02 \x01(\x03R\x05Value\"0\n" +
	"\bItemData\x12\x0e\n" +
	"\x02Id\x18\x01 \x01(\x05R\x02Id\x12\x14\n" +
	"\x05Count\x18\x02 \x01(\x05R\x05Count\"C\n" +
	"\bHeroAttr\x12!\n" +
	"\x04Attr\x18\x01 \x01(\x0e2\r.pb.EHeroAttrR\x04Attr\x12\x14\n" +
	"\x05Value\x18\x02 \x01(\x03R\x05Value\"J\n" +
	"\x0eRandomItemData\x12\x0e\n" +
	"\x02Id\x18\x01 \x01(\x05R\x02Id\x12\x14\n" +
	"\x05Count\x18\x02 \x01(\x03R\x05Count\x12\x12\n" +
	"\x04Prop\x18\x03 \x01(\x05R\x04Prop\"^\n" +
	"\x0eAwardGroupData\x12\x0e\n" +
	"\x02Id\x18\x01 \x01(\x05R\x02Id\x12\x14\n" +
	"\x05Count\x18\x02 \x01(\x05R\x05Count\x12\x12\n" +
	"\x04Prop\x18\x03 \x01(\x05R\x04Prop\x12\x12\n" +
	"\x04Rate\x18\x04 \x01(\x05R\x04Rate\"I\n" +
	"\x11ConditionWithDesc\x12 \n" +
	"\vConditionId\x18\x01 \x01(\x05R\vConditionId\x12\x12\n" +
	"\x04Desc\x18\x02 \x01(\tR\x04Desc\":\n" +
	"\fItemProperty\x12\x16\n" +
	"\x06ItemId\x18\x01 \x01(\x05R\x06ItemId\x12\x12\n" +
	"\x04Prop\x18\x02 \x01(\x05R\x04Prop\"*\n" +
	"\fGridPosition\x12\f\n" +
	"\x01x\x18\x01 \x01(\x05R\x01x\x12\f\n" +
	"\x01y\x18\x02 \x01(\x05R\x01y\"m\n" +
	"\x0fGuideDetailTips\x12\x12\n" +
	"\x04Tips\x18\x01 \x01(\tR\x04Tips\x12\x12\n" +
	"\x04Icon\x18\x02 \x01(\tR\x04Icon\x12\x18\n" +
	"\aOffsetX\x18\x03 \x01(\x05R\aOffsetX\x12\x18\n" +
	"\aOffsetY\x18\x04 \x01(\x05R\aOffsetY\"9\n" +
	"\rCountPropData\x12\x14\n" +
	"\x05Count\x18\x01 \x01(\x05R\x05Count\x12\x12\n" +
	"\x04Prop\x18\x02 \x01(\x05R\x04Prop\"\x18\n" +
	"\b_v_int32\x12\f\n" +
	"\x01a\x18\x01 \x03(\x05R\x01a\"q\n" +
	"\x12listAIActionConfig\x129\n" +
	"\x04list\x18\x01 \x03(\v2%.pb.listAIActionConfig.AIActionConfigR\x04list\x1a \n" +
	"\x0eAIActionConfig\x12\x0e\n" +
	"\x02Id\x18\x02 \x01(\x05R\x02Id\"\xa7\x01\n" +
	"\x15listAIConditionConfig\x12?\n" +
	"\x04list\x18\x01 \x03(\v2+.pb.listAIConditionConfig.AIConditionConfigR\x04list\x1aM\n" +
	"\x11AIConditionConfig\x12\x0e\n" +
	"\x02Id\x18\x02 \x01(\x05R\x02Id\x12(\n" +
	"\x04Type\x18\x03 \x01(\x0e2\x14.pb.EAIConditionTypeR\x04Type\"i\n" +
	"\x10listAINodeConfig\x125\n" +
	"\x04list\x18\x01 \x03(\v2!.pb.listAINodeConfig.AINodeConfigR\x04list\x1a\x1e\n" +
	"\fAINodeConfig\x12\x0e\n" +
	"\x02Id\x18\x02 \x01(\x05R\x02Id\"i\n" +
	"\x10listAvatarConfig\x125\n" +
	"\x04list\x18\x01 \x03(\v2!.pb.listAvatarConfig.AvatarConfigR\x04list\x1a\x1e\n" +
	"\fAvatarConfig\x12\x0e\n" +
	"\x02Id\x18\x02 \x01(\x05R\x02Id\"\xca\x01\n" +
	"\x0flistAwardConfig\x123\n" +
	"\x04list\x18\x01 \x03(\v2\x1f.pb.listAwardConfig.AwardConfigR\x04list\x1a\x81\x01\n" +
	"\vAwardConfig\x12\x0e\n" +
	"\x02Id\x18\x02 \x01(\x05R\x02Id\x12\"\n" +
	"\x04Type\x18\x04 \x01(\x0e2\x0e.pb.EAwardTypeR\x04Type\x12(\n" +
	"\x05Award\x18\x05 \x03(\v2\x12.pb.AwardGroupDataR\x05Award\x12\x14\n" +
	"\x05Count\x18\x06 \x01(\x05R\x05Count\"\x81\x02\n" +
	"\x14listAwardGroupConfig\x12=\n" +
	"\x04list\x18\x01 \x03(\v2).pb.listAwardGroupConfig.AwardGroupConfigR\x04list\x1a\xa9\x01\n" +
	"\x10AwardGroupConfig\x12\x0e\n" +
	"\x02Id\x18\x02 \x01(\x05R\x02Id\x12\x18\n" +
	"\aGroupId\x18\x04 \x01(\x05R\aGroupId\x12'\n" +
	"\x04Type\x18\x05 \x01(\x0e2\x13.pb.EAwardGroupTypeR\x04Type\x12\x16\n" +
	"\x06ItemId\x18\x06 \x01(\x05R\x06ItemId\x12\x14\n" +
	"\x05Count\x18\b \x01(\x03R\x05Count\x12\x14\n" +
	"\x05Value\x18\t \x01(\x05R\x05Value\"\xb8\x02\n" +
	"\x13listConditionConfig\x12;\n" +
	"\x04list\x18\x01 \x03(\v2'.pb.listConditionConfig.ConditionConfigR\x04list\x1a\xe3\x01\n" +
	"\x0fConditionConfig\x12\x0e\n" +
	"\x02Id\x18\x02 \x01(\x05R\x02Id\x12&\n" +
	"\x04Type\x18\x05 \x01(\x0e2\x12.pb.EConditionTypeR\x04Type\x12\x16\n" +
	"\x06Target\x18\x06 \x01(\x05R\x06Target\x12\x16\n" +
	"\x06Param1\x18\a \x01(\x05R\x06Param1\x12\x16\n" +
	"\x06Param2\x18\b \x01(\x05R\x06Param2\x12\x16\n" +
	"\x06Param3\x18\t \x01(\x05R\x06Param3\x12\x16\n" +
	"\x06Param4\x18\n" +
	" \x01(\x05R\x06Param4\x12 \n" +
	"\vCondVersion\x18\v \x01(\x05R\vCondVersion\"\x80\x03\n" +
	"\x1alistConditionTriggerConfig\x12I\n" +
	"\x04list\x18\x01 \x03(\v25.pb.listConditionTriggerConfig.ConditionTriggerConfigR\x04list\x1a\x96\x02\n" +
	"\x16ConditionTriggerConfig\x12\x0e\n" +
	"\x02Id\x18\x02 \x01(\x05R\x02Id\x12.\n" +
	"\bCondType\x18\x04 \x01(\x0e2\x12.pb.EConditionTypeR\bCondType\x12\x1e\n" +
	"\n" +
	"CondTarget\x18\x05 \x01(\x05R\n" +
	"CondTarget\x12\x1e\n" +
	"\n" +
	"CondParams\x18\x06 \x03(\x05R\n" +
	"CondParams\x12 \n" +
	"\vCondVersion\x18\a \x01(\x05R\vCondVersion\x124\n" +
	"\n" +
	"TiggerType\x18\b \x01(\x0e2\x14.pb.ECondTriggerTypeR\n" +
	"TiggerType\x12$\n" +
	"\rTriggerParams\x18\t \x03(\tR\rTriggerParams\"\xf2\x01\n" +
	"\x0flistConstConfig\x123\n" +
	"\x04list\x18\x01 \x03(\v2\x1f.pb.listConstConfig.ConstConfigR\x04list\x1a\xa9\x01\n" +
	"\vConstConfig\x12\x0e\n" +
	"\x02Id\x18\x02 \x01(\x05R\x02Id\x12\x12\n" +
	"\x04VInt\x18\x04 \x01(\x05R\x04VInt\x12\x14\n" +
	"\x05VLong\x18\x05 \x01(\x03R\x05VLong\x12\x1a\n" +
	"\bVIntList\x18\x06 \x03(\x05R\bVIntList\x12*\n" +
	"\tVIntLList\x18\a \x03(\v2\f.pb._v_int32R\tVIntLList\x12\x18\n" +
	"\aVString\x18\b \x01(\tR\aVString\"\xc1\x03\n" +
	"\x18listConstShopGoodsConfig\x12E\n" +
	"\x04list\x18\x01 \x03(\v21.pb.listConstShopGoodsConfig.ConstShopGoodsConfigR\x04list\x1a\xdd\x02\n" +
	"\x14ConstShopGoodsConfig\x12\x0e\n" +
	"\x02Id\x18\x02 \x01(\x05R\x02Id\x12(\n" +
	"\bItemData\x18\x06 \x01(\v2\f.pb.ItemDataR\bItemData\x12(\n" +
	"\bCostItem\x18\a \x01(\v2\f.pb.ItemDataR\bCostItem\x12/\n" +
	"\tLimitType\x18\b \x01(\x0e2\x11.pb.EShopBuyLimitR\tLimitType\x12\x1e\n" +
	"\n" +
	"LimitCount\x18\t \x01(\x05R\n" +
	"LimitCount\x12.\n" +
	"\bCondType\x18\n" +
	" \x01(\x0e2\x12.pb.EConditionTypeR\bCondType\x12\x1e\n" +
	"\n" +
	"CondTarget\x18\v \x01(\x05R\n" +
	"CondTarget\x12\x1e\n" +
	"\n" +
	"CondParams\x18\f \x03(\x05R\n" +
	"CondParams\x12 \n" +
	"\vCondVersion\x18\r \x01(\x05R\vCondVersion\"\xa6\x01\n" +
	"\x12listGainItemConfig\x129\n" +
	"\x04list\x18\x01 \x03(\v2%.pb.listGainItemConfig.GainItemConfigR\x04list\x1aU\n" +
	"\x0eGainItemConfig\x12\x0e\n" +
	"\x02Id\x18\x02 \x01(\x05R\x02Id\x123\n" +
	"\n" +
	"EffectType\x18\x04 \x01(\x0e2\x13.pb.EGainItemEffectR\n" +
	"EffectType\"\xb7\x01\n" +
	"\x0flistGuideConfig\x123\n" +
	"\x04list\x18\x01 \x03(\v2\x1f.pb.listGuideConfig.GuideConfigR\x04list\x1ao\n" +
	"\vGuideConfig\x12\x0e\n" +
	"\x02Id\x18\x02 \x01(\x05R\x02Id\x12\x18\n" +
	"\aGroupId\x18\x03 \x01(\x05R\aGroupId\x12\x1e\n" +
	"\x04Type\x18\x06 \x01(\x0e2\n" +
	".pb.EGuideR\x04Type\x12\x16\n" +
	"\x06Params\x18\t \x03(\tR\x06Params\"\xb4\x02\n" +
	"\x14listGuideGroupConfig\x12=\n" +
	"\x04list\x18\x01 \x03(\v2).pb.listGuideGroupConfig.GuideGroupConfigR\x04list\x1a\xdc\x01\n" +
	"\x10GuideGroupConfig\x12\x0e\n" +
	"\x02Id\x18\x02 \x01(\x05R\x02Id\x12&\n" +
	"\x0eRertyOnFailure\x18\x04 \x01(\bR\x0eRertyOnFailure\x12.\n" +
	"\bCondType\x18\x06 \x01(\x0e2\x12.pb.EConditionTypeR\bCondType\x12\x1e\n" +
	"\n" +
	"CondTarget\x18\a \x01(\x05R\n" +
	"CondTarget\x12\x1e\n" +
	"\n" +
	"CondParams\x18\b \x03(\x05R\n" +
	"CondParams\x12 \n" +
	"\vCondVersion\x18\t \x01(\x05R\vCondVersion\"\xb2\x02\n" +
	"\x16listHeadPortraitConfig\x12A\n" +
	"\x04list\x18\x01 \x03(\v2-.pb.listHeadPortraitConfig.HeadPortraitConfigR\x04list\x1a\xd4\x01\n" +
	"\x12HeadPortraitConfig\x12\x0e\n" +
	"\x02Id\x18\x02 \x01(\x05R\x02Id\x12\x1c\n" +
	"\tIsDefault\x18\x04 \x01(\bR\tIsDefault\x12.\n" +
	"\bCondType\x18\a \x01(\x0e2\x12.pb.EConditionTypeR\bCondType\x12\x1e\n" +
	"\n" +
	"CondTarget\x18\b \x01(\x05R\n" +
	"CondTarget\x12\x1e\n" +
	"\n" +
	"CondParams\x18\t \x03(\x05R\n" +
	"CondParams\x12 \n" +
	"\vCondVersion\x18\n" +
	" \x01(\x05R\vCondVersion\"\xf4\x02\n" +
	"\x0elistHeroConfig\x121\n" +
	"\x04list\x18\x01 \x03(\v2\x1d.pb.listHeroConfig.HeroConfigR\x04list\x1a\xae\x02\n" +
	"\n" +
	"HeroConfig\x12\x0e\n" +
	"\x02Id\x18\x02 \x01(\x05R\x02Id\x12&\n" +
	"\aQuality\x18\x06 \x01(\x0e2\f.pb.EQualityR\aQuality\x12\x12\n" +
	"\x04Star\x18\a \x01(\x05R\x04Star\x12*\n" +
	"\tNeedChips\x18\b \x01(\v2\f.pb.ItemDataR\tNeedChips\x12(\n" +
	"\bBaseAttr\x18\t \x03(\v2\f.pb.HeroAttrR\bBaseAttr\x12\x1e\n" +
	"\n" +
	"InitEnergy\x18\n" +
	" \x01(\x05R\n" +
	"InitEnergy\x12\x1e\n" +
	"\n" +
	"LevelGroup\x18\v \x01(\x05R\n" +
	"LevelGroup\x12 \n" +
	"\vStarGroupId\x18\f \x01(\x05R\vStarGroupId\x12\x1c\n" +
	"\tDuplicate\x18\r \x01(\bR\tDuplicate\"\xf4\x01\n" +
	"\x13listHeroLevelConfig\x12;\n" +
	"\x04list\x18\x01 \x03(\v2'.pb.listHeroLevelConfig.HeroLevelConfigR\x04list\x1a\x9f\x01\n" +
	"\x0fHeroLevelConfig\x12\x0e\n" +
	"\x02Id\x18\x02 \x01(\x05R\x02Id\x12\x14\n" +
	"\x05Level\x18\x03 \x01(\x05R\x05Level\x12\x18\n" +
	"\aGroupId\x18\x04 \x01(\x05R\aGroupId\x12\"\n" +
	"\x05Attrs\x18\x05 \x03(\v2\f.pb.HeroAttrR\x05Attrs\x12(\n" +
	"\bCostItem\x18\x06 \x01(\v2\f.pb.ItemDataR\bCostItem\"\x97\x01\n" +
	"\x15listHeroQualityConfig\x12?\n" +
	"\x04list\x18\x01 \x03(\v2+.pb.listHeroQualityConfig.HeroQualityConfigR\x04list\x1a=\n" +
	"\x11HeroQualityConfig\x12\x0e\n" +
	"\x02Id\x18\x02 \x01(\x05R\x02Id\x12\x18\n" +
	"\aMaxStar\x18\x04 \x01(\x05R\aMaxStar\"\x96\x02\n" +
	"\x12listHeroStarConfig\x129\n" +
	"\x04list\x18\x01 \x03(\v2%.pb.listHeroStarConfig.HeroStarConfigR\x04list\x1a\xc4\x01\n" +
	"\x0eHeroStarConfig\x12\x0e\n" +
	"\x02Id\x18\x02 \x01(\x05R\x02Id\x12\x18\n" +
	"\aGroupId\x18\x03 \x01(\x05R\aGroupId\x12\x1c\n" +
	"\tStarLevel\x18\x04 \x01(\x05R\tStarLevel\x12\x1c\n" +
	"\tCostChips\x18\x05 \x01(\x05R\tCostChips\x12(\n" +
	"\bCostItem\x18\x06 \x01(\v2\f.pb.ItemDataR\bCostItem\x12\"\n" +
	"\x05Attrs\x18\a \x03(\v2\f.pb.HeroAttrR\x05Attrs\"\xb1\x04\n" +
	"\x0elistItemConfig\x121\n" +
	"\x04list\x18\x01 \x03(\v2\x1d.pb.listItemConfig.ItemConfigR\x04list\x1a\xeb\x03\n" +
	"\n" +
	"ItemConfig\x12\x0e\n" +
	"\x02Id\x18\x02 \x01(\x05R\x02Id\x12\x12\n" +
	"\x04Name\x18\x03 \x01(\tR\x04Name\x12\x1d\n" +
	"\x04Type\x18\x04 \x01(\x0e2\t.pb.EItemR\x04Type\x12\x18\n" +
	"\aSubType\x18\x05 \x01(\x05R\aSubType\x12.\n" +
	"\x12PersonalDailyLimit\x18\x06 \x01(\x05R\x12PersonalDailyLimit\x12*\n" +
	"\x10GlobalDailyLimit\x18\a \x01(\x05R\x10GlobalDailyLimit\x12(\n" +
	"\x0fGlobalWeekLimit\x18\b \x01(\x05R\x0fGlobalWeekLimit\x12*\n" +
	"\x10GlobalMonthLimit\x18\t \x01(\x05R\x10GlobalMonthLimit\x12\x1c\n" +
	"\tStartTime\x18\n" +
	" \x01(\x03R\tStartTime\x12\x1e\n" +
	"\n" +
	"ExpireTime\x18\v \x01(\x03R\n" +
	"ExpireTime\x12\x1e\n" +
	"\n" +
	"ValidHours\x18\f \x01(\x05R\n" +
	"ValidHours\x12\x18\n" +
	"\aOverlay\x18\r \x01(\x05R\aOverlay\x12\x12\n" +
	"\x04Bind\x18\x0e \x01(\x05R\x04Bind\x12\x12\n" +
	"\x04Icon\x18\x0f \x01(\tR\x04Icon\x12\x1a\n" +
	"\bTinyIcon\x18\x10 \x01(\tR\bTinyIcon\x12\x12\n" +
	"\x04Desc\x18\x11 \x01(\tR\x04Desc\"\x85\x01\n" +
	"\x0flistLevelConfig\x123\n" +
	"\x04list\x18\x01 \x03(\v2\x1f.pb.listLevelConfig.LevelConfigR\x04list\x1a=\n" +
	"\vLevelConfig\x12\x0e\n" +
	"\x02Id\x18\x02 \x01(\x05R\x02Id\x12\x1e\n" +
	"\n" +
	"Prosperity\x18\x03 \x01(\x05R\n" +
	"Prosperity\"\xda\x01\n" +
	"\x0elistMailConfig\x121\n" +
	"\x04list\x18\x01 \x03(\v2\x1d.pb.listMailConfig.MailConfigR\x04list\x1a\x94\x01\n" +
	"\n" +
	"MailConfig\x12\x0e\n" +
	"\x02Id\x18\x02 \x01(\x05R\x02Id\x12\x14\n" +
	"\x05Title\x18\x04 \x01(\tR\x05Title\x12\x18\n" +
	"\aContent\x18\x05 \x01(\tR\aContent\x12.\n" +
	"\vAttachments\x18\x06 \x03(\v2\f.pb.ItemDataR\vAttachments\x12\x16\n" +
	"\x06Sender\x18\a \x01(\tR\x06Sender\"\xa3\x01\n" +
	"\x17listMainCityScaleConfig\x12C\n" +
	"\x04list\x18\x01 \x03(\v2/.pb.listMainCityScaleConfig.MainCityScaleConfigR\x04list\x1aC\n" +
	"\x13MainCityScaleConfig\x12\x0e\n" +
	"\x02Id\x18\x02 \x01(\x05R\x02Id\x12\x1c\n" +
	"\tCondition\x18\x06 \x01(\x05R\tCondition\"\xb7\x01\n" +
	"\x19listMainTaskChapterConfig\x12G\n" +
	"\x04list\x18\x01 \x03(\v23.pb.listMainTaskChapterConfig.MainTaskChapterConfigR\x04list\x1aQ\n" +
	"\x15MainTaskChapterConfig\x12\x0e\n" +
	"\x02Id\x18\x02 \x01(\x05R\x02Id\x12\x12\n" +
	"\x04Name\x18\x03 \x01(\tR\x04Name\x12\x14\n" +
	"\x05Award\x18\x06 \x01(\x05R\x05Award\"\xe6\x03\n" +
	"\x12listMainTaskConfig\x129\n" +
	"\x04list\x18\x01 \x03(\v2%.pb.listMainTaskConfig.MainTaskConfigR\x04list\x1a\x94\x03\n" +
	"\x0eMainTaskConfig\x12\x0e\n" +
	"\x02Id\x18\x02 \x01(\x05R\x02Id\x12\x12\n" +
	"\x04Name\x18\x03 \x01(\tR\x04Name\x12\x1a\n" +
	"\bNameArgs\x18\x04 \x03(\tR\bNameArgs\x12\x1c\n" +
	"\tChapterId\x18\a \x01(\x05R\tChapterId\x12\x14\n" +
	"\x05PreId\x18\t \x01(\x05R\x05PreId\x12$\n" +
	"\rDefaultAccept\x18\n" +
	" \x01(\bR\rDefaultAccept\x12.\n" +
	"\bCondType\x18\v \x01(\x0e2\x12.pb.EConditionTypeR\bCondType\x12\x1e\n" +
	"\n" +
	"CondTarget\x18\f \x01(\x05R\n" +
	"CondTarget\x12\x1e\n" +
	"\n" +
	"CondParams\x18\r \x03(\x05R\n" +
	"CondParams\x12 \n" +
	"\vCondVersion\x18\x0e \x01(\x05R\vCondVersion\x12\x14\n" +
	"\x05Award\x18\x0f \x01(\x05R\x05Award\x12\x16\n" +
	"\x06TagIds\x18\x12 \x03(\x05R\x06TagIds\x12(\n" +
	"\x0fDefaultComplete\x18\x13 \x01(\bR\x0fDefaultComplete\"{\n" +
	"\rlistMapConfig\x12/\n" +
	"\x04list\x18\x01 \x03(\v2\x1b.pb.listMapConfig.MapConfigR\x04list\x1a9\n" +
	"\tMapConfig\x12\x0e\n" +
	"\x02Id\x18\x02 \x01(\x05R\x02Id\x12\x1c\n" +
	"\tTileMapId\x18\x03 \x01(\x05R\tTileMapId\"\xb5\x01\n" +
	"\x12listMiniGameConfig\x129\n" +
	"\x04list\x18\x01 \x03(\v2%.pb.listMiniGameConfig.MiniGameConfigR\x04list\x1ad\n" +
	"\x0eMiniGameConfig\x12\x0e\n" +
	"\x02Id\x18\x02 \x01(\x05R\x02Id\x12\x1a\n" +
	"\bIdentity\x18\x03 \x01(\tR\bIdentity\x12\x12\n" +
	"\x04Name\x18\x04 \x01(\tR\x04Name\x12\x12\n" +
	"\x04Desc\x18\x05 \x01(\tR\x04Desc\"\xf2\x01\n" +
	"\x0flistMUnitConfig\x123\n" +
	"\x04list\x18\x01 \x03(\v2\x1f.pb.listMUnitConfig.MUnitConfigR\x04list\x1a\xa9\x01\n" +
	"\vMUnitConfig\x12\x0e\n" +
	"\x02Id\x18\x02 \x01(\x05R\x02Id\x12\x1e\n" +
	"\x04Type\x18\x04 \x01(\x0e2\n" +
	".pb.EMUnitR\x04Type\x12\x14\n" +
	"\x05Width\x18\x05 \x01(\x05R\x05Width\x12\x16\n" +
	"\x06Height\x18\x06 \x01(\x05R\x06Height\x12 \n" +
	"\vAssetBundle\x18\a \x01(\tR\vAssetBundle\x12\x1a\n" +
	"\bGridSize\x18\b \x01(\x02R\bGridSize\"\x8c\x02\n" +
	"\x0elistRankConfig\x121\n" +
	"\x04list\x18\x01 \x03(\v2\x1d.pb.listRankConfig.RankConfigR\x04list\x1a\xc6\x01\n" +
	"\n" +
	"RankConfig\x12\x0e\n" +
	"\x02Id\x18\x02 \x01(\x05R\x02Id\x12\x12\n" +
	"\x04Name\x18\x03 \x01(\tR\x04Name\x12$\n" +
	"\rMinValueLimit\x18\x04 \x01(\x05R\rMinValueLimit\x12$\n" +
	"\rMaxQueryLimit\x18\x05 \x01(\x05R\rMaxQueryLimit\x12$\n" +
	"\rShowRankLimit\x18\x06 \x01(\x05R\rShowRankLimit\x12\"\n" +
	"\fMaxRankLimit\x18\a \x01(\x05R\fMaxRankLimit\"\x8d\x01\n" +
	"\x12listRechargeConfig\x129\n" +
	"\x04list\x18\x01 \x03(\v2%.pb.listRechargeConfig.RechargeConfigR\x04list\x1a<\n" +
	"\x0eRechargeConfig\x12\x0e\n" +
	"\x02Id\x18\x02 \x01(\x05R\x02Id\x12\x1a\n" +
	"\bIdentity\x18\x03 \x01(\tR\bIdentity\"\xcc\x02\n" +
	"\x13listShopGoodsConfig\x12;\n" +
	"\x04list\x18\x01 \x03(\v2'.pb.listShopGoodsConfig.ShopGoodsConfigR\x04list\x1a\xf7\x01\n" +
	"\x0fShopGoodsConfig\x12\x0e\n" +
	"\x02Id\x18\x02 \x01(\x05R\x02Id\x12\x18\n" +
	"\aGroupId\x18\x03 \x01(\x05R\aGroupId\x12(\n" +
	"\bItemData\x18\x05 \x01(\v2\f.pb.ItemDataR\bItemData\x12(\n" +
	"\bCostItem\x18\x06 \x01(\v2\f.pb.ItemDataR\bCostItem\x12\x1a\n" +
	"\bDiscount\x18\a \x01(\x05R\bDiscount\x12\x14\n" +
	"\x05Level\x18\b \x03(\x05R\x05Level\x12 \n" +
	"\vConditionId\x18\t \x01(\x05R\vConditionId\x12\x12\n" +
	"\x04Prop\x18\n" +
	" \x01(\x05R\x04Prop\"\xa6\x02\n" +
	"\x11listShopTabConfig\x127\n" +
	"\x04list\x18\x01 \x03(\v2#.pb.listShopTabConfig.ShopTabConfigR\x04list\x1a\xd7\x01\n" +
	"\rShopTabConfig\x12\x0e\n" +
	"\x02Id\x18\x02 \x01(\x05R\x02Id\x12!\n" +
	"\x04Type\x18\x06 \x01(\x0e2\r.pb.EShopTypeR\x04Type\x12\x18\n" +
	"\aGroupId\x18\a \x01(\x05R\aGroupId\x12\x1e\n" +
	"\n" +
	"GoodsCount\x18\b \x01(\x05R\n" +
	"GoodsCount\x12)\n" +
	"\bSystemId\x18\t \x01(\x0e2\r.pb.ESystemIdR\bSystemId\x12.\n" +
	"\vRefreshCost\x18\n" +
	" \x03(\v2\f.pb.ItemDataR\vRefreshCost\"\xf5\x01\n" +
	"\x1clistStoryPlotConditionConfig\x12M\n" +
	"\x04list\x18\x01 \x03(\v29.pb.listStoryPlotConditionConfig.StoryPlotConditionConfigR\x04list\x1a\x85\x01\n" +
	"\x18StoryPlotConditionConfig\x12\x0e\n" +
	"\x02Id\x18\x02 \x01(\x05R\x02Id\x12\x14\n" +
	"\x05Group\x18\x04 \x01(\x05R\x05Group\x12/\n" +
	"\x04Type\x18\x05 \x01(\x0e2\x1b.pb.EStoryPlotConditionTypeR\x04Type\x12\x12\n" +
	"\x04Args\x18\x06 \x03(\tR\x04Args\"\xf0\x01\n" +
	"\x13listStoryPlotConfig\x12;\n" +
	"\x04list\x18\x01 \x03(\v2'.pb.listStoryPlotConfig.StoryPlotConfigR\x04list\x1a\x9b\x01\n" +
	"\x0fStoryPlotConfig\x12\x0e\n" +
	"\x02Id\x18\x02 \x01(\x05R\x02Id\x12\x14\n" +
	"\x05Group\x18\x04 \x01(\x05R\x05Group\x12\x14\n" +
	"\x05PreId\x18\x05 \x01(\x05R\x05PreId\x12&\n" +
	"\x0eConditionGroup\x18\x06 \x01(\x05R\x0eConditionGroup\x12$\n" +
	"\rInstructGroup\x18\a \x01(\x05R\rInstructGroup\"\xb1\x01\n" +
	"\x18listStoryPlotGroupConfig\x12E\n" +
	"\x04list\x18\x01 \x03(\v21.pb.listStoryPlotGroupConfig.StoryPlotGroupConfigR\x04list\x1aN\n" +
	"\x14StoryPlotGroupConfig\x12\x0e\n" +
	"\x02Id\x18\x02 \x01(\x05R\x02Id\x12&\n" +
	"\x04Type\x18\x04 \x01(\x0e2\x12.pb.EStoryPlotTypeR\x04Type\"\xef\x02\n" +
	"\x1clistStoryPlotInstructsConfig\x12M\n" +
	"\x04list\x18\x01 \x03(\v29.pb.listStoryPlotInstructsConfig.StoryPlotInstructsConfigR\x04list\x1a\xff\x01\n" +
	"\x18StoryPlotInstructsConfig\x12\x0e\n" +
	"\x02Id\x18\x02 \x01(\x05R\x02Id\x12\x14\n" +
	"\x05Group\x18\x04 \x01(\x05R\x05Group\x12.\n" +
	"\x04Type\x18\x05 \x01(\x0e2\x1a.pb.EStoryPlotInstructTypeR\x04Type\x12\x12\n" +
	"\x04Args\x18\x06 \x03(\tR\x04Args\x12\x12\n" +
	"\x04Wait\x18\a \x01(\x05R\x04Wait\x12?\n" +
	"\bConditon\x18\b \x01(\x0e2#.pb.EStoryPlotInstructConditionTypeR\bConditon\x12$\n" +
	"\rconditionArgs\x18\t \x03(\tR\rconditionArgs\"\xee\x01\n" +
	"\x14listSystemOpenConfig\x12=\n" +
	"\x04list\x18\x01 \x03(\v2).pb.listSystemOpenConfig.SystemOpenConfigR\x04list\x1a\x96\x01\n" +
	"\x10SystemOpenConfig\x12\x0e\n" +
	"\x02Id\x18\x02 \x01(\x05R\x02Id\x12 \n" +
	"\vConditionId\x18\b \x01(\x05R\vConditionId\x12\x1a\n" +
	"\bParentId\x18\t \x01(\x05R\bParentId\x12\x18\n" +
	"\aVersion\x18\n" +
	" \x01(\x05R\aVersion\x12\x1a\n" +
	"\bDisabled\x18\f \x01(\bR\bDisabled\"]\n" +
	"\rlistTagConfig\x12/\n" +
	"\x04list\x18\x01 \x03(\v2\x1b.pb.listTagConfig.TagConfigR\x04list\x1a\x1b\n" +
	"\tTagConfig\x12\x0e\n" +
	"\x02Id\x18\x02 \x01(\x05R\x02Id\"\x94\x02\n" +
	"\x11listTileMapConfig\x127\n" +
	"\x04list\x18\x01 \x03(\v2#.pb.listTileMapConfig.TileMapConfigR\x04list\x1a\xc5\x01\n" +
	"\rTileMapConfig\x12\x0e\n" +
	"\x02Id\x18\x02 \x01(\x05R\x02Id\x12\x18\n" +
	"\aMapType\x18\x04 \x01(\x05R\aMapType\x12\x14\n" +
	"\x05Width\x18\x05 \x01(\x05R\x05Width\x12\x16\n" +
	"\x06Height\x18\x06 \x01(\x05R\x06Height\x12\x1a\n" +
	"\bGridSize\x18\a \x01(\x02R\bGridSize\x12 \n" +
	"\vAssetBundle\x18\b \x01(\tR\vAssetBundle\x12\x1e\n" +
	"\n" +
	"PrefabName\x18\t \x01(\tR\n" +
	"PrefabName\"\x99\x01\n" +
	"\x12listUserNameConfig\x129\n" +
	"\x04list\x18\x01 \x03(\v2%.pb.listUserNameConfig.UserNameConfigR\x04list\x1aH\n" +
	"\x0eUserNameConfig\x12\x0e\n" +
	"\x02Id\x18\x02 \x01(\x05R\x02Id\x12\x12\n" +
	"\x04name\x18\x03 \x01(\tR\x04name\x12\x12\n" +
	"\x04type\x18\x04 \x01(\x05R\x04type*\x83\x01\n" +
	"\aEGender\x12\x13\n" +
	"\x0fEGender_Invalid\x10\x00\x12\x10\n" +
	"\fEGender_Male\x10\x01\x12\x12\n" +
	"\x0eEGender_Female\x10\x02\x12\x11\n" +
	"\rEGender_Other\x10\x04\x12\x19\n" +
	"\x15EGender_MaleAndFemale\x10\x03\x12\x0f\n" +
	"\vEGender_All\x10\a*\x9a\x03\n" +
	"\x05EAttr\x12\x11\n" +
	"\rEAttr_Invalid\x10\x00\x12\x1e\n" +
	"\x1aEAttr_KitchenRecoverHungry\x10\x01\x12\"\n" +
	"\x1eEAttr_KitchenRecoverHungryRate\x10\x02\x12\x1d\n" +
	"\x19EAttr_KitchenRecoverTired\x10\x03\x12!\n" +
	"\x1dEAttr_KitchenRecoverTiredRate\x10\x04\x12\x1c\n" +
	"\x18EAttr_KitchenRecoverMood\x10\x05\x12 \n" +
	"\x1cEAttr_KitchenRecoverMoodRate\x10\x06\x12\x1b\n" +
	"\x17EAttr_HomeBuildingTired\x10\a\x12\x17\n" +
	"\x13EAttr_HeroPowerRate\x10\b\x12\x15\n" +
	"\x11EAttr_HeroEatRate\x10\t\x12\x1d\n" +
	"\x19EAttr_HeroProdaceTimeRate\x10\n" +
	"\x12\x17\n" +
	"\x13EAttr_HeroSleepRate\x10\v\x12\x19\n" +
	"\x15EAttr_ExploreCostRate\x10\f\x12\x18\n" +
	"\x14EAttr_HeroExpAddRate\x10\r*6\n" +
	"\tEHelpUIId\x12\x15\n" +
	"\x11EHelpUIId_invalid\x10\x00\x12\x12\n" +
	"\x0eEHelpUIId_Test\x10\x02*\xc7\x05\n" +
	"\tESystemId\x12\x15\n" +
	"\x11ESystemId_Invalid\x10\x00\x12\x12\n" +
	"\x0eESystemId_Base\x10\x01\x12\x12\n" +
	"\x0eESystemId_Role\x10\x02\x12\x19\n" +
	"\x14ESystemId_RoleReName\x10\xd1\x0f\x12\x13\n" +
	"\x0fESystemId_Login\x10\x03\x12\x13\n" +
	"\x0fESystemId_Lobby\x10\x04\x12\x10\n" +
	"\fESystemId_GM\x10\x05\x12\x11\n" +
	"\rESystemId_Bag\x10\x06\x12\x17\n" +
	"\x13ESystemId_Condition\x10\a\x12\x12\n" +
	"\x0eESystemId_Task\x10\b\x12\x12\n" +
	"\x0eESystemId_Hero\x10\n" +
	"\x12\x18\n" +
	"\x13ESystemId_HeroLevel\x10\xe9\a\x12\x17\n" +
	"\x12ESystemId_HeroStar\x10\xea\a\x12\x17\n" +
	"\x13ESystemId_DataCache\x10\v\x12\x18\n" +
	"\x14ESystemId_SystemOpen\x10\f\x12\x15\n" +
	"\x11ESystemId_TimeSys\x10\r\x12\x10\n" +
	"\fESystemId_AI\x10\x0e\x12\x12\n" +
	"\x0eESystemId_Attr\x10\x0f\x12\x19\n" +
	"\x15ESystemId_HudRightBtn\x10\x10\x12\x12\n" +
	"\x0eESystemId_Mail\x10\x11\x12\x16\n" +
	"\x12ESystemId_MainCity\x10\x12\x12\x11\n" +
	"\rESystemId_SDK\x10\x13\x12\x18\n" +
	"\x14ESystemId_WeeklyRank\x10\x16\x12\x16\n" +
	"\x12ESystemId_BaseRank\x10\x17\x12\x16\n" +
	"\x12ESystemId_Activity\x10\x18\x12\x17\n" +
	"\x13ESystemId_StoryPlot\x10\x19\x12\x12\n" +
	"\x0eESystemId_Shop\x10\x1a\x12\x11\n" +
	"\rESystemId_Tag\x10\x1b\x12\x19\n" +
	"\x15ESystemId_MainHudTips\x10\x1c\x12\x13\n" +
	"\x0fESystemId_Guide\x10\x1d\x12\x19\n" +
	"\x15ESystemId_CondTrigger\x10\x1e*\x95\x01\n" +
	"\x12EConfirmRepeatType\x12\x1e\n" +
	"\x1aEConfirmRepeatType_Forever\x10\x00\x12 \n" +
	"\x1cEConfirmRepeatType_OnceToday\x10\x01\x12 \n" +
	"\x1cEConfirmRepeatType_LoginTime\x10\x02\x12\x1b\n" +
	"\x17EConfirmRepeatType_Once\x10\x03*r\n" +
	"\x05EItem\x12\x0f\n" +
	"\vEItem_Money\x10\x00\x12\r\n" +
	"\tEItem_Box\x10\x01\x12\x12\n" +
	"\x0eEItem_Building\x10\x02\x12\x12\n" +
	"\x0eEItem_HeadIcon\x10\x03\x12\x0f\n" +
	"\vEItem_Title\x10\x04\x12\x10\n" +
	"\fEItem_Coupon\x10\t*\xa5\x03\n" +
	"\x10EResourceAddType\x12\x19\n" +
	"\x15EResourceAddType_None\x10\x00\x12#\n" +
	"\x1fEResourceAddType_FinishMainTask\x10\x01\x12*\n" +
	"&EResourceAddType_FinishMainTaskChapter\x10\x02\x12\x1f\n" +
	"\x1bEResourceAddType_SystemOpen\x10\x03\x12\x1b\n" +
	"\x17EResourceAddType_System\x10\x04\x12!\n" +
	"\x1dEResourceAddType_MapEventShop\x10\x05\x12\x1f\n" +
	"\x1bEResourceAddType_SystemMail\x10\x06\x12\"\n" +
	"\x1eEResourceAddType_NewPlayerMail\x10\a\x12$\n" +
	" EResourceAddType_WeeklyRankAward\x10\b\x12\x1e\n" +
	"\x1aEResourceAddType_ConstShop\x10\t\x12\x17\n" +
	"\x13EResourceAddType_GM\x10\f\x12 \n" +
	"\x1cEResourceAddType_TreasureBox\x10\r*\xb1\x01\n" +
	"\x10EResourceSubType\x12\x19\n" +
	"\x15EResourceSubType_None\x10\x00\x12 \n" +
	"\x1cEResourceSubType_HeroUpgrade\x10\x01\x12\x1d\n" +
	"\x19EResourceSubType_HeroStar\x10\x02\x12!\n" +
	"\x1dEResourceSubType_MapEventShop\x10\x03\x12\x1e\n" +
	"\x1aEResourceSubType_ConstShop\x10\x04*\xec\x04\n" +
	"\x0eEConditionType\x12\x17\n" +
	"\x13EConditionType_None\x10\x00\x12\x18\n" +
	"\x14EConditionType_Level\x10\x01\x12\x1d\n" +
	"\x19EConditionType_SystemOpen\x10\x02\x12\x1b\n" +
	"\x17EConditionType_GainItem\x10\x03\x12\x1d\n" +
	"\x19EConditionType_FinishTask\x10\x04\x12\x1b\n" +
	"\x17EConditionType_CostItem\x10\x05\x12\x1e\n" +
	"\x1aEConditionType_GainNewItem\x10\x06\x12#\n" +
	"\x1fEConditionType_HeroUpgradeCount\x10\a\x12$\n" +
	" EConditionType_MainCityShopEvent\x10\b\x12\x1e\n" +
	"\x1aEConditionType_HourOfDay12\x10\t\x12\x1e\n" +
	"\x1aEConditionType_HourOfDay24\x10\n" +
	"\x12!\n" +
	"\x1dEConditionType_WeeklyRankOpen\x10\v\x12\x1e\n" +
	"\x1aEConditionType_TagIdsTotal\x10\f\x12\x19\n" +
	"\x15EConditionType_TagIds\x10\r\x12 \n" +
	"\x1cEConditionType_CreateRoleDay\x10\x0e\x12$\n" +
	" EConditionType_TriggerGuideGroup\x10\x0f\x12!\n" +
	"\x1dEConditionType_DoneGuideGroup\x10\x10\x12\x1d\n" +
	"\x19EConditionType_TotalPower\x10\x11\x12\x1c\n" +
	"\x18EConditionType_HeroPower\x10\x12*Z\n" +
	"\vETaskStatus\x12\x14\n" +
	"\x10ETaskStatus_None\x10\x00\x12\x1b\n" +
	"\x17ETaskStatus_NotComplete\x10\x01\x12\x18\n" +
	"\x14ETaskStatus_Complete\x10\x02*\xa7\x01\n" +
	"\bEQuality\x12\x11\n" +
	"\rEQuality_None\x10\x00\x12\x12\n" +
	"\x0eEQuality_White\x10\x01\x12\x12\n" +
	"\x0eEQuality_Green\x10\x02\x12\x11\n" +
	"\rEQuality_Blue\x10\x03\x12\x13\n" +
	"\x0fEQuality_Purple\x10\x04\x12\x13\n" +
	"\x0fEQuality_Orange\x10\x05\x12\x10\n" +
	"\fEQuality_Red\x10\x06\x12\x11\n" +
	"\rEQuality_Pink\x10\a*\x91\x01\n" +
	"\tEHeroGain\x12\x12\n" +
	"\x0eEHeroGain_None\x10\x00\x12\x15\n" +
	"\x11EHeroGain_SysGive\x10\x01\x12\x16\n" +
	"\x12EHeroGain_HeroCard\x10\x02\x12\x18\n" +
	"\x14EHeroGain_ChipsMerge\x10\x03\x12\x10\n" +
	"\fEHeroGain_GM\x10\x04\x12\x15\n" +
	"\x11EHeroGain_Recruit\x10\x05*\xa2\n" +
	"\n" +
	"\tEHeroAttr\x12\x15\n" +
	"\x11EHeroAttr_Invalid\x10\x00\x12\x13\n" +
	"\x0fEHeroAttr_Power\x10\x01\x12\x19\n" +
	"\x15EHeroAttr_HumanTimeMS\x10\x02\x12\x17\n" +
	"\x13EHeroAttr_HumanTime\x10\x03\x12\x1f\n" +
	"\x1bEHeroAttr_TotalPurification\x10\x04\x12\x1e\n" +
	"\x1aEHeroAttr_BasePurification\x10\x05\x12\"\n" +
	"\x1eEHeroAttr_PurificationAddition\x10\x06\x12\x1d\n" +
	"\x19EHeroAttr_PurificationFix\x10\a\x12\x12\n" +
	"\x0eEHeroAttr_Mind\x10\b\x12\x13\n" +
	"\x0fEHeroAttr_Charm\x10\t\x12\x12\n" +
	"\x0eEHeroAttr_Body\x10\n" +
	"\x12\x13\n" +
	"\x0fEHeroAttr_Vigor\x10\v\x12\x17\n" +
	"\x13EHeroAttr_Knowledge\x10\f\x12\x1a\n" +
	"\x16EHeroAttr_CriticalRate\x10\r\x12\x1c\n" +
	"\x18EHeroAttr_CriticalDamage\x10\x0e\x12\x1e\n" +
	"\x1aEHeroAttr_ChastityAddition\x10\x0f\x12 \n" +
	"\x1cEHeroAttr_TemperanceAddition\x10\x10\x12\x1d\n" +
	"\x19EHeroAttr_CharityAddition\x10\x11\x12\x1a\n" +
	"\x16EHeroAttr_HopeAddition\x10\x12\x12\x1f\n" +
	"\x1bEHeroAttr_FortitudeAddition\x10\x13\x12\x1e\n" +
	"\x1aEHeroAttr_KindnessAddition\x10\x14\x12\x1d\n" +
	"\x19EHeroAttr_JusticeAddition\x10\x15\x12\x19\n" +
	"\x15EHeroAttr_SkillFactor\x10\x16\x12\x1c\n" +
	"\x18EHeroAttr_DamageAddition\x10\x17\x12\x1d\n" +
	"\x19EHeroAttr_DamageReduction\x10\x18\x12\x1e\n" +
	"\x1aEHeroAttr_RestrainAddition\x10\x19\x12\x1d\n" +
	"\x19EHeroAttr_ChastityInjured\x10\x1a\x12\x1f\n" +
	"\x1bEHeroAttr_TemperanceInjured\x10\x1b\x12\x1c\n" +
	"\x18EHeroAttr_CharityInjured\x10\x1c\x12\x19\n" +
	"\x15EHeroAttr_HopeInjured\x10\x1d\x12\x1e\n" +
	"\x1aEHeroAttr_FortitudeInjured\x10\x1e\x12\x1d\n" +
	"\x19EHeroAttr_KindnessInjured\x10\x1f\x12\x1c\n" +
	"\x18EHeroAttr_JusticeInjured\x10 \x12\x1f\n" +
	"\x1bEHeroAttr_ChastityReduction\x10!\x12!\n" +
	"\x1dEHeroAttr_TemperanceReduction\x10\"\x12\x1e\n" +
	"\x1aEHeroAttr_CharityReduction\x10#\x12\x1b\n" +
	"\x17EHeroAttr_HopeReduction\x10$\x12 \n" +
	"\x1cEHeroAttr_FortitudeReduction\x10%\x12\x1f\n" +
	"\x1bEHeroAttr_KindnessReduction\x10&\x12\x1e\n" +
	"\x1aEHeroAttr_JusticeReduction\x10'\x12\x16\n" +
	"\x12EHeroAttr_StunRate\x10(\x12\x1a\n" +
	"\x16EHeroAttr_MultiHitRate\x10)\x12\x1e\n" +
	"\x1aEHeroAttr_SkillPowerFactor\x10*\x12)\n" +
	"%EHeroAttr_GrownAttrPurificationFactor\x10+*\xa7\x01\n" +
	"\x0fEGainItemEffect\x12\x18\n" +
	"\x14EGainItemEffect_None\x10\x00\x12\x19\n" +
	"\x15EGainItemEffect_Popup\x10\x01\x12\x1b\n" +
	"\x17EGainItemEffect_IconFly\x10\x02\x12 \n" +
	"\x1cEGainItemEffect_IconFlyMulti\x10\x03\x12 \n" +
	"\x1cEGainItemEffect_IconFlyTouch\x10\x04*\xe0\x01\n" +
	"\tERankType\x12\x12\n" +
	"\x0eERankType_None\x10\x00\x12\x1d\n" +
	"\x19ERankType_CivilProsperity\x10\x01\x12\x19\n" +
	"\x15ERankType_LineUpPower\x10\x02\x12\x18\n" +
	"\x14ERankType_TotalPower\x10\x03\x12\x19\n" +
	"\x14ERankType_BossCopper\x10\xfdM\x12\x19\n" +
	"\x14ERankType_BossSilver\x10\xfeM\x12\x17\n" +
	"\x12ERankType_BossGold\x10\xffM\x12\x1c\n" +
	"\x17ERankType_MiniGameBegin\x10\xe8\a*\xb6\x01\n" +
	"\x05EGoto\x12\x11\n" +
	"\rEGoto_Invalid\x10\x00\x12\f\n" +
	"\bEGoto_UI\x10\x01\x12\x0f\n" +
	"\vEGoto_Scene\x10\x02\x12\x11\n" +
	"\rEGoto_MainBtn\x10\x03\x12\r\n" +
	"\tEGoto_Web\x10\x04\x12\x11\n" +
	"\rEGoto_TextDes\x10\x05\x12\x18\n" +
	"\x14EGoto_MainPlayerView\x10\x06\x12\x15\n" +
	"\x11EGoto_GuideEffect\x10\a\x12\x15\n" +
	"\x11EGoto_DelayOpenUI\x10\b*g\n" +
	"\vEMailStatus\x12\x14\n" +
	"\x10EMailStatus_None\x10\x00\x12\x16\n" +
	"\x12EMailStatus_Unread\x10\x01\x12\x14\n" +
	"\x10EMailStatus_Read\x10\x02\x12\x14\n" +
	"\x10EMailStatus_Gain\x10\x03*j\n" +
	"\vEAINodeType\x12\x14\n" +
	"\x10EAINodeType_None\x10\x00\x12\x17\n" +
	"\x13EAINodeType_Execute\x10\x01\x12\x14\n" +
	"\x10EAINodeType_Loop\x10\x02\x12\x16\n" +
	"\x12EAINodeType_Select\x10\x03*\x9a\x02\n" +
	"\x10EAIConditionType\x12\x19\n" +
	"\x15EAIConditionType_None\x10\x00\x12\x18\n" +
	"\x14EAIConditionType_And\x10\x01\x12\x17\n" +
	"\x13EAIConditionType_Or\x10\x02\x12#\n" +
	"\x1fEAIConditionType_CheckCurAITime\x10\x03\x12\"\n" +
	"\x1eEAIConditionType_CheckVariable\x10\x04\x12$\n" +
	" EAIConditionType_CheckLastAITime\x10\x05\x12$\n" +
	" EAIConditionType_CheckNextAITime\x10\x06\x12#\n" +
	"\x1fEAIConditionType_CheckBuildType\x10\a*\x98\x01\n" +
	"\rEAIActionType\x12\x16\n" +
	"\x12EAIActionType_None\x10\x00\x12\x17\n" +
	"\x13EAIActionType_Delay\x10\x01\x12\x1d\n" +
	"\x19EAIActionType_SetVariable\x10\x02\x12\x1d\n" +
	"\x19EAIActionType_ExecuteNode\x10\x03\x12\x18\n" +
	"\x14EAIActionType_Finish\x10\x04*e\n" +
	"\n" +
	"EAwardType\x12\x13\n" +
	"\x0fEAwardType_None\x10\x00\x12\x14\n" +
	"\x10EAwardType_Fixed\x10\x01\x12\x15\n" +
	"\x11EAwardType_Weight\x10\x02\x12\x15\n" +
	"\x11EAwardType_Random\x10\x03*c\n" +
	"\x0fEAwardGroupType\x12\x18\n" +
	"\x14EAwardGroupType_None\x10\x00\x12\x1a\n" +
	"\x16EAwardGroupType_Random\x10\x01\x12\x1a\n" +
	"\x16EAwardGroupType_Weight\x10\x02*b\n" +
	"\x17EStoryPlotConditionType\x12 \n" +
	"\x1cEStoryPlotConditionType_None\x10\x00\x12%\n" +
	"!EStoryPlotConditionType_EventDone\x10\x01*\x89\x12\n" +
	"\x16EStoryPlotInstructType\x12\x1f\n" +
	"\x1bEStoryPlotInstructType_None\x10\x00\x12\x1f\n" +
	"\x1bEStoryPlotInstructType_Wait\x10\x03\x12!\n" +
	"\x1dEStoryPlotInstructType_AddNpc\x10\x04\x12$\n" +
	" EStoryPlotInstructType_RemoveNpc\x10\x05\x12\"\n" +
	"\x1eEStoryPlotInstructType_NpcMove\x10\x06\x12$\n" +
	" EStoryPlotInstructType_NpcBubble\x10\a\x12$\n" +
	" EStoryPlotInstructType_NpcAction\x10\b\x12#\n" +
	"\x1fEStoryPlotInstructType_NpcEvent\x10\t\x12$\n" +
	" EStoryPlotInstructType_AddPlayer\x10\n" +
	"\x12%\n" +
	"!EStoryPlotInstructType_PlayerMove\x10\v\x12'\n" +
	"#EStoryPlotInstructType_PlayerAction\x10\f\x12'\n" +
	"#EStoryPlotInstructType_PlayerBubble\x10\r\x12&\n" +
	"\"EStoryPlotInstructType_FocusPlayer\x10\x0e\x12#\n" +
	"\x1fEStoryPlotInstructType_FocusNpc\x10\x0f\x12%\n" +
	"!EStoryPlotInstructType_FocusPoint\x10\x10\x12'\n" +
	"#EStoryPlotInstructType_ReleaseFocus\x10\x11\x12!\n" +
	"\x1dEStoryPlotInstructType_Finish\x10\x12\x12!\n" +
	"\x1dEStoryPlotInstructType_HideUI\x10\x13\x12!\n" +
	"\x1dEStoryPlotInstructType_ShowUI\x10\x14\x12&\n" +
	"\"EStoryPlotInstructType_EventBubble\x10\x15\x12$\n" +
	" EStoryPlotInstructType_NpcDialog\x10\x16\x12%\n" +
	"!EStoryPlotInstructType_HeroDialog\x10\x17\x12 \n" +
	"\x1cEStoryPlotInstructType_Black\x10\x18\x12'\n" +
	"#EStoryPlotInstructType_GuidePointer\x10\x19\x12$\n" +
	" EStoryPlotInstructType_ChangeBGM\x10\x1a\x12'\n" +
	"#EStoryPlotInstructType_NpcAddEffect\x10\x1b\x12$\n" +
	" EStoryPlotInstructType_PlaySound\x10\x1c\x12+\n" +
	"'EStoryPlotInstructType_AddNpcActionFlag\x10\x1d\x12.\n" +
	"*EStoryPlotInstructType_RemoveNpcActionFlag\x10\x1e\x12.\n" +
	"*EStoryPlotInstructType_AddPlayerActionFlag\x10\x1f\x121\n" +
	"-EStoryPlotInstructType_RemovePlayerActionFlag\x10 \x12*\n" +
	"&EStoryPlotInstructType_AddScenePicture\x10!\x121\n" +
	"-EStoryPlotInstructType_RemoveSceneStaticActor\x10\"\x12)\n" +
	"%EStoryPlotInstructType_AddSceneEffect\x10#\x12,\n" +
	"(EStoryPlotInstructType_ChangeSceneEffect\x10$\x12*\n" +
	"&EStoryPlotInstructType_RemoveNpcEffect\x10%\x12+\n" +
	"'EStoryPlotInstructType_AddCelebrateView\x10&\x12$\n" +
	" EStoryPlotInstructType_PlayVideo\x10'\x12\"\n" +
	"\x1eEStoryPlotInstructType_AddHero\x10(\x12%\n" +
	"!EStoryPlotInstructType_RemoveHero\x10)\x12#\n" +
	"\x1fEStoryPlotInstructType_HeroMove\x10*\x12%\n" +
	"!EStoryPlotInstructType_HeroBubble\x10+\x12%\n" +
	"!EStoryPlotInstructType_HeroAction\x10,\x12$\n" +
	" EStoryPlotInstructType_FocusHero\x10-\x12(\n" +
	"$EStoryPlotInstructType_AddHeroEffect\x10.\x12+\n" +
	"'EStoryPlotInstructType_RemoveHeroEffect\x10/\x12,\n" +
	"(EStoryPlotInstructType_AddHeroActionFlag\x100\x12/\n" +
	"+EStoryPlotInstructType_RemoveHeroActionFlag\x101\x12,\n" +
	"(EStoryPlotInstructType_LoadUIViewContent\x102\x12-\n" +
	")EStoryPlotInstructType_UIViewSetHeroSpine\x103\x12*\n" +
	"&EStoryPlotInstructType_UIViewAddBubble\x104\x12*\n" +
	"&EStoryPlotInstructType_WaitPlayerInput\x105\x12&\n" +
	"\"EStoryPlotInstructType_StoryDialog\x106\x12/\n" +
	"+EStoryPlotInstructType_UIViewPlayTransition\x107\x12-\n" +
	")EStoryPlotInstructType_OpenUIAndWaitClose\x108\x12'\n" +
	"#EStoryPlotInstructType_ReportShushu\x109*w\n" +
	"\rEShopBuyLimit\x12\x16\n" +
	"\x12EShopBuyLimit_None\x10\x00\x12\x17\n" +
	"\x13EShopBuyLimit_Daily\x10\x01\x12\x18\n" +
	"\x14EShopBuyLimit_Weekly\x10\x02\x12\x1b\n" +
	"\x17EShopBuyLimit_Permanent\x10\x03*<\n" +
	"\tEShopType\x12\x16\n" +
	"\x12EShopType_PropShop\x10\x00\x12\x17\n" +
	"\x13EShopType_ConstShop\x10\x01*\xa7\x01\n" +
	"\x0eEStoryPlotType\x12\x17\n" +
	"\x13EStoryPlotType_None\x10\x00\x12\x1c\n" +
	"\x18EStoryPlotType_MainScene\x10\x01\x12\x1f\n" +
	"\x1bEStoryPlotType_ExploreScene\x10\x02\x12\x1d\n" +
	"\x19EStoryPlotType_StoryScene\x10\x03\x12\x1e\n" +
	"\x1aEStoryPlotType_StoryUIView\x10\x04*\xed\x01\n" +
	"\x1fEStoryPlotInstructConditionType\x12(\n" +
	"$EStoryPlotInstructConditionType_None\x10\x00\x12:\n" +
	"6EStoryPlotInstructConditionType_SlimeTaskChapterFinish\x10\x01\x125\n" +
	"1EStoryPlotInstructConditionType_HomeBuildingLevel\x10\x02\x12-\n" +
	")EStoryPlotInstructConditionType_HasHeroId\x10\x03*\xb2\x01\n" +
	"\x06EMUnit\x12\x0f\n" +
	"\vEMUnit_Root\x10\x00\x12\x0e\n" +
	"\n" +
	"EMUnit_Map\x10\x01\x12\x13\n" +
	"\x0fEMUnit_Building\x10\n" +
	"\x12\x13\n" +
	"\x0fEMUnit_WorkSpot\x10\v\x12\x10\n" +
	"\fEMUnit_Event\x10\f\x12\x12\n" +
	"\x0eEMUnit_Special\x10\r\x12\x10\n" +
	"\fEMUnit_Cloud\x10\x0e\x12\x10\n" +
	"\fEMUnit_Staff\x10\x0f\x12\x13\n" +
	"\x0fEMUnit_Furnture\x10\x10*\xa2\x01\n" +
	"\n" +
	"ETileBlock\x12\x16\n" +
	"\x12ETileBlock_Invalid\x10\x00\x12\x13\n" +
	"\x0fETileBlock_Data\x10\x01\x12\x1b\n" +
	"\x17ETileBlock_BuildingArea\x10\x02\x12\x14\n" +
	"\x10ETileBlock_Cloud\x10\x03\x12\x19\n" +
	"\x15ETileBlock_EntityTile\x10\x04\x12\x19\n" +
	"\x15ETileBlock_LotteryDup\x10\x05*\x97\x01\n" +
	"\x0eEMapLogicLayer\x12\x1a\n" +
	"\x16EMapLogicLayer_Invalid\x10\x00\x12\x17\n" +
	"\x13EMapLogicLayer_Data\x10\x01\x12\x1b\n" +
	"\x17EMapLogicLayer_Building\x10\x02\x12\x19\n" +
	"\x15EMapLogicLayer_Clouds\x10\x03\x12\x18\n" +
	"\x14EMapLogicLayer_MUnit\x10\x04*M\n" +
	"\bEMapType\x12\x14\n" +
	"\x10EMapType_Invalid\x10\x00\x12\x12\n" +
	"\x0eEMapType_Scene\x10\x01\x12\x17\n" +
	"\x13EMapType_LotteryDup\x10\x02*\x95\x02\n" +
	"\tEMapLayer\x12\x12\n" +
	"\x0eEMapLayer_None\x10\x00\x12 \n" +
	"\x1cEMapLayer_BuildingFloorLayer\x10\x01\x12\x1c\n" +
	"\x18EMapLayer_StaffDownLayer\x10\x02\x12\x17\n" +
	"\x13EMapLayer_HeroLayer\x10\x03\x12\x1a\n" +
	"\x16EMapLayer_StaffUpLayer\x10\x04\x12\x1f\n" +
	"\x1bEMapLayer_BuildingRoofLayer\x10\x05\x12\x1e\n" +
	"\x1aEMapLayer_BuildingBarLayer\x10\x06\x12!\n" +
	"\x1dEMapLayer_BuildingRoofLayerUp\x10\a\x12\x1b\n" +
	"\x17EMapLayer_CloudTopLayer\x10\b*g\n" +
	"\x06EGuide\x12\x0f\n" +
	"\vEGuide_None\x10\x00\x12\x17\n" +
	"\x13EGuide_GuidePointer\x10\x06\x12\x18\n" +
	"\x14EGuide_GuideBuilding\x10\x1a\x12\x19\n" +
	"\x15EGuide_GuideCityEvent\x10\x1b*}\n" +
	"\x0fEAttrFormatType\x12\x18\n" +
	"\x14EAttrFormatType_None\x10\x00\x12\x19\n" +
	"\x15EAttrFormatType_Value\x10\x01\x12\x1b\n" +
	"\x17EAttrFormatType_Percent\x10\x02\x12\x18\n" +
	"\x14EAttrFormatType_Time\x10\x03*T\n" +
	"\tERedPoint\x12\x15\n" +
	"\x11ERedPoint_invalid\x10\x00\x12\x19\n" +
	"\x15ERedPoint_AlawaysTrue\x10\x01\x12\x15\n" +
	"\x11ERedPoint_MailSys\x10\x02*\x89\x01\n" +
	"\x12ETileBlockWorkType\x12\x1b\n" +
	"\x17ETileBlockWorkType_Seat\x10\x00\x12\x1c\n" +
	"\x18ETileBlockWorkType_Queue\x10\x01\x12\x1b\n" +
	"\x17ETileBlockWorkType_Gate\x10\x02\x12\x1b\n" +
	"\x17ETileBlockWorkType_Rent\x10\x1f*N\n" +
	"\x10ECondTriggerType\x12\x19\n" +
	"\x15ECondTriggerType_None\x10\x00\x12\x1f\n" +
	"\x1bECondTriggerType_ModifyTime\x10\x01*`\n" +
	"\x0fEActorDirection\x12\x18\n" +
	"\x14EActorDirection_None\x10\x00\x12\x18\n" +
	"\x14EActorDirection_Left\x10\x01\x12\x19\n" +
	"\x15EActorDirection_Right\x10\x02B&Z$kairo_paradise_server/services/pb;pb"

var (
	file_gameconfig_proto_rawDescOnce sync.Once
	file_gameconfig_proto_rawDescData []byte
)

func file_gameconfig_proto_rawDescGZIP() []byte {
	file_gameconfig_proto_rawDescOnce.Do(func() {
		file_gameconfig_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_gameconfig_proto_rawDesc), len(file_gameconfig_proto_rawDesc)))
	})
	return file_gameconfig_proto_rawDescData
}

var file_gameconfig_proto_enumTypes = make([]protoimpl.EnumInfo, 39)
var file_gameconfig_proto_msgTypes = make([]protoimpl.MessageInfo, 91)
var file_gameconfig_proto_goTypes = []any{
	(EGender)(0),                                    // 0: pb.EGender
	(EAttr)(0),                                      // 1: pb.EAttr
	(EHelpUIId)(0),                                  // 2: pb.EHelpUIId
	(ESystemId)(0),                                  // 3: pb.ESystemId
	(EConfirmRepeatType)(0),                         // 4: pb.EConfirmRepeatType
	(EItem)(0),                                      // 5: pb.EItem
	(EResourceAddType)(0),                           // 6: pb.EResourceAddType
	(EResourceSubType)(0),                           // 7: pb.EResourceSubType
	(EConditionType)(0),                             // 8: pb.EConditionType
	(ETaskStatus)(0),                                // 9: pb.ETaskStatus
	(EQuality)(0),                                   // 10: pb.EQuality
	(EHeroGain)(0),                                  // 11: pb.EHeroGain
	(EHeroAttr)(0),                                  // 12: pb.EHeroAttr
	(EGainItemEffect)(0),                            // 13: pb.EGainItemEffect
	(ERankType)(0),                                  // 14: pb.ERankType
	(EGoto)(0),                                      // 15: pb.EGoto
	(EMailStatus)(0),                                // 16: pb.EMailStatus
	(EAINodeType)(0),                                // 17: pb.EAINodeType
	(EAIConditionType)(0),                           // 18: pb.EAIConditionType
	(EAIActionType)(0),                              // 19: pb.EAIActionType
	(EAwardType)(0),                                 // 20: pb.EAwardType
	(EAwardGroupType)(0),                            // 21: pb.EAwardGroupType
	(EStoryPlotConditionType)(0),                    // 22: pb.EStoryPlotConditionType
	(EStoryPlotInstructType)(0),                     // 23: pb.EStoryPlotInstructType
	(EShopBuyLimit)(0),                              // 24: pb.EShopBuyLimit
	(EShopType)(0),                                  // 25: pb.EShopType
	(EStoryPlotType)(0),                             // 26: pb.EStoryPlotType
	(EStoryPlotInstructConditionType)(0),            // 27: pb.EStoryPlotInstructConditionType
	(EMUnit)(0),                                     // 28: pb.EMUnit
	(ETileBlock)(0),                                 // 29: pb.ETileBlock
	(EMapLogicLayer)(0),                             // 30: pb.EMapLogicLayer
	(EMapType)(0),                                   // 31: pb.EMapType
	(EMapLayer)(0),                                  // 32: pb.EMapLayer
	(EGuide)(0),                                     // 33: pb.EGuide
	(EAttrFormatType)(0),                            // 34: pb.EAttrFormatType
	(ERedPoint)(0),                                  // 35: pb.ERedPoint
	(ETileBlockWorkType)(0),                         // 36: pb.ETileBlockWorkType
	(ECondTriggerType)(0),                           // 37: pb.ECondTriggerType
	(EActorDirection)(0),                            // 38: pb.EActorDirection
	(*Vector2)(nil),                                 // 39: pb.Vector2
	(*Vector3)(nil),                                 // 40: pb.Vector3
	(*AttrData)(nil),                                // 41: pb.AttrData
	(*ItemData)(nil),                                // 42: pb.ItemData
	(*HeroAttr)(nil),                                // 43: pb.HeroAttr
	(*RandomItemData)(nil),                          // 44: pb.RandomItemData
	(*AwardGroupData)(nil),                          // 45: pb.AwardGroupData
	(*ConditionWithDesc)(nil),                       // 46: pb.ConditionWithDesc
	(*ItemProperty)(nil),                            // 47: pb.ItemProperty
	(*GridPosition)(nil),                            // 48: pb.GridPosition
	(*GuideDetailTips)(nil),                         // 49: pb.GuideDetailTips
	(*CountPropData)(nil),                           // 50: pb.CountPropData
	(*XVInt32)(nil),                                 // 51: pb._v_int32
	(*ListAIActionConfig)(nil),                      // 52: pb.listAIActionConfig
	(*ListAIConditionConfig)(nil),                   // 53: pb.listAIConditionConfig
	(*ListAINodeConfig)(nil),                        // 54: pb.listAINodeConfig
	(*ListAvatarConfig)(nil),                        // 55: pb.listAvatarConfig
	(*ListAwardConfig)(nil),                         // 56: pb.listAwardConfig
	(*ListAwardGroupConfig)(nil),                    // 57: pb.listAwardGroupConfig
	(*ListConditionConfig)(nil),                     // 58: pb.listConditionConfig
	(*ListConditionTriggerConfig)(nil),              // 59: pb.listConditionTriggerConfig
	(*ListConstConfig)(nil),                         // 60: pb.listConstConfig
	(*ListConstShopGoodsConfig)(nil),                // 61: pb.listConstShopGoodsConfig
	(*ListGainItemConfig)(nil),                      // 62: pb.listGainItemConfig
	(*ListGuideConfig)(nil),                         // 63: pb.listGuideConfig
	(*ListGuideGroupConfig)(nil),                    // 64: pb.listGuideGroupConfig
	(*ListHeadPortraitConfig)(nil),                  // 65: pb.listHeadPortraitConfig
	(*ListHeroConfig)(nil),                          // 66: pb.listHeroConfig
	(*ListHeroLevelConfig)(nil),                     // 67: pb.listHeroLevelConfig
	(*ListHeroQualityConfig)(nil),                   // 68: pb.listHeroQualityConfig
	(*ListHeroStarConfig)(nil),                      // 69: pb.listHeroStarConfig
	(*ListItemConfig)(nil),                          // 70: pb.listItemConfig
	(*ListLevelConfig)(nil),                         // 71: pb.listLevelConfig
	(*ListMailConfig)(nil),                          // 72: pb.listMailConfig
	(*ListMainCityScaleConfig)(nil),                 // 73: pb.listMainCityScaleConfig
	(*ListMainTaskChapterConfig)(nil),               // 74: pb.listMainTaskChapterConfig
	(*ListMainTaskConfig)(nil),                      // 75: pb.listMainTaskConfig
	(*ListMapConfig)(nil),                           // 76: pb.listMapConfig
	(*ListMiniGameConfig)(nil),                      // 77: pb.listMiniGameConfig
	(*ListMUnitConfig)(nil),                         // 78: pb.listMUnitConfig
	(*ListRankConfig)(nil),                          // 79: pb.listRankConfig
	(*ListRechargeConfig)(nil),                      // 80: pb.listRechargeConfig
	(*ListShopGoodsConfig)(nil),                     // 81: pb.listShopGoodsConfig
	(*ListShopTabConfig)(nil),                       // 82: pb.listShopTabConfig
	(*ListStoryPlotConditionConfig)(nil),            // 83: pb.listStoryPlotConditionConfig
	(*ListStoryPlotConfig)(nil),                     // 84: pb.listStoryPlotConfig
	(*ListStoryPlotGroupConfig)(nil),                // 85: pb.listStoryPlotGroupConfig
	(*ListStoryPlotInstructsConfig)(nil),            // 86: pb.listStoryPlotInstructsConfig
	(*ListSystemOpenConfig)(nil),                    // 87: pb.listSystemOpenConfig
	(*ListTagConfig)(nil),                           // 88: pb.listTagConfig
	(*ListTileMapConfig)(nil),                       // 89: pb.listTileMapConfig
	(*ListUserNameConfig)(nil),                      // 90: pb.listUserNameConfig
	(*ListAIActionConfig_AIActionConfig)(nil),       // 91: pb.listAIActionConfig.AIActionConfig
	(*ListAIConditionConfig_AIConditionConfig)(nil), // 92: pb.listAIConditionConfig.AIConditionConfig
	(*ListAINodeConfig_AINodeConfig)(nil),           // 93: pb.listAINodeConfig.AINodeConfig
	(*ListAvatarConfig_AvatarConfig)(nil),           // 94: pb.listAvatarConfig.AvatarConfig
	(*ListAwardConfig_AwardConfig)(nil),             // 95: pb.listAwardConfig.AwardConfig
	(*ListAwardGroupConfig_AwardGroupConfig)(nil),   // 96: pb.listAwardGroupConfig.AwardGroupConfig
	(*ListConditionConfig_ConditionConfig)(nil),     // 97: pb.listConditionConfig.ConditionConfig
	(*ListConditionTriggerConfig_ConditionTriggerConfig)(nil),     // 98: pb.listConditionTriggerConfig.ConditionTriggerConfig
	(*ListConstConfig_ConstConfig)(nil),                           // 99: pb.listConstConfig.ConstConfig
	(*ListConstShopGoodsConfig_ConstShopGoodsConfig)(nil),         // 100: pb.listConstShopGoodsConfig.ConstShopGoodsConfig
	(*ListGainItemConfig_GainItemConfig)(nil),                     // 101: pb.listGainItemConfig.GainItemConfig
	(*ListGuideConfig_GuideConfig)(nil),                           // 102: pb.listGuideConfig.GuideConfig
	(*ListGuideGroupConfig_GuideGroupConfig)(nil),                 // 103: pb.listGuideGroupConfig.GuideGroupConfig
	(*ListHeadPortraitConfig_HeadPortraitConfig)(nil),             // 104: pb.listHeadPortraitConfig.HeadPortraitConfig
	(*ListHeroConfig_HeroConfig)(nil),                             // 105: pb.listHeroConfig.HeroConfig
	(*ListHeroLevelConfig_HeroLevelConfig)(nil),                   // 106: pb.listHeroLevelConfig.HeroLevelConfig
	(*ListHeroQualityConfig_HeroQualityConfig)(nil),               // 107: pb.listHeroQualityConfig.HeroQualityConfig
	(*ListHeroStarConfig_HeroStarConfig)(nil),                     // 108: pb.listHeroStarConfig.HeroStarConfig
	(*ListItemConfig_ItemConfig)(nil),                             // 109: pb.listItemConfig.ItemConfig
	(*ListLevelConfig_LevelConfig)(nil),                           // 110: pb.listLevelConfig.LevelConfig
	(*ListMailConfig_MailConfig)(nil),                             // 111: pb.listMailConfig.MailConfig
	(*ListMainCityScaleConfig_MainCityScaleConfig)(nil),           // 112: pb.listMainCityScaleConfig.MainCityScaleConfig
	(*ListMainTaskChapterConfig_MainTaskChapterConfig)(nil),       // 113: pb.listMainTaskChapterConfig.MainTaskChapterConfig
	(*ListMainTaskConfig_MainTaskConfig)(nil),                     // 114: pb.listMainTaskConfig.MainTaskConfig
	(*ListMapConfig_MapConfig)(nil),                               // 115: pb.listMapConfig.MapConfig
	(*ListMiniGameConfig_MiniGameConfig)(nil),                     // 116: pb.listMiniGameConfig.MiniGameConfig
	(*ListMUnitConfig_MUnitConfig)(nil),                           // 117: pb.listMUnitConfig.MUnitConfig
	(*ListRankConfig_RankConfig)(nil),                             // 118: pb.listRankConfig.RankConfig
	(*ListRechargeConfig_RechargeConfig)(nil),                     // 119: pb.listRechargeConfig.RechargeConfig
	(*ListShopGoodsConfig_ShopGoodsConfig)(nil),                   // 120: pb.listShopGoodsConfig.ShopGoodsConfig
	(*ListShopTabConfig_ShopTabConfig)(nil),                       // 121: pb.listShopTabConfig.ShopTabConfig
	(*ListStoryPlotConditionConfig_StoryPlotConditionConfig)(nil), // 122: pb.listStoryPlotConditionConfig.StoryPlotConditionConfig
	(*ListStoryPlotConfig_StoryPlotConfig)(nil),                   // 123: pb.listStoryPlotConfig.StoryPlotConfig
	(*ListStoryPlotGroupConfig_StoryPlotGroupConfig)(nil),         // 124: pb.listStoryPlotGroupConfig.StoryPlotGroupConfig
	(*ListStoryPlotInstructsConfig_StoryPlotInstructsConfig)(nil), // 125: pb.listStoryPlotInstructsConfig.StoryPlotInstructsConfig
	(*ListSystemOpenConfig_SystemOpenConfig)(nil),                 // 126: pb.listSystemOpenConfig.SystemOpenConfig
	(*ListTagConfig_TagConfig)(nil),                               // 127: pb.listTagConfig.TagConfig
	(*ListTileMapConfig_TileMapConfig)(nil),                       // 128: pb.listTileMapConfig.TileMapConfig
	(*ListUserNameConfig_UserNameConfig)(nil),                     // 129: pb.listUserNameConfig.UserNameConfig
}
var file_gameconfig_proto_depIdxs = []int32{
	1,   // 0: pb.AttrData.Attr:type_name -> pb.EAttr
	12,  // 1: pb.HeroAttr.Attr:type_name -> pb.EHeroAttr
	91,  // 2: pb.listAIActionConfig.list:type_name -> pb.listAIActionConfig.AIActionConfig
	92,  // 3: pb.listAIConditionConfig.list:type_name -> pb.listAIConditionConfig.AIConditionConfig
	93,  // 4: pb.listAINodeConfig.list:type_name -> pb.listAINodeConfig.AINodeConfig
	94,  // 5: pb.listAvatarConfig.list:type_name -> pb.listAvatarConfig.AvatarConfig
	95,  // 6: pb.listAwardConfig.list:type_name -> pb.listAwardConfig.AwardConfig
	96,  // 7: pb.listAwardGroupConfig.list:type_name -> pb.listAwardGroupConfig.AwardGroupConfig
	97,  // 8: pb.listConditionConfig.list:type_name -> pb.listConditionConfig.ConditionConfig
	98,  // 9: pb.listConditionTriggerConfig.list:type_name -> pb.listConditionTriggerConfig.ConditionTriggerConfig
	99,  // 10: pb.listConstConfig.list:type_name -> pb.listConstConfig.ConstConfig
	100, // 11: pb.listConstShopGoodsConfig.list:type_name -> pb.listConstShopGoodsConfig.ConstShopGoodsConfig
	101, // 12: pb.listGainItemConfig.list:type_name -> pb.listGainItemConfig.GainItemConfig
	102, // 13: pb.listGuideConfig.list:type_name -> pb.listGuideConfig.GuideConfig
	103, // 14: pb.listGuideGroupConfig.list:type_name -> pb.listGuideGroupConfig.GuideGroupConfig
	104, // 15: pb.listHeadPortraitConfig.list:type_name -> pb.listHeadPortraitConfig.HeadPortraitConfig
	105, // 16: pb.listHeroConfig.list:type_name -> pb.listHeroConfig.HeroConfig
	106, // 17: pb.listHeroLevelConfig.list:type_name -> pb.listHeroLevelConfig.HeroLevelConfig
	107, // 18: pb.listHeroQualityConfig.list:type_name -> pb.listHeroQualityConfig.HeroQualityConfig
	108, // 19: pb.listHeroStarConfig.list:type_name -> pb.listHeroStarConfig.HeroStarConfig
	109, // 20: pb.listItemConfig.list:type_name -> pb.listItemConfig.ItemConfig
	110, // 21: pb.listLevelConfig.list:type_name -> pb.listLevelConfig.LevelConfig
	111, // 22: pb.listMailConfig.list:type_name -> pb.listMailConfig.MailConfig
	112, // 23: pb.listMainCityScaleConfig.list:type_name -> pb.listMainCityScaleConfig.MainCityScaleConfig
	113, // 24: pb.listMainTaskChapterConfig.list:type_name -> pb.listMainTaskChapterConfig.MainTaskChapterConfig
	114, // 25: pb.listMainTaskConfig.list:type_name -> pb.listMainTaskConfig.MainTaskConfig
	115, // 26: pb.listMapConfig.list:type_name -> pb.listMapConfig.MapConfig
	116, // 27: pb.listMiniGameConfig.list:type_name -> pb.listMiniGameConfig.MiniGameConfig
	117, // 28: pb.listMUnitConfig.list:type_name -> pb.listMUnitConfig.MUnitConfig
	118, // 29: pb.listRankConfig.list:type_name -> pb.listRankConfig.RankConfig
	119, // 30: pb.listRechargeConfig.list:type_name -> pb.listRechargeConfig.RechargeConfig
	120, // 31: pb.listShopGoodsConfig.list:type_name -> pb.listShopGoodsConfig.ShopGoodsConfig
	121, // 32: pb.listShopTabConfig.list:type_name -> pb.listShopTabConfig.ShopTabConfig
	122, // 33: pb.listStoryPlotConditionConfig.list:type_name -> pb.listStoryPlotConditionConfig.StoryPlotConditionConfig
	123, // 34: pb.listStoryPlotConfig.list:type_name -> pb.listStoryPlotConfig.StoryPlotConfig
	124, // 35: pb.listStoryPlotGroupConfig.list:type_name -> pb.listStoryPlotGroupConfig.StoryPlotGroupConfig
	125, // 36: pb.listStoryPlotInstructsConfig.list:type_name -> pb.listStoryPlotInstructsConfig.StoryPlotInstructsConfig
	126, // 37: pb.listSystemOpenConfig.list:type_name -> pb.listSystemOpenConfig.SystemOpenConfig
	127, // 38: pb.listTagConfig.list:type_name -> pb.listTagConfig.TagConfig
	128, // 39: pb.listTileMapConfig.list:type_name -> pb.listTileMapConfig.TileMapConfig
	129, // 40: pb.listUserNameConfig.list:type_name -> pb.listUserNameConfig.UserNameConfig
	18,  // 41: pb.listAIConditionConfig.AIConditionConfig.Type:type_name -> pb.EAIConditionType
	20,  // 42: pb.listAwardConfig.AwardConfig.Type:type_name -> pb.EAwardType
	45,  // 43: pb.listAwardConfig.AwardConfig.Award:type_name -> pb.AwardGroupData
	21,  // 44: pb.listAwardGroupConfig.AwardGroupConfig.Type:type_name -> pb.EAwardGroupType
	8,   // 45: pb.listConditionConfig.ConditionConfig.Type:type_name -> pb.EConditionType
	8,   // 46: pb.listConditionTriggerConfig.ConditionTriggerConfig.CondType:type_name -> pb.EConditionType
	37,  // 47: pb.listConditionTriggerConfig.ConditionTriggerConfig.TiggerType:type_name -> pb.ECondTriggerType
	51,  // 48: pb.listConstConfig.ConstConfig.VIntLList:type_name -> pb._v_int32
	42,  // 49: pb.listConstShopGoodsConfig.ConstShopGoodsConfig.ItemData:type_name -> pb.ItemData
	42,  // 50: pb.listConstShopGoodsConfig.ConstShopGoodsConfig.CostItem:type_name -> pb.ItemData
	24,  // 51: pb.listConstShopGoodsConfig.ConstShopGoodsConfig.LimitType:type_name -> pb.EShopBuyLimit
	8,   // 52: pb.listConstShopGoodsConfig.ConstShopGoodsConfig.CondType:type_name -> pb.EConditionType
	13,  // 53: pb.listGainItemConfig.GainItemConfig.EffectType:type_name -> pb.EGainItemEffect
	33,  // 54: pb.listGuideConfig.GuideConfig.Type:type_name -> pb.EGuide
	8,   // 55: pb.listGuideGroupConfig.GuideGroupConfig.CondType:type_name -> pb.EConditionType
	8,   // 56: pb.listHeadPortraitConfig.HeadPortraitConfig.CondType:type_name -> pb.EConditionType
	10,  // 57: pb.listHeroConfig.HeroConfig.Quality:type_name -> pb.EQuality
	42,  // 58: pb.listHeroConfig.HeroConfig.NeedChips:type_name -> pb.ItemData
	43,  // 59: pb.listHeroConfig.HeroConfig.BaseAttr:type_name -> pb.HeroAttr
	43,  // 60: pb.listHeroLevelConfig.HeroLevelConfig.Attrs:type_name -> pb.HeroAttr
	42,  // 61: pb.listHeroLevelConfig.HeroLevelConfig.CostItem:type_name -> pb.ItemData
	42,  // 62: pb.listHeroStarConfig.HeroStarConfig.CostItem:type_name -> pb.ItemData
	43,  // 63: pb.listHeroStarConfig.HeroStarConfig.Attrs:type_name -> pb.HeroAttr
	5,   // 64: pb.listItemConfig.ItemConfig.Type:type_name -> pb.EItem
	42,  // 65: pb.listMailConfig.MailConfig.Attachments:type_name -> pb.ItemData
	8,   // 66: pb.listMainTaskConfig.MainTaskConfig.CondType:type_name -> pb.EConditionType
	28,  // 67: pb.listMUnitConfig.MUnitConfig.Type:type_name -> pb.EMUnit
	42,  // 68: pb.listShopGoodsConfig.ShopGoodsConfig.ItemData:type_name -> pb.ItemData
	42,  // 69: pb.listShopGoodsConfig.ShopGoodsConfig.CostItem:type_name -> pb.ItemData
	25,  // 70: pb.listShopTabConfig.ShopTabConfig.Type:type_name -> pb.EShopType
	3,   // 71: pb.listShopTabConfig.ShopTabConfig.SystemId:type_name -> pb.ESystemId
	42,  // 72: pb.listShopTabConfig.ShopTabConfig.RefreshCost:type_name -> pb.ItemData
	22,  // 73: pb.listStoryPlotConditionConfig.StoryPlotConditionConfig.Type:type_name -> pb.EStoryPlotConditionType
	26,  // 74: pb.listStoryPlotGroupConfig.StoryPlotGroupConfig.Type:type_name -> pb.EStoryPlotType
	23,  // 75: pb.listStoryPlotInstructsConfig.StoryPlotInstructsConfig.Type:type_name -> pb.EStoryPlotInstructType
	27,  // 76: pb.listStoryPlotInstructsConfig.StoryPlotInstructsConfig.Conditon:type_name -> pb.EStoryPlotInstructConditionType
	77,  // [77:77] is the sub-list for method output_type
	77,  // [77:77] is the sub-list for method input_type
	77,  // [77:77] is the sub-list for extension type_name
	77,  // [77:77] is the sub-list for extension extendee
	0,   // [0:77] is the sub-list for field type_name
}

func init() { file_gameconfig_proto_init() }
func file_gameconfig_proto_init() {
	if File_gameconfig_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_gameconfig_proto_rawDesc), len(file_gameconfig_proto_rawDesc)),
			NumEnums:      39,
			NumMessages:   91,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_gameconfig_proto_goTypes,
		DependencyIndexes: file_gameconfig_proto_depIdxs,
		EnumInfos:         file_gameconfig_proto_enumTypes,
		MessageInfos:      file_gameconfig_proto_msgTypes,
	}.Build()
	File_gameconfig_proto = out.File
	file_gameconfig_proto_goTypes = nil
	file_gameconfig_proto_depIdxs = nil
}
