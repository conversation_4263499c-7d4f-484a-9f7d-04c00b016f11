mysql:
  host: 127.0.0.1
  port: 3306
  user: root
  password: mysql123zxc
  database: kairo_paradise_game
  charset: utf8mb4
  max_idle_conns: 50  # 保留最大空闲连接数
  max_open_conns: 200 # 设置最大打开连接数
  conn_max_lifetime: 3600 # 设置连接的最大生命周期
  conn_max_idle_time: 1800  # 设置连接的最大空闲时间
  parse_time: true
  loc: Local

redis:
  host: 127.0.0.1
  port: 6379
  passwd: "redis123zxc"
  db: 0

jwt:
  secret_key: "#NMNKAIROJIQUSHE"
  access_token_expiry: 30 #单位秒
  refresh_token_expiry: 30 # 单位天
  issuer: kairo_paradise_server

logger:
  log_level: "error"  # 只记录错误日志
  output_paths: ["stdout", "./logs/app.log"]
  error_output_paths: ["stderr", "./logs/error.log"]
  encoding: "json"
  max_size: 100
  max_backups: 10
  max_age: 30
  compress: true

etcd:
  endpoints: ["localhost:2379"]
  dial_timeout: 5s
  username: ""
  password: ""
  service_ttl: 60s
  service_prefix: "/services/"
  auto_sync: true
  auto_sync_interval: 10m

debug: false
