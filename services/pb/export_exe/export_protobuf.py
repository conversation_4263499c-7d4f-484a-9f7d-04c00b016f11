#!/usr/bin/env python
# -*- coding: utf-8 -*-
# Date       :
# Author     :
# Description:


from __future__ import print_function
from __future__ import division
from __future__ import absolute_import
import sys
import io
import os
import glob
import re
import getopt
from string import digits

# reload(sys)
# sys.setdefaultencoding('utf8')

PB_FIELD_KEYWORD = {'uint32', 'int32', 'sint32', 'uint64', 'int64', 'sint64', 'float', 'double', 'bytes', 'string',
                    'bool', 'fixed32', 'fixed64', 'sfixed32', 'sfixed64'}

PROTO_CONST_NAME = "msg_const.go"
PROTO_FILENAME = u'*.proto'


def gen_protobuf(protoPath = "./", genPath = "./"):
    proto_set = merge_proto(protoPath)
    gen_proto_id_define(proto_set, genPath)

def merge_proto(protoPath):
    proto_path = os.path.join(protoPath, PROTO_FILENAME)
    print(u"analysis proto from:", proto_path)
    file_list = glob.glob(proto_path)
    # proto_list = []
    pattern = re.compile(r'\/\/\s+\d{3,5}(?:\s+\/\/\s+USED.*)?\s+message\s+[a-zA-Z0-9_]+')

    # msg_text = u""
    proto_set = {}
    for file_name in file_list:
        with open(file_name, 'r', encoding='utf-8') as file:
            content = file.read()
        print("export proto_id const: %s" % file_name)
        matches = re.findall(pattern, content)

        for match in matches:
            # print(u"\n\nmatch：%s" % match)
            line_list = match.split()
            proto_id = 0
            pkg_name = ""
            for line in line_list:
                if line.isdigit(): ## decode common for proto_id
                    proto_id = int(line)
                elif line.startswith("C2S") or line.startswith("S2C") or line.startswith("G2C") or line.startswith("C2G") or line.startswith("G2S") or line.startswith("S2G") or line.startswith("S2S"): ## record c2s message
                    pkg_name = line
                elif line.startswith("event_"):
                    pkg_name = line
            proto_set[pkg_name] = proto_id
    return proto_set

def gen_proto_id_define(proto_set, genPath):
    texts = [u"""
// --------------------------
// 工具生成
// --------------------------
"""]
    texts.append("package msg")
    import_str = u"import \"kairo_paradise_server/services/pb\""
    # # print("import:%s" % import_str)
    texts.append(import_str)

    texts.append("type PCK int32")

    texts.append("const (")
    textfunc = [u"\n", u"func onInit() {"]
    for proto_name, proto_id in proto_set.items():
        # 生成协议号宏
        name = format_proto_id_name(proto_name)
        # print(u"name:%s" % name)    
        msg_line = u"\t%s PCK = %s" % (name, proto_id)
        # msg_line = msg_line + "\n"
        texts.append(msg_line)
        # 生成注册函数
        struct_name = format_proto_name(proto_name)
        print(proto_id, proto_name, name, struct_name)
        # print(u"struct_name:%s" % struct_name)
        if struct_name.startswith("C2S"):
            func_line = u"\tC2SProcessor.Register(uint16(%s), &pb.%s{})" % (name, struct_name)
        elif struct_name.startswith("S2C"):
            func_line = u"\tS2CProcessor.Register(uint16(%s), &pb.%s{})" % (name, struct_name)
        elif struct_name.startswith("S2S"):
            func_line = u"\tS2SProcessor.Register(uint16(%s), &pb.%s{})" % (name, struct_name)
        elif struct_name.startswith("S2G"):
            func_line = u"\tS2CProcessor.Register(uint16(%s), &pb.%s{})" % (name, struct_name)
        elif struct_name.startswith("G2S"):
            func_line = u"\tC2SProcessor.Register(uint16(%s), &pb.%s{})" % (name, struct_name)
        elif struct_name.startswith("C2G"):
            func_line = u"\tC2SProcessor.Register(uint16(%s), &pb.%s{})" % (name, struct_name)
        elif struct_name.startswith("G2C"):
            func_line = u"\tS2CProcessor.Register(uint16(%s), &pb.%s{})" % (name, struct_name)
        elif struct_name.startswith("Event"):
            func_line = u"\tEventProcessor.Register(uint16(%s), &pb.%s{})" % (name, struct_name)
        textfunc.append(func_line)

    texts.append(')')
    # print (u"text:%s" % texts)
    textfunc.append('}')
    # print(u"textfunc:%s" % textfunc)
    texts.append("\n".join(textfunc))
    write_const(texts, genPath)

def write_const(msg_text, genPath):
    print(msg_text)
    p = os.path.join(genPath, PROTO_CONST_NAME)
    with io.open(p, 'wb') as fds:
        print(u"gen const file:", p)
        fds.write("\n".join(msg_text).encode('utf-8'))
       

def format_proto_id_name(name):
    if name in PB_FIELD_KEYWORD:
        return name

    return "PCK_" + name

def format_proto_name(name):
    if name in PB_FIELD_KEYWORD:
        return name

    return name

def main(argv = None):
    if argv is None:
        argv = sys.argv
    proto_path = argv[1]
    gen_path = argv[2]
    try:
        opts, args = getopt.gnu_getopt(
            argv[1:], "hascp:",
            ["help", "all", "server", "client", "path="])        
        gen_protobuf(proto_path, gen_path)
    except getopt.error as msg:
        print(msg)

def try_decode(s):
    try:
        return s.decode('gbk')
    except UnicodeDecodeError:
        try:
            return s.decode('utf-8')
        except UnicodeDecodeError:
            return s


if __name__ == "__main__":
    sys.exit(main(sys.argv))




