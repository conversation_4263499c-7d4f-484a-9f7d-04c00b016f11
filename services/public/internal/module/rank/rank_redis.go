package rank

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"kairo_paradise_server/internal/bootstrap"
	"kairo_paradise_server/internal/logger"
	"time"

	"github.com/redis/go-redis/v9"
	"go.uber.org/zap"
)

const (
	// Redis key prefixes
	realtimeRankKeyPrefix = "realtime_rank:"    // 实时排行榜ZSET前缀
	periodicRankKeyPrefix = "periodic_rank:"    // 定时更新排行榜ZSET前缀
	rankDataKeyPrefix     = "rank_data:"        // 排行榜数据HASH前缀
	lastUpdateTimePrefix  = "rank_last_update:" // 排行榜最后更新时间前缀
	rankEligibilityPrefix = "rank_eligibility:" // 排行榜资格缓存前缀
	rankEligibilityExpiry = 3600                // 排行榜资格缓存过期时间（秒）
)

// getRank<PERSON>ey returns the Redis key for a rank ZSET
func getRankKey(rankType int32, isRealtime bool) string {
	if isRealtime {
		return fmt.Sprintf("%s%d", realtimeRankKeyPrefix, rankType)
	}
	return fmt.Sprintf("%s%d", periodicRankKeyPrefix, rankType)
}

// getRankDataKey returns the Redis key for a rank entry's data
func getRankDataKey(rankType int32, playerID uint64) string {
	return fmt.Sprintf("%s%d:%d", rankDataKeyPrefix, rankType, playerID)
}

// getLastUpdateTimeKey returns the Redis key for the last update time of a rank
func getLastUpdateTimeKey(rankType int32) string {
	return fmt.Sprintf("%s%d", lastUpdateTimePrefix, rankType)
}

// getRankEligibilityKey returns the Redis key for rank eligibility cache
func getRankEligibilityKey(rankType int32, playerID uint64) string {
	return fmt.Sprintf("%s%d:%d", rankEligibilityPrefix, rankType, playerID)
}

// saveRankEntry saves a rank entry to Redis
func saveRankEntry(ctx context.Context, rankType int32, maxRankingLimit int32, entry *Entry, isRealtime bool) (int32, error) {
	// Save score to sorted set
	rankKey := getRankKey(rankType, isRealtime)
	err := bootstrap.RedisClient.ZAdd(ctx, rankKey, redis.Z{
		Score:  float64(entry.Prosperity),
		Member: entry.PlayerID,
	}).Err()
	if err != nil {
		return 0, fmt.Errorf("failed to add to rank sorted set: %w", err)
	}
	if err := bootstrap.RedisClient.ZRemRangeByRank(ctx, rankKey, 0, int64(maxRankingLimit)).Err(); err != nil {
		return 0, fmt.Errorf("failed to remove range by rank: %w", err)
	}
	vals, err := bootstrap.RedisClient.ZRangeWithScores(ctx, rankKey, 0, 0).Result()
	if err != nil {
		return 0, fmt.Errorf("failed to get rank range: %w", err)
	}
	if len(vals) > 0 {
		return int32(vals[0].Score), nil
	}
	return 0, nil
}

// getRankEntry gets a rank entry from Redis
func getRankEntry(ctx context.Context, rankType int32, playerID uint64) (*Entry, error) {
	dataKey := getRankDataKey(rankType, playerID)
	entryJSON, err := bootstrap.RedisClient.Get(ctx, dataKey).Result()
	if err != nil {
		if errors.Is(err, redis.Nil) {
			return nil, nil // Entry not found
		}
		return nil, fmt.Errorf("failed to get rank entry data: %w", err)
	}

	var entry Entry
	err = json.Unmarshal([]byte(entryJSON), &entry)
	if err != nil {
		return nil, fmt.Errorf("failed to unmarshal rank entry: %w", err)
	}

	return &entry, nil
}

func setRankEntry(ctx context.Context, rankType int32, entry *Entry) error {
	// Save entry data
	dataKey := getRankDataKey(rankType, entry.PlayerID)
	entryJSON, err := json.Marshal(entry)
	if err != nil {
		return fmt.Errorf("failed to marshal rank entry: %w", err)
	}
	err = bootstrap.RedisClient.Set(ctx, dataKey, entryJSON, 0).Err()
	if err != nil {
		return fmt.Errorf("failed to save rank entry data: %w", err)
	}
	return nil
}

// getPlayerRank gets a player's rank from Redis
func getPlayerRank(ctx context.Context, rankType int32, playerID uint64, isRealtime bool) (int32, error) {
	rankKey := getRankKey(rankType, isRealtime)
	rank, err := bootstrap.RedisClient.ZRevRank(ctx, rankKey, fmt.Sprintf("%d", playerID)).Result()
	if err != nil {
		if errors.Is(err, redis.Nil) {
			return -1, nil // Player not ranked
		}
		return -1, fmt.Errorf("failed to get player rank: %w", err)
	}
	return int32(rank) + 1, nil
}

// getRankRange gets a range of entries from the leaderboard
func getRankRange(ctx context.Context, rankType int32, start, count int32, isRealtime bool) ([]*Entry, error) {
	rankKey := getRankKey(rankType, isRealtime)

	// Redis ZRevRange is 0-based, so subtract 1 from start
	redisStart := int64(start - 1)
	if redisStart < 0 {
		redisStart = 0
	}
	redisStop := redisStart + int64(count) - 1

	// Get the range of player IDs with scores
	results, err := bootstrap.RedisClient.ZRevRangeWithScores(ctx, rankKey, redisStart, redisStop).Result()
	if err != nil {
		return nil, fmt.Errorf("failed to get rank range: %w", err)
	}

	entries := make([]*Entry, 0, len(results))
	for _, z := range results {
		playerID, ok := z.Member.(string)
		if !ok {
			logger.Warn("Invalid player ID in rank", zap.Any("member", z.Member))
			continue
		}

		// Get the full entry data
		entry, err := getRankEntry(ctx, rankType, parsePlayerID(playerID))
		if err != nil {
			logger.Warn("Failed to get rank entry", zap.Error(err), zap.String("playerID", playerID))
			continue
		}

		if entry != nil {
			entries = append(entries, entry)
		}
	}

	return entries, nil
}

// getRankCount gets the total number of entries in a leaderboard
func getRankCount(ctx context.Context, rankType int32, isRealtime bool) (int64, error) {
	rankKey := getRankKey(rankType, isRealtime)
	return bootstrap.RedisClient.ZCard(ctx, rankKey).Result()
}

// updateLastUpdateTime updates the last update time of a leaderboard
func updateLastUpdateTime(ctx context.Context, rankType int32) error {
	key := getLastUpdateTimeKey(rankType)
	now := time.Now().Unix()
	return bootstrap.RedisClient.Set(ctx, key, now, 0).Err()
}

// getLastUpdateTime gets the last update time of a leaderboard
func getLastUpdateTime(ctx context.Context, rankType int32) (int64, error) {
	key := getLastUpdateTimeKey(rankType)
	result, err := bootstrap.RedisClient.Get(ctx, key).Result()
	if err != nil {
		if errors.Is(err, redis.Nil) {
			return 0, nil // Never updated
		}
		return 0, err
	}

	var timestamp int64
	_, err = fmt.Sscanf(result, "%d", &timestamp)
	if err != nil {
		return 0, err
	}

	return timestamp, nil
}

// clearRank clears all entries in a leaderboard
func clearRank(ctx context.Context, rankType int32, isRealtime bool) error {
	rankKey := getRankKey(rankType, isRealtime)

	// Get all player IDs in the rank
	playerIDs, err := bootstrap.RedisClient.ZRange(ctx, rankKey, 0, -1).Result()
	if err != nil {
		return fmt.Errorf("failed to get player IDs: %w", err)
	}

	// Delete all entry data
	for _, playerID := range playerIDs {
		dataKey := getRankDataKey(rankType, parsePlayerID(playerID))
		err := bootstrap.RedisClient.Del(ctx, dataKey).Err()
		if err != nil {
			logger.Warn("Failed to delete rank entry data", zap.Error(err), zap.String("playerID", playerID))
		}
	}

	// Delete the rank sorted set
	err = bootstrap.RedisClient.Del(ctx, rankKey).Err()
	if err != nil {
		return fmt.Errorf("failed to delete rank sorted set: %w", err)
	}

	// Delete the last update time
	lastUpdateKey := getLastUpdateTimeKey(rankType)
	err = bootstrap.RedisClient.Del(ctx, lastUpdateKey).Err()
	if err != nil {
		logger.Warn("Failed to delete last update time", zap.Error(err))
	}

	return nil
}

// setRankEligibility sets whether a player is eligible for a leaderboard
func setRankEligibility(ctx context.Context, rankType int32, playerID uint64, eligible bool) error {
	key := getRankEligibilityKey(rankType, playerID)
	value := 0
	if eligible {
		value = 1
	}
	return bootstrap.RedisClient.Set(ctx, key, value, time.Duration(rankEligibilityExpiry)*time.Second).Err()
}

// getRankEligibility gets whether a player is eligible for a leaderboard
func getRankEligibility(ctx context.Context, rankType int32, playerID uint64) (bool, error) {
	key := getRankEligibilityKey(rankType, playerID)
	result, err := bootstrap.RedisClient.Get(ctx, key).Result()
	if err != nil {
		if errors.Is(err, redis.Nil) {
			return false, nil // Not cached, assume not eligible
		}
		return false, err
	}

	return result == "1", nil
}

// Helper function to parse player ID from string
func parsePlayerID(playerID string) uint64 {
	var id uint64
	_, err := fmt.Sscanf(playerID, "%d", &id)
	if err != nil {
		logger.Warn("Failed to parse player ID", zap.Error(err), zap.String("playerID", playerID))
		return 0
	}
	return id
}
