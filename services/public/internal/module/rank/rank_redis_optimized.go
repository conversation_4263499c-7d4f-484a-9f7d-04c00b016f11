package rank

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"kairo_paradise_server/internal/bootstrap"
	"kairo_paradise_server/internal/logger"
	"strconv"
	"time"

	"github.com/redis/go-redis/v9"
	"go.uber.org/zap"
)

// BatchGetPlayerRanks 批量获取玩家排名（优化版）
func BatchGetPlayerRanks(ctx context.Context, rankType int32, playerIDs []uint64, isRealtime bool) ([]*RankInfo, error) {
	if len(playerIDs) == 0 {
		return []*RankInfo{}, nil
	}

	rankKey := getRankKey(rankType, isRealtime)

	// 使用pipeline批量获取排名
	pipe := bootstrap.RedisClient.Pipeline()

	// 批量获取排名
	rankCmds := make([]*redis.IntCmd, len(playerIDs))
	for i, playerID := range playerIDs {
		rankCmds[i] = pipe.ZRevRank(ctx, rankKey, fmt.Sprintf("%d", playerID))
	}

	// 批量获取数据
	dataCmds := make([]*redis.StringCmd, len(playerIDs))
	for i, playerID := range playerIDs {
		dataKey := getRankDataKey(rankType, playerID)
		dataCmds[i] = pipe.Get(ctx, dataKey)
	}

	// 执行pipeline
	_, err := pipe.Exec(ctx)
	if err != nil && !errors.Is(err, redis.Nil) {
		return nil, fmt.Errorf("failed to execute pipeline: %w", err)
	}

	// 处理结果
	results := make([]*RankInfo, len(playerIDs))
	for i, playerID := range playerIDs {
		rank, err := rankCmds[i].Result()
		if err != nil {
			if errors.Is(err, redis.Nil) {
				results[i] = &RankInfo{Rank: -1}
				continue
			}
			logger.Warn("Failed to get player rank", zap.Error(err), zap.Uint64("playerID", playerID))
			results[i] = &RankInfo{Rank: -1}
			continue
		}

		// Redis ranks are 0-based, so add 1 to get the actual rank
		actualRank := int32(rank + 1)

		// 获取玩家数据
		entryJSON, err := dataCmds[i].Result()
		if err != nil {
			if errors.Is(err, redis.Nil) {
				results[i] = &RankInfo{Rank: -1}
				continue
			}
			logger.Warn("Failed to get player data", zap.Error(err), zap.Uint64("playerID", playerID))
			results[i] = &RankInfo{Rank: actualRank, Entry: Entry{PlayerID: playerID}}
			continue
		}

		var entry Entry
		err = json.Unmarshal([]byte(entryJSON), &entry)
		if err != nil {
			logger.Warn("Failed to unmarshal player data", zap.Error(err), zap.Uint64("playerID", playerID))
			results[i] = &RankInfo{Rank: actualRank, Entry: Entry{PlayerID: playerID}}
			continue
		}

		results[i] = &RankInfo{
			Rank:  actualRank,
			Entry: entry,
		}
	}

	return results, nil
}

// BatchSaveRankEntries 批量保存排行榜条目
func BatchSaveRankEntries(ctx context.Context, rankType int32, entries []*Entry, isRealtime bool) error {
	if len(entries) == 0 {
		return nil
	}

	rankKey := getRankKey(rankType, isRealtime)
	pipe := bootstrap.RedisClient.Pipeline()

	// 批量添加到sorted set
	zMembers := make([]redis.Z, len(entries))
	for i, entry := range entries {
		zMembers[i] = redis.Z{
			Score:  float64(entry.Prosperity),
			Member: entry.PlayerID,
		}
	}
	pipe.ZAdd(ctx, rankKey, zMembers...)

	// 批量保存条目数据
	for _, entry := range entries {
		dataKey := getRankDataKey(rankType, entry.PlayerID)
		entryJSON, err := json.Marshal(entry)
		if err != nil {
			logger.Warn("Failed to marshal rank entry", zap.Error(err), zap.Uint64("playerID", entry.PlayerID))
			continue
		}
		pipe.Set(ctx, dataKey, entryJSON, 0)
	}

	// 执行pipeline
	_, err := pipe.Exec(ctx)
	if err != nil {
		return fmt.Errorf("failed to execute batch save pipeline: %w", err)
	}

	logger.Debug("Batch saved rank entries",
		zap.Int32("rankType", rankType),
		zap.Int("count", len(entries)))

	return nil
}

// GetRankRangeOptimized 获取排行榜范围（优化版）
func GetRankRangeOptimized(ctx context.Context, rankType int32, start, count int32, isRealtime bool) ([]*Entry, int64, error) {
	rankKey := getRankKey(rankType, isRealtime)

	// 使用pipeline同时获取排行榜数据和总数
	pipe := bootstrap.RedisClient.Pipeline()

	// Redis ZRevRange is 0-based, so subtract 1 from start
	redisStart := int64(start - 1)
	if redisStart < 0 {
		redisStart = 0
	}
	redisStop := redisStart + int64(count) - 1

	// 获取排行榜范围
	rangeCmd := pipe.ZRevRangeWithScores(ctx, rankKey, redisStart, redisStop)
	// 获取总数
	countCmd := pipe.ZCard(ctx, rankKey)

	// 执行pipeline
	_, err := pipe.Exec(ctx)
	if err != nil {
		return nil, 0, fmt.Errorf("failed to execute range pipeline: %w", err)
	}

	// 获取结果
	results, err := rangeCmd.Result()
	if err != nil {
		return nil, 0, fmt.Errorf("failed to get rank range: %w", err)
	}

	totalCount, err := countCmd.Result()
	if err != nil {
		return nil, 0, fmt.Errorf("failed to get rank count: %w", err)
	}

	if len(results) == 0 {
		return []*Entry{}, totalCount, nil
	}

	// 批量获取条目数据
	pipe2 := bootstrap.RedisClient.Pipeline()
	dataCmds := make([]*redis.StringCmd, len(results))

	for i, z := range results {
		playerIDStr, ok := z.Member.(string)
		if !ok {
			logger.Warn("Invalid player ID in rank", zap.Any("member", z.Member))
			continue
		}

		playerID, err := strconv.ParseUint(playerIDStr, 10, 64)
		if err != nil {
			logger.Warn("Failed to parse player ID", zap.Error(err), zap.String("playerID", playerIDStr))
			continue
		}

		dataKey := getRankDataKey(rankType, playerID)
		dataCmds[i] = pipe2.Get(ctx, dataKey)
	}

	// 执行第二个pipeline
	_, err = pipe2.Exec(ctx)
	if err != nil && !errors.Is(err, redis.Nil) {
		return nil, 0, fmt.Errorf("failed to execute data pipeline: %w", err)
	}

	// 处理结果
	entries := make([]*Entry, 0, len(results))
	for i, z := range results {
		if i >= len(dataCmds) {
			break
		}

		playerIDStr, ok := z.Member.(string)
		if !ok {
			continue
		}

		playerID, err := strconv.ParseUint(playerIDStr, 10, 64)
		if err != nil {
			continue
		}

		entryJSON, err := dataCmds[i].Result()
		if err != nil {
			if errors.Is(err, redis.Nil) {
				// 如果没有详细数据，创建基本条目
				entries = append(entries, &Entry{
					PlayerID:   playerID,
					Prosperity: int32(z.Score),
					UpdateTime: time.Now().Unix(),
				})
				continue
			}
			logger.Warn("Failed to get rank entry data", zap.Error(err), zap.Uint64("playerID", playerID))
			continue
		}

		var entry Entry
		err = json.Unmarshal([]byte(entryJSON), &entry)
		if err != nil {
			logger.Warn("Failed to unmarshal rank entry", zap.Error(err), zap.Uint64("playerID", playerID))
			// 创建基本条目
			entries = append(entries, &Entry{
				PlayerID:   playerID,
				Prosperity: int32(z.Score),
				UpdateTime: time.Now().Unix(),
			})
			continue
		}

		entries = append(entries, &entry)
	}

	return entries, totalCount, nil
}

// BatchCheckRankEligibility 批量检查排行榜资格
func BatchCheckRankEligibility(ctx context.Context, rankType int32, playerIDs []uint64) (map[uint64]bool, error) {
	if len(playerIDs) == 0 {
		return make(map[uint64]bool), nil
	}

	pipe := bootstrap.RedisClient.Pipeline()
	cmds := make([]*redis.StringCmd, len(playerIDs))

	for i, playerID := range playerIDs {
		key := getRankEligibilityKey(rankType, playerID)
		cmds[i] = pipe.Get(ctx, key)
	}

	// 执行pipeline
	_, err := pipe.Exec(ctx)
	if err != nil && err != redis.Nil {
		return nil, fmt.Errorf("failed to execute eligibility pipeline: %w", err)
	}

	// 处理结果
	results := make(map[uint64]bool)
	for i, playerID := range playerIDs {
		result, err := cmds[i].Result()
		if err != nil {
			if err == redis.Nil {
				results[playerID] = false // 默认不合格
				continue
			}
			logger.Warn("Failed to get rank eligibility", zap.Error(err), zap.Uint64("playerID", playerID))
			results[playerID] = false
			continue
		}

		results[playerID] = result == "1"
	}

	return results, nil
}

// BatchSetRankEligibility 批量设置排行榜资格
func BatchSetRankEligibility(ctx context.Context, rankType int32, eligibilities map[uint64]bool) error {
	if len(eligibilities) == 0 {
		return nil
	}

	pipe := bootstrap.RedisClient.Pipeline()

	for playerID, eligible := range eligibilities {
		key := getRankEligibilityKey(rankType, playerID)
		value := "0"
		if eligible {
			value = "1"
		}
		pipe.Set(ctx, key, value, time.Duration(rankEligibilityExpiry)*time.Second)
	}

	// 执行pipeline
	_, err := pipe.Exec(ctx)
	if err != nil {
		return fmt.Errorf("failed to execute eligibility set pipeline: %w", err)
	}

	logger.Debug("Batch set rank eligibility",
		zap.Int32("rankType", rankType),
		zap.Int("count", len(eligibilities)))

	return nil
}
