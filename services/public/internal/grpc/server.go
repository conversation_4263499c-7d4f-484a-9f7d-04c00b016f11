package grpc

import (
	"context"
	"errors"
	"fmt"
	"go.uber.org/zap"
	"google.golang.org/grpc/metadata"
	"kairo_paradise_server/internal/grpc"
	"kairo_paradise_server/internal/handler"
	"kairo_paradise_server/internal/logger"
	"kairo_paradise_server/services/pb"
	"kairo_paradise_server/services/pb/msg"
)

// Server implements the GRpcServiceServer interface for the public service
type Server struct {
	pb.UnimplementedGRpcServiceServer
}

// G2SRpc handles requests from the gate service to the public service
func (s *Server) G2SRpc(ctx context.Context, in *pb.G2SRequest) (*pb.S2GRequest, error) {
	msgID := *in.ProtocolId
	// Extract UID from metadata
	md, ok := metadata.FromIncomingContext(ctx)
	if !ok {
		return nil, errors.New("failed to get metadata")
	}
	uid, err := grpc.GetUid(md)
	if err != nil {
		return nil, err
	}
	ctx = context.WithValue(ctx, "uid", *uid)

	// Find the appropriate handler for the message ID and call it
	response, err := handler.Handle(ctx, msg.PCK(msgID), in.Payload)
	if err != nil {
		logger.Error("Failed to handle message", zap.Uint32("message_id", msgID), zap.Error(err))
		// Return an error response
		return nil, errors.New(fmt.Sprintf("Failed to handle message %d: %v", msgID, err))
	}
	resp, err := msg.S2CProcessor.Marshal(response)
	if err != nil {
		logger.Error("Failed to marshal response", zap.Uint32("message_id", msgID), zap.Error(err))
		// Return an error response
		return nil, errors.New(fmt.Sprintf("Error handling message %d: %v", msgID, err))
	}

	return &pb.S2GRequest{
		Payload: resp.Data,
	}, nil
}
