package main

import (
	"context"
	"kairo_paradise_server/services/public"
	"math/rand"
	"testing"
	"time"
)

// BenchmarkGetRankList 测试获取排行榜列表的性能
func BenchmarkGetRankList(b *testing.B) {
	// 初始化测试环境
	manager := public.Rank
	ctx := context.Background()
	rankType := int32(2) // 繁荣榜

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		_, _, err := manager.GetRankList(ctx, rankType, 1, 50)
		if err != nil {
			b.<PERSON>("GetRankList failed: %v", err)
		}
	}
}

// BenchmarkGetPlayerRank 测试获取玩家排名的性能
func BenchmarkGetPlayerRank(b *testing.B) {
	manager := GetRankManager()
	ctx := context.Background()
	rankType := int32(2)
	playerID := uint64(12345)

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		_, err := manager.GetPlayerRank(ctx, rankType, playerID)
		if err != nil {
			b.<PERSON>("GetPlayerRank failed: %v", err)
		}
	}
}

// BenchmarkGetMultiPlayerRank 测试批量获取玩家排名的性能
func BenchmarkGetMultiPlayerRank(b *testing.B) {
	manager := GetRankManager()
	ctx := context.Background()
	rankType := int32(2)

	// 生成测试用的玩家ID列表
	playerIDs := make([]uint64, 100)
	for i := range playerIDs {
		playerIDs[i] = uint64(10000 + i)
	}

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		_, err := manager.GetMultiPlayerRank(ctx, rankType, playerIDs)
		if err != nil {
			b.Errorf("GetMultiPlayerRank failed: %v", err)
		}
	}
}

// TestCachePerformance 测试缓存性能
func TestCachePerformance(t *testing.T) {
	manager := GetRankManager()
	ctx := context.Background()
	rankType := int32(2)

	// 第一次调用（缓存未命中）
	start := time.Now()
	_, _, err := manager.GetRankList(ctx, rankType, 1, 50)
	if err != nil {
		t.Errorf("GetRankList failed: %v", err)
		return
	}
	firstCallDuration := time.Since(start)

	// 第二次调用（缓存命中）
	start = time.Now()
	_, _, err = manager.GetRankList(ctx, rankType, 1, 50)
	if err != nil {
		t.Errorf("GetRankList failed: %v", err)
		return
	}
	secondCallDuration := time.Since(start)

	// 缓存命中应该比缓存未命中快很多
	if secondCallDuration >= firstCallDuration {
		t.Logf("Warning: Cache hit (%v) not faster than cache miss (%v)",
			secondCallDuration, firstCallDuration)
	} else {
		speedup := float64(firstCallDuration) / float64(secondCallDuration)
		t.Logf("Cache speedup: %.2fx (miss: %v, hit: %v)",
			speedup, firstCallDuration, secondCallDuration)
	}

	// 打印缓存统计
	stats := manager.GetCacheStats()
	t.Logf("Cache stats: %+v", stats)
}

// TestBatchVsSingleRequests 测试批量请求vs单个请求的性能
func TestBatchVsSingleRequests(t *testing.T) {
	manager := GetRankManager()
	ctx := context.Background()
	rankType := int32(2)

	// 生成测试用的玩家ID列表
	playerIDs := make([]uint64, 50)
	for i := range playerIDs {
		playerIDs[i] = uint64(20000 + i)
	}

	// 测试单个请求
	start := time.Now()
	for _, playerID := range playerIDs {
		_, err := manager.GetPlayerRank(ctx, rankType, playerID)
		if err != nil {
			t.Errorf("GetPlayerRank failed for player %d: %v", playerID, err)
		}
	}
	singleRequestDuration := time.Since(start)

	// 清除缓存以确保公平比较
	manager.cacheManager.ClearRankCache(rankType)

	// 测试批量请求
	start = time.Now()
	_, err := manager.GetMultiPlayerRank(ctx, rankType, playerIDs)
	if err != nil {
		t.Errorf("GetMultiPlayerRank failed: %v", err)
		return
	}
	batchRequestDuration := time.Since(start)

	// 批量请求应该比单个请求快
	if batchRequestDuration < singleRequestDuration {
		speedup := float64(singleRequestDuration) / float64(batchRequestDuration)
		t.Logf("Batch request speedup: %.2fx (single: %v, batch: %v)",
			speedup, singleRequestDuration, batchRequestDuration)
	} else {
		t.Logf("Warning: Batch request (%v) not faster than single requests (%v)",
			batchRequestDuration, singleRequestDuration)
	}
}

// TestCacheExpiration 测试缓存过期
func TestCacheExpiration(t *testing.T) {
	manager := GetRankManager()
	ctx := context.Background()
	rankType := int32(2)
	playerID := uint64(30000)

	// 设置短的缓存过期时间用于测试
	originalExpireTime := manager.cacheManager.cacheExpireTime
	manager.SetCacheExpireTime(1) // 1秒过期
	defer manager.SetCacheExpireTime(originalExpireTime)

	// 第一次调用
	_, err := manager.GetPlayerRank(ctx, rankType, playerID)
	if err != nil {
		t.Errorf("GetPlayerRank failed: %v", err)
		return
	}

	// 检查缓存是否存在
	cache := manager.cacheManager.GetPlayerRankCache(rankType, playerID)
	if cache == nil {
		t.Error("Cache should exist after first call")
		return
	}

	// 等待缓存过期
	time.Sleep(2 * time.Second)

	// 检查缓存是否已过期
	cache = manager.cacheManager.GetPlayerRankCache(rankType, playerID)
	if cache != nil {
		t.Error("Cache should be expired")
	}

	t.Log("Cache expiration test passed")
}

// PerformanceTestSuite 性能测试套件
func PerformanceTestSuite(t *testing.T) {
	t.Log("Starting rank system performance test suite")

	// 模拟高并发场景
	concurrency := 10
	requestsPerGoroutine := 100

	manager := GetRankManager()
	ctx := context.Background()
	rankType := int32(2)

	start := time.Now()

	// 创建多个goroutine模拟并发请求
	done := make(chan bool, concurrency)

	for i := 0; i < concurrency; i++ {
		go func(goroutineID int) {
			defer func() { done <- true }()

			for j := 0; j < requestsPerGoroutine; j++ {
				// 随机选择操作类型
				switch rand.Intn(3) {
				case 0:
					// 获取排行榜列表
					_, _, err := manager.GetRankList(ctx, rankType, 1, 20)
					if err != nil {
						t.Errorf("Goroutine %d: GetRankList failed: %v", goroutineID, err)
					}
				case 1:
					// 获取单个玩家排名
					playerID := uint64(40000 + rand.Intn(1000))
					_, err := manager.GetPlayerRank(ctx, rankType, playerID)
					if err != nil {
						t.Errorf("Goroutine %d: GetPlayerRank failed: %v", goroutineID, err)
					}
				case 2:
					// 批量获取玩家排名
					playerIDs := make([]uint64, 10)
					for k := range playerIDs {
						playerIDs[k] = uint64(50000 + rand.Intn(1000))
					}
					_, err := manager.GetMultiPlayerRank(ctx, rankType, playerIDs)
					if err != nil {
						t.Errorf("Goroutine %d: GetMultiPlayerRank failed: %v", goroutineID, err)
					}
				}
			}
		}(i)
	}

	// 等待所有goroutine完成
	for i := 0; i < concurrency; i++ {
		<-done
	}

	duration := time.Since(start)
	totalRequests := concurrency * requestsPerGoroutine
	qps := float64(totalRequests) / duration.Seconds()

	t.Logf("Performance test completed, concurrency: %d, total_requests: %d, duration: %v, qps: %.2f", concurrency, totalRequests, duration, qps)

	// 打印缓存统计
	stats := manager.GetCacheStats()
	t.Logf("Final cache stats: %+v", stats)

	t.Logf("Performance test: %d requests in %v (%.2f QPS)",
		totalRequests, duration, qps)
}
