package discovery

import (
	"fmt"
	"os"
	"sync"
	"time"

	"kairo_paradise_server/internal/logger"
)

var (
	// Global registry instance
	registry ServiceRegistry
	// Global registration instance
	registration *ServiceRegistration
	// Mutex for initialization
	initMutex sync.Mutex
)

// Initialize initializes the service discovery system
func Initialize(config Config) error {
	initMutex.Lock()
	defer initMutex.Unlock()

	if registry != nil {
		return nil // Already initialized
	}

	etcdRegistry, err := NewEtcdRegistry(config)
	if err != nil {
		return fmt.Errorf("failed to create etcd registry: %w", err)
	}

	registry = etcdRegistry
	logger.Info("Service discovery initialized")
	return nil
}

// RegisterService registers the current service with the discovery system
func RegisterService(serviceType ServiceType, address string, metadata map[string]string) (*string, error) {
	if registry == nil {
		return nil, fmt.Errorf("service discovery not initialized")
	}

	// Format the address to ensure it includes host information
	formattedAddress, err := FormatServiceAddress(address)
	if err != nil {
		return nil, fmt.Errorf("invalid service address: %w", err)
	}

	// Generate a unique ID for this service instance
	hostname, err := os.Hostname()
	if err != nil {
		hostname = "unknown"
	}

	id := fmt.Sprintf("Register service: %s-%s-%d", string(serviceType), hostname, os.Getpid())

	info := &ServiceInfo{
		ID:       id,
		Type:     serviceType,
		Address:  formattedAddress,
		Metadata: metadata,
	}

	etcdRegistry, ok := registry.(*EtcdRegistry)
	if !ok {
		return &id, fmt.Errorf("registry is not an etcd registry")
	}

	reg, err := etcdRegistry.RegisterService(info)
	if err != nil {
		return &id, fmt.Errorf("failed to register service: %w", err)
	}

	registration = reg
	return &id, nil
}

// GetServicesByType returns all services of a specific type
func GetServicesByType(serviceType ServiceType) ([]*ServiceInfo, error) {
	if registry == nil {
		return nil, fmt.Errorf("service discovery not initialized")
	}

	return registry.GetServicesByType(serviceType), nil
}

// GetService returns a service by ID
func GetService(id string) (*ServiceInfo, error) {
	if registry == nil {
		return nil, fmt.Errorf("service discovery not initialized")
	}

	service, ok := registry.GetService(id)
	if !ok {
		return nil, fmt.Errorf("service %s not found", id)
	}

	return service, nil
}

// UpdateServiceStats updates the statistics for a service
func UpdateServiceStats(id string, stats *ServiceStats) error {
	if registry == nil {
		return fmt.Errorf("service discovery not initialized")
	}

	service, ok := registry.GetService(id)
	if !ok {
		return fmt.Errorf("service %s not found", id)
	}

	// Update the stats
	stats.UpdatedAt = time.Now()
	service.Stats = stats

	// If using etcd registry, update the service in etcd
	_, ok = registry.(*EtcdRegistry)
	if ok && registration != nil {
		// Update the service info in etcd
		return registration.Refresh(service)
	}

	return nil
}

// GetServicesByTypeAndStatus returns all services of a specific type with the given status
func GetServicesByTypeAndStatus(serviceType ServiceType, status ServiceStatus) ([]*ServiceInfo, error) {
	if registry == nil {
		return nil, fmt.Errorf("service discovery not initialized")
	}

	allServices := registry.GetServicesByType(serviceType)
	var filteredServices []*ServiceInfo

	for _, service := range allServices {
		if service.Stats != nil && service.Stats.Status == status {
			filteredServices = append(filteredServices, service)
		}
	}

	return filteredServices, nil
}

// GetBestService returns the best service based on connection count and status
func GetBestService(serviceType ServiceType) (*ServiceInfo, error) {
	if registry == nil {
		return nil, fmt.Errorf("service discovery not initialized")
	}
	// Get all available services of the given type
	services := registry.GetServicesByType(serviceType)
	if len(services) == 0 {
		return nil, fmt.Errorf("no %s services available", serviceType)
	}

	// First, try to find services with normal status
	var candidates []*ServiceInfo
	for _, service := range services {
		if service.Stats != nil && service.Stats.Status == ServiceStatusNormal {
			candidates = append(candidates, service)
		}
	}

	// If no normal services, try busy services
	if len(candidates) == 0 {
		for _, service := range services {
			if service.Stats != nil && service.Stats.Status == ServiceStatusBusy {
				candidates = append(candidates, service)
			}
		}
	}

	// If still no candidates, use any available service
	if len(candidates) == 0 {
		candidates = services
	}

	var bestService *ServiceInfo
	lowestConnCount := -1

	for _, service := range candidates {
		// Skip services in maintenance or full status
		if service.Stats != nil && (service.Stats.Status == ServiceStatusMaintenance || service.Stats.Status == ServiceStatusFull) {
			continue
		}
		// If no stats available, just use the first service
		if service.Stats == nil {
			if bestService == nil {
				bestService = service
			}
			continue
		}
		// Compare connection counts
		if lowestConnCount == -1 || service.Stats.ConnectionCount < lowestConnCount {
			bestService = service
			lowestConnCount = service.Stats.ConnectionCount
		}
	}

	if bestService == nil {
		return nil, fmt.Errorf("no suitable %s services available	", serviceType)
	}
	return bestService, nil
}

// Shutdown stops the service discovery system
func Shutdown() error {
	initMutex.Lock()
	defer initMutex.Unlock()

	if registration != nil {
		registration.Stop()
		registration = nil
	}

	if registry != nil {
		err := registry.Close()
		registry = nil
		return err
	}

	return nil
}

// GetGateWay returns the URL of the best available gate service
func GetGateWay() string {
	service, err := GetBestService(ServiceTypeGate)
	if err != nil {
		logger.Errorf("Failed to get best gate service: %v", err)
		return ""
	}

	addr := service.Address
	if service.Stats != nil {
		logger.Infof("Using gate service at %s (status: %s, connections: %d/%d)",
			addr, service.Stats.Status, service.Stats.ConnectionCount, service.Stats.MaxConnections)
	} else {
		logger.Infof("Using gate service at %s (no stats available)", addr)
	}

	return addr
}

func GetServices() map[string]string {
	serviceTypeList := []ServiceType{ServiceTypeGame, ServiceTypeGateGRPC, ServiceTypePublic}
	maps := make(map[string]string, len(serviceTypeList))
	for _, serviceStr := range serviceTypeList {
		service, err := GetBestService(serviceStr)
		if err != nil {
			logger.Errorf("Failed to get best service: %v", err)
			continue
		}
		logger.Infof("Using %s service at %s", serviceStr, service.Address)
		maps[string(serviceStr)] = service.Address
	}
	return maps
}
