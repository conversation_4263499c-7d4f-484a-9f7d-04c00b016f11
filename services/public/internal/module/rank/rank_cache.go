package rank

import (
	"context"
	"kairo_paradise_server/internal/logger"
	"sync"
	"time"

	"go.uber.org/zap"
)

// CacheManager 缓存管理器
type CacheManager struct {
	// 热点排行榜缓存 (前N名)
	hotRankCaches map[int32]*Cache
	// 玩家排名缓存 - 只存储排名和分数信息
	playerRankCaches map[uint64]*PlayerRankCache
	// 缓存配置
	hotCacheSize    int32 // 热点缓存大小
	cacheExpireTime int64 // 缓存过期时间
	// 锁
	mutex sync.RWMutex
}

// NewCacheManager 创建缓存管理器
func NewCacheManager() *CacheManager {
	return &CacheManager{
		hotRankCaches:    make(map[int32]*Cache),
		playerRankCaches: make(map[string]*PlayerRankCache),
		hotCacheSize:     100, // 缓存前100名
		cacheExpireTime:  300, // 5分钟过期
	}
}

// NewCacheManagerWithConfig 使用配置创建缓存管理器
func NewCacheManagerWithConfig(hotCacheSize int32, cacheExpireTime int64) *CacheManager {
	return &CacheManager{
		hotRankCaches:    make(map[int32]*Cache),
		playerRankCaches: make(map[string]*PlayerRankCache),
		hotCacheSize:     hotCacheSize,
		cacheExpireTime:  cacheExpireTime,
	}
}

// GetHotRankCache 获取热点排行榜缓存
func (cm *CacheManager) GetHotRankCache(rankType int32) *Cache {
	cm.mutex.RLock()
	defer cm.mutex.RUnlock()

	cache, exists := cm.hotRankCaches[rankType]
	if !exists || cm.isCacheExpired(cache.UpdateTime) {
		return nil
	}
	return cache
}

// SetHotRankCache 设置热点排行榜缓存
func (cm *CacheManager) SetHotRankCache(rankType int32, entries []*Entry, totalCount int64) {
	cm.mutex.Lock()
	defer cm.mutex.Unlock()

	// 只缓存前N名
	cacheEntries := entries
	if int32(len(entries)) > cm.hotCacheSize {
		cacheEntries = entries[:cm.hotCacheSize]
	}

	cache := &Cache{
		Entries:    cacheEntries,
		TotalCount: totalCount,
		UpdateTime: time.Now().Unix(),
	}

	cm.hotRankCaches[rankType] = cache
	logger.Debug("Set hot rank cache",
		zap.Int32("rankType", rankType),
		zap.Int("entryCount", len(cacheEntries)))
}

// GetPlayerRankCache 获取玩家数据缓存
func (cm *CacheManager) GetPlayerRankCache(rankType int32, playerID uint64) *PlayerRankCache {
	key := cm.getPlayerCacheKey(rankType, playerID)
	if cache, exists := cm.playerRankCaches[key]; exists {
		return cache
	}
	// 从redis中获取数据
	entry, err := getRankEntry(context.Background(), rankType, playerID)
	if err != nil {
		logger.Warn("Failed to get rank entry", zap.Error(err), zap.Uint64("playerID", playerID))
		return nil
	}
	if entry == nil {
		return nil
	}
	cache := &PlayerRankCache{
		Rank:       -1,
		Entry:      entry,
		UpdateTime: entry.UpdateTime,
	}
	cm.mutex.Lock()
	defer cm.mutex.Unlock()
	cm.playerRankCaches[key] = cache

	return cache
}

// SetPlayerRankCache 设置玩家排名缓存
func (cm *CacheManager) SetPlayerRankCache(ctx context.Context, rankType int32, playerID uint64, rank int32, entry *Entry) {
	// 先保存到redis
	entry.Ranking = rank
	_ = setRankEntry(ctx, rankType, entry)
	key := cm.getPlayerCacheKey(rankType, playerID)
	cache := &PlayerRankCache{
		Rank:       rank, // TODO 这里似乎缓存ranking数据不太合理
		Entry:      entry,
		UpdateTime: time.Now().Unix(),
	}

	cm.mutex.Lock()
	defer cm.mutex.Unlock()
	cm.playerRankCaches[key] = cache
	logger.Debug("Set player rank cache",
		zap.Int32("rankType", rankType),
		zap.Uint64("playerID", playerID),
		zap.Int32("rank", rank))
}

// ClearRankCache 清除排行榜相关缓存
func (cm *CacheManager) ClearRankCache(rankType int32) {
	cm.mutex.Lock()
	defer cm.mutex.Unlock()

	// 清除热点排行榜缓存
	delete(cm.hotRankCaches, rankType)

	// 清除相关玩家排名缓存
	prefix := cm.getRankPrefix(rankType)
	for key := range cm.playerRankCaches {
		if len(key) > len(prefix) && key[:len(prefix)] == prefix {
			delete(cm.playerRankCaches, key)
		}
	}

	logger.Debug("Cleared rank cache", zap.Int32("rankType", rankType))
}

// ClearExpiredCache 清除过期缓存
func (cm *CacheManager) ClearExpiredCache() {
	cm.mutex.Lock()
	defer cm.mutex.Unlock()

	now := time.Now().Unix()
	expiredCount := 0

	// 清除过期的热点排行榜缓存
	for rankType, cache := range cm.hotRankCaches {
		if now-cache.UpdateTime > cm.cacheExpireTime {
			delete(cm.hotRankCaches, rankType)
			expiredCount++
		}
	}

	// 清除过期的玩家排名缓存
	for key, cache := range cm.playerRankCaches {
		if now-cache.UpdateTime > cm.cacheExpireTime {
			delete(cm.playerRankCaches, key)
			expiredCount++
		}
	}

	if expiredCount > 0 {
		logger.Debug("Cleared expired cache", zap.Int("count", expiredCount))
	}
}

// GetCacheStats 获取缓存统计信息
func (cm *CacheManager) GetCacheStats() map[string]interface{} {
	cm.mutex.RLock()
	defer cm.mutex.RUnlock()

	return map[string]interface{}{
		"hot_rank_cache_count":    len(cm.hotRankCaches),
		"player_rank_cache_count": len(cm.playerRankCaches),
		"hot_cache_size":          cm.hotCacheSize,
		"cache_expire_time":       cm.cacheExpireTime,
	}
}

// BatchGetPlayerRanks 批量获取玩家排名（优化版）
func (cm *CacheManager) BatchGetPlayerRanks(ctx context.Context, rankType int32, playerIDs []uint64,
	fallbackFunc func(context.Context, int32, []uint64) ([]*RankInfo, error)) ([]*RankInfo, error) {

	results := make([]*RankInfo, len(playerIDs))
	uncachedIDs := make([]uint64, 0)
	uncachedIndices := make([]int, 0)

	// 先从缓存获取
	for i, playerID := range playerIDs {
		cache := cm.GetPlayerRankCache(rankType, playerID)
		if cache != nil {
			if cache.Rank == -1 {
				results[i] = &RankInfo{Rank: -1}
			} else {
				results[i] = &RankInfo{
					Rank:  cache.Rank,
					Entry: *cache.Entry,
				}
			}
		} else {
			uncachedIDs = append(uncachedIDs, playerID)
			uncachedIndices = append(uncachedIndices, i)
		}
	}

	// 如果有未缓存的数据，调用fallback函数
	if len(uncachedIDs) > 0 {
		uncachedResults, err := fallbackFunc(ctx, rankType, uncachedIDs)
		if err != nil {
			return nil, err
		}

		// 将结果填入对应位置并缓存
		for i, result := range uncachedResults {
			idx := uncachedIndices[i]
			playerID := uncachedIDs[i]
			results[idx] = result

			// 缓存结果
			if result.Rank == -1 {
				cm.SetPlayerRankCache(ctx, rankType, playerID, -1, nil)
			} else {
				cm.SetPlayerRankCache(ctx, rankType, playerID, result.Rank, &result.Entry)
			}
		}
	}

	return results, nil
}

// 辅助方法
func (cm *CacheManager) isCacheExpired(updateTime int64) bool {
	return time.Now().Unix()-updateTime > cm.cacheExpireTime
}

func (cm *CacheManager) getPlayerCacheKey(rankType int32, playerID uint64) string {
	return cm.getRankPrefix(rankType) + string(rune(playerID))
}

func (cm *CacheManager) getRankPrefix(rankType int32) string {
	return string(rune(rankType)) + ":"
}

// StartCacheCleanup 启动缓存清理任务
func (cm *CacheManager) StartCacheCleanup(stopCh <-chan struct{}) {
	ticker := time.NewTicker(5 * time.Minute) // 每5分钟清理一次
	defer ticker.Stop()

	for {
		select {
		case <-ticker.C:
			cm.ClearExpiredCache()
		case <-stopCh:
			return
		}
	}
}
