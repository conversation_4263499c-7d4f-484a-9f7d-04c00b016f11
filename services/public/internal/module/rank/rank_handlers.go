package rank

import (
	"context"
	"github.com/golang/protobuf/proto"
	"kairo_paradise_server/internal/handler"
	"kairo_paradise_server/internal/logger"
	"kairo_paradise_server/services/pb"
	"kairo_paradise_server/services/pb/msg"

	"go.uber.org/zap"
)

const (
	// 消息ID
	UpdateRankRequestID      = 10400
	UpdateRankResponseID     = 10401
	GetRankListRequestID     = 10402
	GetRankListResponseID    = 10403
	GetPlayerRankRequestID   = 10404
	GetPlayerRankResponseID  = 10405
	GetMultiPlayerRankReqID  = 10406
	GetMultiPlayerRankRespID = 10407
	ClearRankRequestID       = 10408
	ClearRankResponseID      = 10409
)

// Register 注册排行榜消息处理器
func Register() {
	// 注册更新排行榜请求处理器
	handler.Register(msg.PCK(UpdateRankRequestID), HandleUpdateRank)

	// 注册获取排行榜列表请求处理器
	handler.Register(msg.PCK(GetRankListRequestID), HandleGetRankList)

	// 注册获取玩家排名请求处理器
	handler.Register(msg.PCK(GetPlayerRankRequestID), HandleGetPlayerRank)

	// 注册批量获取玩家排名请求处理器
	handler.Register(msg.PCK(GetMultiPlayerRankReqID), HandleGetMultiPlayerRank)

	// 注册清除排行榜请求处理器
	handler.Register(msg.PCK(ClearRankRequestID), HandleClearRank)

	logger.Info("Rank handlers registered")
}

// HandleUpdateRank 处理更新排行榜请求
func HandleUpdateRank(ctx context.Context, payload []byte) (proto.Message, error) {
	// 解析请求
	request := &pb.G2SUpdateRankRequest{}
	err := proto.Unmarshal(payload, request)
	if err != nil {
		logger.Error("Failed to unmarshal update rank request", zap.Error(err))
		return createUpdateRankErrorResponse(pb.ResponseCode_fail, "Invalid request format"), nil
	}

	// 获取排行榜类型
	rankType := int32(request.GetRankType())

	// 获取排行榜条目
	entry := request.GetEntry()
	if entry == nil {
		logger.Error("Rank entry is nil")
		return createUpdateRankErrorResponse(pb.pb.ResponseCode_fail, "Rank entry is required"), nil
	}

	// 转换为内部格式
	rankEntry := NewRankEntryFromProto(entry)

	// 更新排行榜
	rank, err := GetRankManager().UpdateRankEntry(ctx, rankType, rankEntry)
	if err != nil {
		logger.Error("Failed to update rank entry", zap.Error(err))
		return createUpdateRankErrorResponse(pb.pb.ResponseCode_fail, err.Error()), nil
	}

	// 创建响应
	response := &pb.S2GUpdateRankResponse{
		Code: pb.ResponseCode_normal.Enum(),
		Rank: proto.Int32(rank),
	}

	return response, nil
}

// HandleGetRankList 处理获取排行榜列表请求
func HandleGetRankList(ctx context.Context, payload []byte) (proto.Message, error) {
	// 解析请求
	request := &pb.G2SGetRankListRequest{}
	err := proto.Unmarshal(payload, request)
	if err != nil {
		logger.Error("Failed to unmarshal get rank list request", zap.Error(err))
		return createGetRankListErrorResponse(pb.pb.ResponseCode_fail, "Invalid request format"), nil
	}

	// 获取排行榜类型
	rankType := int32(request.GetRankType())

	// 获取起始排名和数量
	start := request.GetStart()
	count := request.GetCount()

	// 获取排行榜列表
	entries, totalCount, err := GetRankManager().GetRankList(ctx, rankType, start, count)
	if err != nil {
		logger.Error("Failed to get rank list", zap.Error(err))
		return createGetRankListErrorResponse(pb.pb.ResponseCode_fail, err.Error()), nil
	}

	// 转换为protobuf格式
	protoEntries := make([]*pb.RankEntryData, 0, len(entries))
	for _, entry := range entries {
		protoEntries = append(protoEntries, entry.ToProto())
	}

	// 创建响应
	response := &pb.S2GGetRankListResponse{
		Code:       pb.ResponseCode_normal.Enum(),
		Entries:    protoEntries,
		TotalCount: proto.Int32(int32(totalCount)),
	}

	return response, nil
}

// HandleGetPlayerRank 处理获取玩家排名请求
func HandleGetPlayerRank(ctx context.Context, payload []byte) (proto.Message, error) {
	// 解析请求
	request := &pb.G2SGetPlayerRankRequest{}
	err := proto.Unmarshal(payload, request)
	if err != nil {
		logger.Error("Failed to unmarshal get player rank request", zap.Error(err))
		return createGetPlayerRankErrorResponse(pb.pb.ResponseCode_fail, "Invalid request format"), nil
	}

	// 获取排行榜类型和玩家ID
	rankType := int32(request.GetRankType())
	playerID := request.GetPlayerId()

	// 获取玩家排名
	rankInfo, err := GetRankManager().GetPlayerRank(ctx, rankType, playerID)
	if err != nil {
		logger.Error("Failed to get player rank", zap.Error(err))
		return createGetPlayerRankErrorResponse(pb.pb.ResponseCode_fail, err.Error()), nil
	}

	// 创建响应
	response := &pb.S2GGetPlayerRankResponse{
		Code: pb.ResponseCode_normal.Enum(),
		Rank: proto.Int32(rankInfo.Rank),
	}

	// 如果玩家有排名，添加排行榜条目
	if rankInfo.Rank > 0 {
		response.Entry = rankInfo.Entry.ToProto()
	}

	return response, nil
}

// HandleGetMultiPlayerRank 处理批量获取玩家排名请求
func HandleGetMultiPlayerRank(ctx context.Context, payload []byte) (proto.Message, error) {
	// 解析请求
	request := &pb.G2SGetMultiPlayerRankRequest{}
	err := proto.Unmarshal(payload, request)
	if err != nil {
		logger.Error("Failed to unmarshal get multi player rank request", zap.Error(err))
		return createGetMultiPlayerRankErrorResponse(pb.pb.ResponseCode_fail, "Invalid request format"), nil
	}

	// 获取排行榜类型和玩家ID列表
	rankType := int32(request.GetRankType())
	playerIDs := request.GetPlayerIds()

	// 获取玩家排名
	rankInfos, err := GetRankManager().GetMultiPlayerRank(ctx, rankType, playerIDs)
	if err != nil {
		logger.Error("Failed to get multi player rank", zap.Error(err))
		return createGetMultiPlayerRankErrorResponse(pb.pb.ResponseCode_fail, err.Error()), nil
	}

	// 转换为protobuf格式
	protoRankInfos := make([]*pb.PlayerRankInfo, 0, len(rankInfos))
	for _, rankInfo := range rankInfos {
		protoRankInfo := rankInfo.ToProto()
		protoRankInfos = append(protoRankInfos, protoRankInfo)
	}

	// 创建响应
	response := &pb.S2GGetMultiPlayerRankResponse{
		Code:        pb.ResponseCode_normal.Enum(),
		PlayerRanks: protoRankInfos,
	}

	return response, nil
}

// HandleClearRank 处理清除排行榜请求
func HandleClearRank(ctx context.Context, payload []byte) (proto.Message, error) {
	// 解析请求
	request := &pb.G2SClearRankRequest{}
	err := proto.Unmarshal(payload, request)
	if err != nil {
		logger.Error("Failed to unmarshal clear rank request", zap.Error(err))
		return createClearRankErrorResponse(pb.pb.ResponseCode_fail, "Invalid request format"), nil
	}

	// 获取排行榜类型
	rankType := int32(request.GetRankType())

	// 清除排行榜
	err = GetRankManager().ClearRank(ctx, rankType)
	if err != nil {
		logger.Error("Failed to clear rank", zap.Error(err))
		return createClearRankErrorResponse(pb.pb.ResponseCode_fail, err.Error()), nil
	}

	// 创建响应
	response := &pb.S2GClearRankResponse{
		Code: pb.ResponseCode_normal.Enum(),
	}

	return response, nil
}

// 创建错误响应的辅助函数
func createUpdateRankErrorResponse(code pb.ResponseCode, message string) *pb.S2GUpdateRankResponse {
	return &pb.S2GUpdateRankResponse{
		Code:    code.Enum(),
		Message: proto.String(message),
		Rank:    proto.Int32(-1),
	}
}

func createGetRankListErrorResponse(code pb.ResponseCode, message string) *pb.S2GGetRankListResponse {
	return &pb.S2GGetRankListResponse{
		Code:    code.Enum(),
		Message: proto.String(message),
	}
}

func createGetPlayerRankErrorResponse(code pb.ResponseCode, message string) *pb.S2GGetPlayerRankResponse {
	return &pb.S2GGetPlayerRankResponse{
		Code:    code.Enum(),
		Message: proto.String(message),
		Rank:    proto.Int32(-1),
	}
}

func createGetMultiPlayerRankErrorResponse(code pb.ResponseCode, message string) *pb.S2GGetMultiPlayerRankResponse {
	return &pb.S2GGetMultiPlayerRankResponse{
		Code:    code.Enum(),
		Message: proto.String(message),
	}
}

func createClearRankErrorResponse(code pb.ResponseCode, message string) *pb.S2GClearRankResponse {
	return &pb.S2GClearRankResponse{
		Code:    code.Enum(),
		Message: proto.String(message),
	}
}
