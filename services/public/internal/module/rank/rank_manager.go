package rank

import (
	"context"
	"fmt"
	"kairo_paradise_server/internal/game_config"
	"kairo_paradise_server/internal/logger"
	"sync"
	"time"

	"go.uber.org/zap"
)

// Manager 排行榜管理器
type Manager struct {
	// 排行榜配置
	rankConfigs map[int32]*Config
	// 实时排行榜缓存
	realtimeRanks map[int32]bool
	// 定时更新排行榜缓存
	periodicRanks map[int32]bool
	// 缓存管理器 - 每个排行榜有独立的缓存管理器
	cacheManagers map[int32]*CacheManager
	// 定时器
	ticker *time.Ticker
	// 互斥锁
	mutex sync.RWMutex
	// 停止信号
	stopCh chan struct{}
}

var (
	// 单例实例
	instance *Manager
	once     sync.Once
)

// GetRankManager 获取排行榜管理器实例
func GetRankManager() *Manager {
	once.Do(func() {
		instance = &Manager{
			rankConfigs:   make(map[int32]*Config),
			realtimeRanks: make(map[int32]bool),
			periodicRanks: make(map[int32]bool),
			cacheManager:  NewCacheManager(),
			stopCh:        make(chan struct{}),
		}
	})
	return instance
}

// Initialize 初始化排行榜管理器
func (m *Manager) Initialize() error {
	// 加载排行榜配置
	err := m.loadRankConfigs()
	if err != nil {
		return err
	}

	// 启动定时更新任务
	m.startPeriodicUpdate()

	// 启动缓存清理任务
	go m.cacheManager.StartCacheCleanup(m.stopCh)

	logger.Info("Rank manager initialized")
	return nil
}

// loadRankConfigs 加载排行榜配置
func (m *Manager) loadRankConfigs() error {
	m.mutex.Lock()
	defer m.mutex.Unlock()

	// 从配置中加载排行榜信息
	for _, id := range game_config.RankConfig.GetIds() {
		config := game_config.RankConfig.Item(id)
		if config == nil {
			continue
		}

		// 创建排行榜配置
		rankConfig := &Config{
			RankType:      id,
			MinScoreLimit: config.GetMinValueLimit(),
			MaxQueryLimit: config.GetMaxQueryLimit(),
			ShowRankLimit: config.GetShowRankLimit(),
			MaxRankLimit:  config.GetMaxRankLimit(),
		}

		// 如果没有设置MaxRankLimit，设置默认值
		if rankConfig.MaxRankLimit == 0 {
			if m.realtimeRanks[id] {
				rankConfig.MaxRankLimit = 300 // 实时排行榜默认显示前300名
			} else {
				rankConfig.MaxRankLimit = 3000 // 定时排行榜默认显示前3000名
			}
		}

		// 如果没有设置ShowRankLimit，使用MaxRankLimit的值
		if rankConfig.ShowRankLimit == 0 {
			rankConfig.ShowRankLimit = rankConfig.MaxRankLimit
		}

		// 如果没有设置MaxQueryLimit，设置默认值
		if rankConfig.MaxQueryLimit == 0 {
			rankConfig.MaxQueryLimit = 500 // 默认查询上限500
		}

		m.rankConfigs[id] = rankConfig
		logger.Info("Loaded rank config",
			zap.Int32("rankType", rankConfig.RankType),
			zap.Int32("minScoreLimit", rankConfig.MinScoreLimit),
			zap.Int32("maxQueryLimit", rankConfig.MaxQueryLimit),
			zap.Int32("showRankLimit", rankConfig.ShowRankLimit),
			zap.Int32("maxRankLimit", rankConfig.MaxRankLimit),
			zap.Int64("updateInterval", rankConfig.UpdateInterval),
			zap.Bool("isRealtime", rankConfig.IsRealtime))
	}

	return nil
}

// startPeriodicUpdate 启动定时更新任务
func (m *Manager) startPeriodicUpdate() {
	m.ticker = time.NewTicker(1 * time.Minute)

	go func() {
		for {
			select {
			case <-m.ticker.C:
				m.checkAndUpdatePeriodicRanks()
			case <-m.stopCh:
				m.ticker.Stop()
				return
			}
		}
	}()
}

// Stop 停止排行榜管理器
func (m *Manager) Stop() {
	close(m.stopCh)
}

// checkAndUpdatePeriodicRanks 检查并更新定时排行榜
func (m *Manager) checkAndUpdatePeriodicRanks() {
	ctx := context.Background()
	now := time.Now().Unix()

	m.mutex.RLock()
	defer m.mutex.RUnlock()

	for rankType, _ := range m.periodicRanks {
		config := m.rankConfigs[rankType]
		if config == nil || config.IsRealtime {
			continue
		}
		// 获取上次更新时间
		lastUpdate, err := getLastUpdateTime(ctx, rankType)
		if err != nil {
			logger.Error("Failed to get last update time", zap.Error(err), zap.Int32("rankType", rankType))
			continue
		}

		// 如果达到更新时间，则更新排行榜
		if now-lastUpdate >= config.UpdateInterval {
			logger.Info("Updating periodic rank", zap.Int32("rankType", rankType))

			// 更新排行榜
			// TODO 这里实际上应该从游戏服或数据库获取最新数据
			// 目前只更新时间戳
			err := updateLastUpdateTime(ctx, rankType)
			if err != nil {
				logger.Error("Failed to update last update time", zap.Error(err), zap.Int32("rankType", rankType))
			}
		}
	}
}

// UpdateRankEntry 更新排行榜条目
func (m *Manager) UpdateRankEntry(ctx context.Context, rankType int32, entry *Entry) (int32, error) {
	config, exists := m.rankConfigs[rankType]
	if !exists {
		return -1, fmt.Errorf("rank type %d not found", rankType)
	}
	// 检查分数是否达到上榜要求
	if entry.Prosperity < config.MinScoreLimit {
		return -1, nil
	}

	// 设置合格缓存
	_ = setRankEligibility(ctx, rankType, entry.PlayerID, true)
	// 保存排行榜到redis中
	minScore, err := saveRankEntry(ctx, rankType, config.MaxRankLimit, entry, config.IsRealtime)
	if err != nil {
		return -1, err
	}
	// 更新最低上榜分数
	m.SetMinScoreLimit(rankType, minScore)
	// 获取玩家排名
	rank, err := getPlayerRank(ctx, rankType, entry.PlayerID, config.IsRealtime)
	if err != nil {
		return -1, err
	}
	// 如果排名超出最大排行榜数量，则返回-1
	if rank > config.MaxRankLimit {
		return -1, nil
	}
	// 保存玩家的外观和排名数据
	m.cacheManager.SetPlayerRankCache(ctx, rankType, entry.PlayerID, rank, entry)

	return rank, nil
}

// GetRankList 获取排行榜列表
func (m *Manager) GetRankList(ctx context.Context, rankType int32, start, count int32) ([]*Entry, int64, error) {
	// 读的时候不加锁，加锁影响性能，而且也没有必要加锁，没有竞态条件
	config, exists := m.rankConfigs[rankType]
	if !exists {
		return nil, 0, fmt.Errorf("rank type %d not found", rankType)
	}

	// 检查请求参数，只获取排行榜的显示部分数据
	if count > config.ShowRankLimit {
		count = config.ShowRankLimit
	}

	// 尝试从缓存获取
	cache := m.cacheManager.GetHotRankCache(rankType)
	if cache != nil && start == 1 && count <= int32(len(cache.Entries)) {
		// 缓存命中，直接返回缓存数据
		endIndex := count
		if endIndex > int32(len(cache.Entries)) {
			endIndex = int32(len(cache.Entries))
		}
		return cache.Entries[0:endIndex], cache.TotalCount, nil
	}

	// 缓存未命中，使用优化的Redis获取方法
	entries, totalCount, err := GetRankRangeOptimized(ctx, rankType, start, count, config.IsRealtime)
	if err != nil {
		return nil, 0, err
	}

	// 如果是获取前面的数据，更新热点缓存
	if start == 1 && len(entries) > 0 {
		m.cacheManager.SetHotRankCache(rankType, entries, totalCount)
	}

	return entries, totalCount, nil
}

// GetPlayerRank 获取玩家排名
func (m *Manager) GetPlayerRank(ctx context.Context, rankType int32, playerID uint64) (*RankInfo, error) {
	config, exists := m.rankConfigs[rankType]
	if !exists {
		return nil, fmt.Errorf("rank type %d not found", rankType)
	}

	// 尝试从缓存获取
	cache := m.cacheManager.GetPlayerRankCache(rankType, playerID)
	if cache != nil {
		if cache.Rank == -1 {
			return &RankInfo{Rank: -1}, nil
		}
		return &RankInfo{
			Rank:  cache.Rank,
			Entry: *cache.Entry,
		}, nil
	}

	// 检查玩家是否有资格上榜
	eligible, err := getRankEligibility(ctx, rankType, playerID)
	if err != nil {
		logger.Warn("Failed to get rank eligibility", zap.Error(err))
		// 继续执行，不要因为缓存错误影响查询
	} else if !eligible {
		// 如果缓存显示玩家不合格，直接返回并缓存结果
		m.cacheManager.SetPlayerRankCache(ctx, rankType, playerID, -1, nil)
		return &RankInfo{Rank: -1}, nil
	}

	// 获取玩家排名
	rank, err := getPlayerRank(ctx, rankType, playerID, config.IsRealtime)
	if err != nil {
		return nil, err
	}
	// 如果玩家未上榜
	if rank == -1 {
		m.cacheManager.SetPlayerRankCache(ctx, rankType, playerID, -1, nil)
		return &RankInfo{Rank: -1}, nil
	}

	// 获取玩家排行榜条目
	entry, err := getRankEntry(ctx, rankType, playerID)
	if err != nil {
		return nil, err
	}
	if entry == nil {
		m.cacheManager.SetPlayerRankCache(ctx, rankType, playerID, -1, nil)
		return &RankInfo{Rank: -1}, nil
	}
	// 缓存结果
	m.cacheManager.SetPlayerRankCache(ctx, rankType, playerID, rank, entry)

	return &RankInfo{
		Rank:  rank,
		Entry: *entry,
	}, nil
}

// GetMultiPlayerRank 批量获取玩家排名
func (m *Manager) GetMultiPlayerRank(ctx context.Context, rankType int32, playerIDs []uint64) ([]*RankInfo, error) {
	m.mutex.RLock()
	config, exists := m.rankConfigs[rankType]
	m.mutex.RUnlock()

	if !exists {
		return nil, fmt.Errorf("rank type %d not found", rankType)
	}

	// 使用缓存管理器的批量获取方法
	return m.cacheManager.BatchGetPlayerRanks(ctx, rankType, playerIDs,
		func(ctx context.Context, rankType int32, uncachedIDs []uint64) ([]*RankInfo, error) {
			// 批量获取未缓存的玩家排名
			return BatchGetPlayerRanks(ctx, rankType, uncachedIDs, config.IsRealtime)
		})
}

// ClearRank 清除排行榜
func (m *Manager) ClearRank(ctx context.Context, rankType int32) error {
	m.mutex.RLock()
	config, exists := m.rankConfigs[rankType]
	m.mutex.RUnlock()

	if !exists {
		return fmt.Errorf("rank type %d not found", rankType)
	}

	// 清除Redis中的排行榜数据
	err := clearRank(ctx, rankType, config.IsRealtime)
	if err != nil {
		return err
	}

	// 清除本地缓存
	m.cacheManager.ClearRankCache(rankType)

	return nil
}

// GetCacheStats 获取缓存统计信息
func (m *Manager) GetCacheStats() map[string]interface{} {
	return m.cacheManager.GetCacheStats()
}

// SetCacheExpireTime 设置缓存过期时间
func (m *Manager) SetCacheExpireTime(expireTime int64) {
	m.cacheManager.cacheExpireTime = expireTime
}

// SetHotCacheSize 设置热点缓存大小
func (m *Manager) SetHotCacheSize(size int32) {
	m.cacheManager.hotCacheSize = size
}

// SetMinScoreLimit 设置最低分数限制，每次有上榜更新，都需要更新这个值
func (m *Manager) SetMinScoreLimit(rankType int32, minScoreLimit int32) {
	// 这里需要加锁，避免竞态条件导致数据不一致
	m.mutex.Lock()
	defer m.mutex.Unlock()

	config, exists := m.rankConfigs[rankType]
	if !exists {
		return
	}
	if config.MinScoreLimit != minScoreLimit {
		config.MinScoreLimit = minScoreLimit
		// TODO 下发至游戏服，用于游戏服拦截更新榜单请求
	}
}
