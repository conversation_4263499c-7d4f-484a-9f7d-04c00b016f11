package rank

import (
	"kairo_paradise_server/internal/logger"
	"kairo_paradise_server/services/pb"

	"go.uber.org/zap"
)

// ExampleRankConfigs 示例排行榜配置
// 这个文件展示了如何配置不同类型的排行榜
func ExampleRankConfigs() {
	logger.Info("Rank configuration examples")

	// 示例配置数据（对应您提供的Excel表格）
	exampleConfigs := []struct {
		ID             int32
		Desc           string
		MinValueLimit  int32
		MaxQueryLimit  int32
		ShowRankLimit  int32
		MaxRankLimit   int32
		IsRealtime     bool
		UpdateInterval int64
	}{
		{
			ID:             2,
			Desc:           "繁荣榜",
			MinValueLimit:  100,
			MaxQueryLimit:  500,
			ShowRankLimit:  300,
			MaxRankLimit:   1000,
			IsRealtime:     true,  // 实时排行榜
			UpdateInterval: 0,     // 实时更新
		},
		{
			ID:             3,
			Desc:           "充值榜",
			MinValueLimit:  10,
			MaxQueryLimit:  500,
			ShowRankLimit:  300,
			MaxRankLimit:   1000,
			IsRealtime:     false, // 定时排行榜
			UpdateInterval: 300,   // 5分钟更新一次
		},
	}

	// 打印配置信息
	for _, config := range exampleConfigs {
		logger.Info("Rank config example",
			zap.Int32("id", config.ID),
			zap.String("desc", config.Desc),
			zap.Int32("minValueLimit", config.MinValueLimit),
			zap.Int32("maxQueryLimit", config.MaxQueryLimit),
			zap.Int32("showRankLimit", config.ShowRankLimit),
			zap.Int32("maxRankLimit", config.MaxRankLimit),
			zap.Bool("isRealtime", config.IsRealtime),
			zap.Int64("updateInterval", config.UpdateInterval))
	}
}

// CreateExampleListRankConfig 创建示例ListRankConfig
func CreateExampleListRankConfig() *pb.ListRankConfig {
	return &pb.ListRankConfig{
		RankConfig: []*pb.RankConfig{
			{
				Id:             2,
				MinValueLimit:  100,
				MaxQueryLimit:  500,
				ShowRankLimit:  300,
				MaxRankLimit:   1000,
			},
			{
				Id:             3,
				MinValueLimit:  10,
				MaxQueryLimit:  500,
				ShowRankLimit:  300,
				MaxRankLimit:   1000,
			},
		},
	}
}

// CreateExampleListWeeklyRankConfig 创建示例ListWeeklyRankConfig
func CreateExampleListWeeklyRankConfig() *pb.ListWeeklyRankConfig {
	return &pb.ListWeeklyRankConfig{
		WeeklyRankConfig: []*pb.WeeklyRankConfig{
			{
				RankType:     pb.ERankType_RANK_TYPE_PROSPERITY, // 繁荣榜
				RankInterval: 0,                                 // 实时更新
				MaxRankCount: 1000,
			},
			{
				RankType:     pb.ERankType_RANK_TYPE_RECHARGE, // 充值榜
				RankInterval: 300,                             // 5分钟更新
				MaxRankCount: 1000,
			},
		},
	}
}

// PerformanceOptimizationTips 性能优化建议
func PerformanceOptimizationTips() {
	tips := []string{
		"1. 使用本地缓存减少Redis访问频率",
		"2. 实时排行榜缓存前100名热点数据",
		"3. 定时排行榜缓存前3000名数据",
		"4. 使用批量操作减少网络往返",
		"5. 设置合理的缓存过期时间（建议5分钟）",
		"6. 对于高频查询的玩家排名进行缓存",
		"7. 使用Redis Pipeline批量操作",
		"8. 定期清理过期缓存",
		"9. 监控缓存命中率",
		"10. 根据业务需求调整热点缓存大小",
	}

	logger.Info("Rank system performance optimization tips:")
	for i, tip := range tips {
		logger.Info("Tip", zap.Int("number", i+1), zap.String("content", tip))
	}
}

// CacheStrategyExplanation 缓存策略说明
func CacheStrategyExplanation() {
	strategies := map[string]string{
		"热点缓存": "缓存排行榜前N名数据，适用于频繁查询的头部排名",
		"玩家缓存": "缓存单个玩家的排名信息，减少重复查询",
		"批量优化": "使用Redis Pipeline批量获取数据，减少网络延迟",
		"过期策略": "设置合理的缓存过期时间，平衡数据一致性和性能",
		"分层缓存": "本地缓存 + Redis缓存的两级缓存架构",
		"异步更新": "后台异步更新缓存，避免阻塞用户请求",
	}

	logger.Info("Cache strategy explanation:")
	for strategy, explanation := range strategies {
		logger.Info("Strategy", 
			zap.String("name", strategy), 
			zap.String("explanation", explanation))
	}
}

// BestPractices 最佳实践
func BestPractices() {
	practices := []struct {
		Category string
		Practice string
	}{
		{"配置管理", "根据排行榜类型设置不同的缓存策略"},
		{"性能监控", "定期监控缓存命中率和QPS"},
		{"容量规划", "根据玩家数量合理设置MaxRankLimit"},
		{"数据一致性", "实时排行榜使用较短的缓存时间"},
		{"错误处理", "缓存失败时优雅降级到Redis查询"},
		{"资源管理", "定期清理过期缓存释放内存"},
		{"并发控制", "使用读写锁保护缓存操作"},
		{"批量操作", "优先使用批量接口减少网络开销"},
	}

	logger.Info("Rank system best practices:")
	for _, practice := range practices {
		logger.Info("Best practice",
			zap.String("category", practice.Category),
			zap.String("practice", practice.Practice))
	}
}

// ConfigurationGuide 配置指南
func ConfigurationGuide() {
	guide := map[string]interface{}{
		"MinValueLimit": map[string]interface{}{
			"description": "最低上榜分数限制",
			"example":     "繁荣榜设置为100，充值榜设置为10",
			"impact":      "过低会导致排行榜数据过多，过高会导致上榜人数过少",
		},
		"MaxQueryLimit": map[string]interface{}{
			"description": "单次查询最大返回数量",
			"example":     "建议设置为500",
			"impact":      "限制客户端单次请求的数据量，防止大量数据传输",
		},
		"ShowRankLimit": map[string]interface{}{
			"description": "排行榜显示的最大数量",
			"example":     "建议设置为300",
			"impact":      "影响排行榜UI显示的数据量",
		},
		"MaxRankLimit": map[string]interface{}{
			"description": "排行榜存储的最大数量",
			"example":     "实时榜1000，定时榜3000",
			"impact":      "影响Redis存储空间和查询性能",
		},
		"CacheExpireTime": map[string]interface{}{
			"description": "缓存过期时间（秒）",
			"example":     "建议设置为300（5分钟）",
			"impact":      "影响数据一致性和缓存命中率",
		},
		"HotCacheSize": map[string]interface{}{
			"description": "热点缓存大小",
			"example":     "建议设置为100",
			"impact":      "影响内存使用和缓存命中率",
		},
	}

	logger.Info("Configuration guide:")
	for key, value := range guide {
		logger.Info("Configuration item",
			zap.String("key", key),
			zap.Any("details", value))
	}
}
