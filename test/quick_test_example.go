package test

import (
	"context"
	"kairo_paradise_server/internal/bootstrap"
	"kairo_paradise_server/internal/logger"
	"kairo_paradise_server/services/public"
	"kairo_paradise_server/services/public/internal/module/rank"
	"testing"
	"time"

	"go.uber.org/zap"
)

// TestQuickPerformanceDemo 快速性能演示测试
func TestQuickPerformanceDemo(t *testing.T) {
	// 初始化测试环�?	bootstrap.InitRedis()

	manager := public.GetRankManager()
	ctx := context.Background()
	rankType := int32(2) // 繁荣�?
	logger.Info("Starting quick performance demo")

	// 清空排行�?	err := manager.ClearRank(ctx, rankType)
	if err != nil {
		t.Fatalf("Failed to clear rank: %v", err)
	}

	// 1. 插入性能测试
	t.Log("=== 1. 插入性能测试 ===")
	mockGen := NewMockDataGenerator()
	insertCount := 100

	start := time.Now()
	for i := 0; i < insertCount; i++ {
		mockEntry := mockGen.GenerateRankEntry()
		entry := mockEntry.ConvertToRankEntry()
		_, err := manager.UpdateRankEntry(ctx, rankType, entry)
		if err != nil {
			t.Errorf("Insert failed: %v", err)
		}
	}
	insertDuration := time.Since(start)
	insertQPS := float64(insertCount) / insertDuration.Seconds()

	t.Logf("插入 %d 条记�?, insertCount)
	t.Logf("总耗时: %v", insertDuration)
	t.Logf("插入QPS: %.2f", insertQPS)
	t.Logf("平均延迟: %v", insertDuration/time.Duration(insertCount))

	// 2. 查询性能测试（冷缓存�?	t.Log("\n=== 2. 查询性能测试（冷缓存�?==")
	queryCount := 50

	start = time.Now()
	for i := 0; i < queryCount; i++ {
		_, _, err := manager.GetRankList(ctx, rankType, 1, 20)
		if err != nil {
			t.Errorf("Query failed: %v", err)
		}
	}
	coldQueryDuration := time.Since(start)
	coldQueryQPS := float64(queryCount) / coldQueryDuration.Seconds()

	t.Logf("冷缓存查�?%d �?, queryCount)
	t.Logf("总耗时: %v", coldQueryDuration)
	t.Logf("查询QPS: %.2f", coldQueryQPS)
	t.Logf("平均延迟: %v", coldQueryDuration/time.Duration(queryCount))

	// 3. 查询性能测试（热缓存�?	t.Log("\n=== 3. 查询性能测试（热缓存�?==")

	start = time.Now()
	for i := 0; i < queryCount; i++ {
		_, _, err := manager.GetRankList(ctx, rankType, 1, 20)
		if err != nil {
			t.Errorf("Query failed: %v", err)
		}
	}
	hotQueryDuration := time.Since(start)
	hotQueryQPS := float64(queryCount) / hotQueryDuration.Seconds()

	t.Logf("热缓存查�?%d �?, queryCount)
	t.Logf("总耗时: %v", hotQueryDuration)
	t.Logf("查询QPS: %.2f", hotQueryQPS)
	t.Logf("平均延迟: %v", hotQueryDuration/time.Duration(queryCount))

	// 4. 缓存效果分析
	t.Log("\n=== 4. 缓存效果分析 ===")
	if hotQueryDuration < coldQueryDuration {
		speedup := float64(coldQueryDuration) / float64(hotQueryDuration)
		t.Logf("缓存加速比: %.2fx", speedup)
		t.Logf("性能提升: %.1f%%", (speedup-1)*100)
	} else {
		t.Log("警告: 热缓存性能未优于冷缓存")
	}

	// 5. 批量操作测试
	t.Log("\n=== 5. 批量操作测试 ===")
	playerIDs := make([]uint64, 20)
	for i := range playerIDs {
		playerIDs[i] = uint64(100000 + i)
	}

	// 单个查询
	start = time.Now()
	for _, playerID := range playerIDs {
		_, err := manager.GetPlayerRank(ctx, rankType, playerID)
		if err != nil {
			t.Errorf("Single query failed: %v", err)
		}
	}
	singleDuration := time.Since(start)

	// 批量查询
	start = time.Now()
	_, err = manager.GetMultiPlayerRank(ctx, rankType, playerIDs)
	if err != nil {
		t.Errorf("Batch query failed: %v", err)
	}
	batchDuration := time.Since(start)

	t.Logf("单个查询 %d 次耗时: %v", len(playerIDs), singleDuration)
	t.Logf("批量查询 %d 次耗时: %v", len(playerIDs), batchDuration)

	if batchDuration < singleDuration {
		speedup := float64(singleDuration) / float64(batchDuration)
		t.Logf("批量查询加速比: %.2fx", speedup)
	}

	// 6. 获取排行榜状�?	t.Log("\n=== 6. 排行榜状�?===")
	entries, total, err := manager.GetRankList(ctx, rankType, 1, 10)
	if err != nil {
		t.Errorf("Failed to get rank list: %v", err)
	} else {
		t.Logf("排行榜总人�? %d", total)
		t.Logf("�?0�?")
		for i, entry := range entries {
			t.Logf("  %d. 玩家%d: %d繁荣�?, i+1, entry.PlayerID, entry.Prosperity)
		}
	}

	// 7. 缓存统计
	t.Log("\n=== 7. 缓存统计 ===")
	cacheStats := manager.GetCacheStats()
	t.Logf("缓存统计: %+v", cacheStats)

	// 8. 性能总结
	t.Log("\n=== 8. 性能总结 ===")
	t.Logf("插入性能: %.2f QPS", insertQPS)
	t.Logf("冷缓存查�? %.2f QPS", coldQueryQPS)
	t.Logf("热缓存查�? %.2f QPS", hotQueryQPS)

	// 性能评估
	if insertQPS >= 200 {
		t.Log("�?插入性能: 优秀")
	} else if insertQPS >= 100 {
		t.Log("⚠️  插入性能: 良好")
	} else {
		t.Log("�?插入性能: 需要优�?)
	}

	if hotQueryQPS >= 1000 {
		t.Log("�?查询性能: 优秀")
	} else if hotQueryQPS >= 500 {
		t.Log("⚠️  查询性能: 良好")
	} else {
		t.Log("�?查询性能: 需要优�?)
	}

	logger.Info("Quick performance demo completed",
		zap.Float64("insert_qps", insertQPS),
		zap.Float64("cold_query_qps", coldQueryQPS),
		zap.Float64("hot_query_qps", hotQueryQPS),
		zap.Int64("total_rank_count", total),
		zap.Any("cache_stats", cacheStats))
}

// TestSimpleInsertDemo 简单插入演�?func TestSimpleInsertDemo(t *testing.T) {
	// 初始化测试环�?	bootstrap.InitRedis()

	manager := public.GetRankManager()
	ctx := context.Background()
	rankType := int32(2)

	t.Log("=== 简单插入演�?===")

	// 清空排行�?	manager.ClearRank(ctx, rankType)

	// 创建测试数据
	testPlayers := []struct {
		playerID   uint64
		prosperity int32
		playerName string
	}{
		{100001, 95000, "超级玩家"},
		{100002, 85000, "高级玩家"},
		{100003, 75000, "中级玩家"},
		{100004, 65000, "普通玩�?},
		{100005, 55000, "新手玩家"},
	}

	// 插入测试数据
	for _, player := range testPlayers {
		entry := &rank.RankEntry{
			PlayerID:   player.playerID,
			Prosperity: player.prosperity,
			PlayerName: player.playerName,
			Level:      50,
			UpdateTime: time.Now().Unix(),
		}

		rank, err := manager.UpdateRankEntry(ctx, rankType, entry)
		if err != nil {
			t.Errorf("插入失败: %v", err)
		} else {
			t.Logf("插入成功: %s (ID:%d) 繁荣�?%d 排名:%d",
				player.playerName, player.playerID, player.prosperity, rank)
		}
	}

	// 查看排行�?	t.Log("\n当前排行�?")
	entries, total, err := manager.GetRankList(ctx, rankType, 1, 10)
	if err != nil {
		t.Errorf("获取排行榜失�? %v", err)
	} else {
		t.Logf("总人�? %d", total)
		for i, entry := range entries {
			t.Logf("%d. %s: %d繁荣�?, i+1, entry.PlayerName, entry.Prosperity)
		}
	}

	// 查询单个玩家排名
	t.Log("\n查询单个玩家排名:")
	for _, player := range testPlayers[:3] {
		rankInfo, err := manager.GetPlayerRank(ctx, rankType, player.playerID)
		if err != nil {
			t.Errorf("查询失败: %v", err)
		} else {
			if rankInfo.Rank > 0 {
				t.Logf("%s: �?d�?, player.playerName, rankInfo.Rank)
			} else {
				t.Logf("%s: 未上�?, player.playerName)
			}
		}
	}
}

// BenchmarkQuickInsert 快速插入基准测�?func BenchmarkQuickInsert(b *testing.B) {
	bootstrap.InitRedis()

	manager := public.GetRankManager()
	ctx := context.Background()
	rankType := int32(2)

	mockGen := NewMockDataGenerator()

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		mockEntry := mockGen.GenerateRankEntry()
		entry := mockEntry.ConvertToRankEntry()

		_, err := manager.UpdateRankEntry(ctx, rankType, entry)
		if err != nil {
			b.Errorf("Insert failed: %v", err)
		}
	}
}

// BenchmarkQuickQuery 快速查询基准测�?func BenchmarkQuickQuery(b *testing.B) {
	bootstrap.InitRedis()

	manager := public.GetRankManager()
	ctx := context.Background()
	rankType := int32(2)

	// 预热缓存
	manager.GetRankList(ctx, rankType, 1, 50)

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		_, _, err := manager.GetRankList(ctx, rankType, 1, 20)
		if err != nil {
			b.Errorf("Query failed: %v", err)
		}
	}
}
