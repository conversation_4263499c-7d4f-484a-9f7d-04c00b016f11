package rank

import (
	"context"
	"fmt"
	"kairo_paradise_server/internal/logger"
	"time"

	"go.uber.org/zap"
)

// ExampleCacheConfiguration 展示如何配置独立的缓存管理器
func ExampleCacheConfiguration() {
	logger.Info("Cache configuration examples")

	// 获取排行榜管理器
	manager := GetRankManager()

	// 示例1：为不同排行榜设置不同的缓存配置
	logger.Info("=== 示例1：为不同排行榜设置独立缓存配置 ===")

	// 繁荣榜（实时排行榜）- 需要更快的更新频率
	prosperityRankType := int32(2)
	manager.SetCacheExpireTime(prosperityRankType, 60)  // 1分钟过期
	manager.SetHotCacheSize(prosperityRankType, 100)    // 缓存前100名

	// 充值榜（定时排行榜）- 可以使用较长的缓存时间
	rechargeRankType := int32(3)
	manager.SetCacheExpireTime(rechargeRankType, 300)   // 5分钟过期
	manager.SetHotCacheSize(rechargeRankType, 300)      // 缓存前300名

	logger.Info("Individual cache configuration completed",
		zap.Int32("prosperityRankType", prosperityRankType),
		zap.Int32("rechargeRankType", rechargeRankType))

	// 示例2：为所有排行榜设置统一配置
	logger.Info("=== 示例2：为所有排行榜设置统一配置 ===")

	// 如果需要为所有排行榜设置相同的配置
	manager.SetAllCacheExpireTime(180) // 3分钟过期
	manager.SetAllHotCacheSize(150)    // 缓存前150名

	logger.Info("Global cache configuration completed")

	// 示例3：获取和监控缓存统计信息
	logger.Info("=== 示例3：获取缓存统计信息 ===")

	stats := manager.GetCacheStats()
	for rankKey, rankStats := range stats {
		logger.Info("Rank cache statistics",
			zap.String("rankKey", rankKey),
			zap.Any("stats", rankStats))
	}
}

// ExampleCacheUsage 展示缓存的实际使用效果
func ExampleCacheUsage() {
	logger.Info("Cache usage examples")

	manager := GetRankManager()
	ctx := context.Background()
	rankType := int32(2) // 繁荣榜

	// 配置缓存
	manager.SetCacheExpireTime(rankType, 60)  // 1分钟过期
	manager.SetHotCacheSize(rankType, 50)     // 缓存前50名

	logger.Info("=== 缓存使用示例 ===")

	// 第一次获取排行榜（会从Redis获取并缓存）
	start := time.Now()
	entries1, total1, err := manager.GetRankList(ctx, rankType, 1, 10)
	duration1 := time.Since(start)

	if err != nil {
		logger.Error("Failed to get rank list", zap.Error(err))
		return
	}

	logger.Info("First call (cache miss)",
		zap.Duration("duration", duration1),
		zap.Int("entryCount", len(entries1)),
		zap.Int64("total", total1))

	// 第二次获取排行榜（应该从缓存获取）
	start = time.Now()
	entries2, total2, err := manager.GetRankList(ctx, rankType, 1, 10)
	duration2 := time.Since(start)

	if err != nil {
		logger.Error("Failed to get rank list", zap.Error(err))
		return
	}

	logger.Info("Second call (cache hit)",
		zap.Duration("duration", duration2),
		zap.Int("entryCount", len(entries2)),
		zap.Int64("total", total2),
		zap.Float64("speedup", float64(duration1.Nanoseconds())/float64(duration2.Nanoseconds())))

	// 获取缓存统计
	stats := manager.GetCacheStats()
	if rankStats, exists := stats[fmt.Sprintf("rank_%d", rankType)]; exists {
		logger.Info("Cache statistics after usage",
			zap.Int32("rankType", rankType),
			zap.Any("stats", rankStats))
	}
}

// ExampleDifferentRankTypeConfigs 展示不同排行榜类型的推荐配置
func ExampleDifferentRankTypeConfigs() {
	logger.Info("Different rank type configuration examples")

	manager := GetRankManager()

	// 配置建议
	configs := []struct {
		RankType        int32
		Name            string
		CacheExpireTime int64
		HotCacheSize    int32
		Reason          string
	}{
		{
			RankType:        2,
			Name:            "繁荣榜",
			CacheExpireTime: 60,  // 1分钟
			HotCacheSize:    100, // 前100名
			Reason:          "实时排行榜，需要快速更新",
		},
		{
			RankType:        3,
			Name:            "充值榜",
			CacheExpireTime: 300, // 5分钟
			HotCacheSize:    300, // 前300名
			Reason:          "定时排行榜，可以使用较长缓存时间",
		},
		{
			RankType:        4,
			Name:            "等级榜",
			CacheExpireTime: 600, // 10分钟
			HotCacheSize:    200, // 前200名
			Reason:          "等级变化较慢，可以使用更长缓存时间",
		},
		{
			RankType:        5,
			Name:            "战力榜",
			CacheExpireTime: 120, // 2分钟
			HotCacheSize:    150, // 前150名
			Reason:          "战力变化中等频率，平衡实时性和性能",
		},
	}

	logger.Info("=== 不同排行榜类型的推荐配置 ===")

	for _, config := range configs {
		manager.SetCacheExpireTime(config.RankType, config.CacheExpireTime)
		manager.SetHotCacheSize(config.RankType, config.HotCacheSize)

		logger.Info("Configured rank type",
			zap.Int32("rankType", config.RankType),
			zap.String("name", config.Name),
			zap.Int64("cacheExpireTime", config.CacheExpireTime),
			zap.Int32("hotCacheSize", config.HotCacheSize),
			zap.String("reason", config.Reason))
	}

	// 显示最终配置统计
	stats := manager.GetCacheStats()
	logger.Info("Final cache configuration summary",
		zap.Int("totalRankTypes", len(stats)))

	for rankKey, rankStats := range stats {
		logger.Info("Rank configuration",
			zap.String("rankKey", rankKey),
			zap.Any("config", rankStats))
	}
}

// ExampleCacheMonitoring 展示如何监控缓存性能
func ExampleCacheMonitoring() {
	logger.Info("Cache monitoring examples")

	manager := GetRankManager()

	// 定期监控缓存统计
	ticker := time.NewTicker(30 * time.Second)
	defer ticker.Stop()

	logger.Info("=== 缓存监控示例（运行30秒） ===")

	// 模拟监控
	for i := 0; i < 3; i++ {
		stats := manager.GetCacheStats()

		logger.Info("Cache monitoring report",
			zap.Int("reportNumber", i+1),
			zap.Time("timestamp", time.Now()))

		for rankKey, rankStats := range stats {
			if statsMap, ok := rankStats.(map[string]interface{}); ok {
				logger.Info("Rank cache status",
					zap.String("rankKey", rankKey),
					zap.Any("hotRankCacheCount", statsMap["hot_rank_cache_count"]),
					zap.Any("playerRankCacheCount", statsMap["player_rank_cache_count"]),
					zap.Any("hotCacheSize", statsMap["hot_cache_size"]),
					zap.Any("cacheExpireTime", statsMap["cache_expire_time"]))
			}
		}

		if i < 2 {
			time.Sleep(10 * time.Second)
		}
	}

	logger.Info("Cache monitoring completed")
}

// ExampleCacheConfigurationBestPractices 展示缓存配置最佳实践
func ExampleCacheConfigurationBestPractices() {
	logger.Info("Cache configuration best practices")

	manager := GetRankManager()

	logger.Info("=== 缓存配置最佳实践 ===")

	// 最佳实践1：根据访问频率配置缓存大小
	logger.Info("Best Practice 1: Configure cache size based on access frequency")

	// 高频访问的排行榜
	highFrequencyRanks := []int32{2, 3} // 繁荣榜、充值榜
	for _, rankType := range highFrequencyRanks {
		manager.SetHotCacheSize(rankType, 200) // 较大的缓存
		logger.Info("High frequency rank configured",
			zap.Int32("rankType", rankType),
			zap.Int32("hotCacheSize", 200))
	}

	// 低频访问的排行榜
	lowFrequencyRanks := []int32{4, 5} // 等级榜、战力榜
	for _, rankType := range lowFrequencyRanks {
		manager.SetHotCacheSize(rankType, 50) // 较小的缓存
		logger.Info("Low frequency rank configured",
			zap.Int32("rankType", rankType),
			zap.Int32("hotCacheSize", 50))
	}

	// 最佳实践2：根据数据更新频率配置过期时间
	logger.Info("Best Practice 2: Configure expiration based on update frequency")

	// 快速更新的数据
	fastUpdateRanks := []int32{2} // 繁荣榜
	for _, rankType := range fastUpdateRanks {
		manager.SetCacheExpireTime(rankType, 30) // 30秒过期
		logger.Info("Fast update rank configured",
			zap.Int32("rankType", rankType),
			zap.Int64("cacheExpireTime", 30))
	}

	// 慢速更新的数据
	slowUpdateRanks := []int32{4} // 等级榜
	for _, rankType := range slowUpdateRanks {
		manager.SetCacheExpireTime(rankType, 600) // 10分钟过期
		logger.Info("Slow update rank configured",
			zap.Int32("rankType", rankType),
			zap.Int64("cacheExpireTime", 600))
	}

	logger.Info("Cache configuration best practices applied")
}
