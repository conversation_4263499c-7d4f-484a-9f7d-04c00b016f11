#!/bin/bash

# 排行榜性能测试运行脚本
# 使用方法: ./run_rank_performance_tests.sh [test_type]
# test_type: all, benchmark, stress, analysis

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查Go环境
check_go_env() {
    if ! command -v go &> /dev/null; then
        log_error "Go is not installed or not in PATH"
        exit 1
    fi
    
    log_info "Go version: $(go version)"
}

# 检查Redis连接
check_redis() {
    log_info "Checking Redis connection..."
    # 这里可以添加Redis连接检查逻辑
    # 暂时跳过，假设Redis已经运行
    log_success "Redis connection check passed"
}

# 运行基准测试
run_benchmarks() {
    log_info "Running benchmark tests..."
    
    echo "=== Benchmark Tests ==="
    
    # 单个操作基准测试
    log_info "Running single operation benchmarks..."
    go test -bench=BenchmarkUpdateRankEntry -benchmem -count=3 ./test/
    go test -bench=BenchmarkGetRankList -benchmem -count=3 ./test/
    go test -bench=BenchmarkGetPlayerRank -benchmem -count=3 ./test/
    go test -bench=BenchmarkGetMultiPlayerRank -benchmem -count=3 ./test/
    
    # 批量操作基准测试
    log_info "Running batch operation benchmarks..."
    go test -bench=BenchmarkBatchUpdateRankEntries -benchmem -count=3 ./test/
    
    # 并发操作基准测试
    log_info "Running concurrent operation benchmarks..."
    go test -bench=BenchmarkConcurrentUpdateRankEntry -benchmem -count=3 ./test/
    
    log_success "Benchmark tests completed"
}

# 运行压力测试
run_stress_tests() {
    log_info "Running stress tests..."
    
    echo "=== Stress Tests ==="
    
    # 插入压力测试
    log_info "Running insert stress test..."
    go test -v -timeout=10m -run=TestInsertStressTest ./test/
    
    # 混合工作负载压力测试
    log_info "Running mixed workload stress test..."
    go test -v -timeout=10m -run=TestMixedWorkloadStressTest ./test/
    
    log_success "Stress tests completed"
}

# 运行性能分析测试
run_analysis_tests() {
    log_info "Running performance analysis tests..."
    
    echo "=== Performance Analysis Tests ==="
    
    # 性能分析测试
    log_info "Running performance analysis..."
    go test -v -run=TestPerformanceAnalysis ./test/
    
    # 缓存效果分析测试
    log_info "Running cache effectiveness analysis..."
    go test -v -run=TestCacheEffectivenessAnalysis ./test/
    
    # 缓存性能测试
    log_info "Running cache performance test..."
    go test -v -run=TestCachePerformance ./test/
    
    # 批量vs单个请求对比测试
    log_info "Running batch vs single requests test..."
    go test -v -run=TestBatchVsSingleRequests ./test/
    
    # 缓存过期测试
    log_info "Running cache expiration test..."
    go test -v -run=TestCacheExpiration ./test/
    
    log_success "Performance analysis tests completed"
}

# 运行功能测试
run_functional_tests() {
    log_info "Running functional tests..."
    
    echo "=== Functional Tests ==="
    
    # 综合性能测试套件
    log_info "Running performance test suite..."
    go test -v -run=TestPerformanceTestSuite ./test/
    
    log_success "Functional tests completed"
}

# 生成性能报告
generate_report() {
    log_info "Generating performance report..."
    
    REPORT_FILE="rank_performance_report_$(date +%Y%m%d_%H%M%S).txt"
    
    {
        echo "========================================"
        echo "排行榜系统性能测试报告"
        echo "生成时间: $(date)"
        echo "========================================"
        echo ""
        
        echo "测试环境信息:"
        echo "- Go版本: $(go version)"
        echo "- 操作系统: $(uname -a)"
        echo "- CPU信息: $(grep 'model name' /proc/cpuinfo | head -1 | cut -d: -f2 | xargs || echo 'N/A')"
        echo "- 内存信息: $(free -h | grep '^Mem:' | awk '{print $2}' || echo 'N/A')"
        echo ""
        
        echo "测试配置:"
        echo "- 排行榜类型: 繁荣榜 (ID=2)"
        echo "- MinValueLimit: 100"
        echo "- MaxQueryLimit: 500"
        echo "- ShowRankLimit: 300"
        echo "- MaxRankLimit: 1000"
        echo "- 缓存过期时间: 5分钟"
        echo "- 热点缓存大小: 100"
        echo ""
        
        echo "性能优化特性:"
        echo "- 多层缓存架构 (热点缓存 + 玩家缓存)"
        echo "- Redis Pipeline批量操作"
        echo "- 智能缓存过期策略"
        echo "- 并发安全的缓存管理"
        echo "- 自动缓存清理机制"
        echo ""
        
        echo "测试结果将在上方显示..."
        echo "========================================"
    } > "$REPORT_FILE"
    
    log_success "Performance report generated: $REPORT_FILE"
}

# 清理测试数据
cleanup() {
    log_info "Cleaning up test data..."
    # 这里可以添加清理Redis测试数据的逻辑
    log_success "Cleanup completed"
}

# 主函数
main() {
    local test_type=${1:-"all"}
    
    log_info "Starting rank performance tests..."
    log_info "Test type: $test_type"
    
    # 环境检查
    check_go_env
    check_redis
    
    # 根据参数运行不同类型的测试
    case $test_type in
        "benchmark")
            run_benchmarks
            ;;
        "stress")
            run_stress_tests
            ;;
        "analysis")
            run_analysis_tests
            ;;
        "functional")
            run_functional_tests
            ;;
        "all")
            run_benchmarks
            echo ""
            run_stress_tests
            echo ""
            run_analysis_tests
            echo ""
            run_functional_tests
            ;;
        *)
            log_error "Unknown test type: $test_type"
            echo "Usage: $0 [benchmark|stress|analysis|functional|all]"
            exit 1
            ;;
    esac
    
    # 生成报告
    generate_report
    
    # 清理
    cleanup
    
    log_success "All tests completed successfully!"
    
    echo ""
    echo "=== 测试总结 ==="
    echo "1. 基准测试: 测试各个操作的基础性能指标"
    echo "2. 压力测试: 测试高并发场景下的系统表现"
    echo "3. 性能分析: 分析缓存效果和性能优化效果"
    echo "4. 功能测试: 验证系统功能的正确性"
    echo ""
    echo "关键性能指标:"
    echo "- QPS (每秒查询数): 衡量系统吞吐量"
    echo "- 延迟 (Latency): 衡量响应时间"
    echo "- 缓存命中率: 衡量缓存效果"
    echo "- 错误率: 衡量系统稳定性"
    echo ""
    echo "优化建议:"
    echo "- 监控缓存命中率，目标 >80%"
    echo "- 关注平均延迟，目标 <10ms"
    echo "- 观察错误率，目标 <1%"
    echo "- 根据实际负载调整缓存参数"
}

# 脚本入口
main "$@"
