package game_config

import (
	"kairo_paradise_server/internal/consts"
	"kairo_paradise_server/internal/logger"
	"kairo_paradise_server/services/pb"
	"path/filepath"
)

// auto generated by tools, do not edit it manually
// X小游戏表.xlsx MiniGameConfig

type miniGameConfig struct {
	data map[int32]*pb.ListMiniGameConfig_MiniGameConfig
	ids  []int32
}

var miniGameConfigFunc = func() *pb.ListMiniGameConfig { return &pb.ListMiniGameConfig{} }

var MiniGameConfig *miniGameConfig

func newMiniGameConfig() *miniGameConfig {
	result := &miniGameConfig{
		data: make(map[int32]*pb.ListMiniGameConfig_MiniGameConfig),
		ids:  make([]int32, 0),
	}

	listConfig, err := loadProtoFile(filepath.Join(consts.GameConfigPath, "MiniGameConfig.bytes"), miniGameConfigFunc)
	if err != nil {
		logger.Errorf("load MiniGameConfig.bytes error: %v", err)
		return result
	}
	for _, item := range listConfig.List {
		if item.Id != nil {
			id := *item.Id
			result.data[id] = item
			result.ids = append(result.ids, id)
		}
	}
	return result
}

func (c *miniGameConfig) Item(id int32) *pb.ListMiniGameConfig_MiniGameConfig {
	return c.data[id]
}

func (c *miniGameConfig) Items() []*pb.ListMiniGameConfig_MiniGameConfig {
	items := make([]*pb.ListMiniGameConfig_MiniGameConfig, 0, len(c.data))
	for _, item := range c.data {
		items = append(items, item)
	}
	return items
}

func (c *miniGameConfig) GetIds() []int32 {
	return c.ids
}
