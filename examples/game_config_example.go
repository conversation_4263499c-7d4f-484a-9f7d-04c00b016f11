package main

import (
	"fmt"
	"github.com/golang/protobuf/proto"
	"kairo_paradise_server/internal/consts"
	"kairo_paradise_server/services/pb"
	"os"
	"path/filepath"
)

func main() {
	b := loadFile("ConstConfig.bytes")
	lis := &pb.ListConstConfig{}
	if err := proto.Unmarshal(b, lis); err != nil {
		panic(err)
	}
	fmt.Println("ConstConfig", lis.List)

	b = loadFile("RankConfig.bytes")
	listRank := &pb.ListRankConfig{}
	if err := proto.Unmarshal(b, listRank); err != nil {
		panic(err)
	}
	fmt.Println("RankConfig", listRank.List)

	b = loadFile("ItemConfig.bytes")
	listItem := &pb.ListItemConfig{}
	if err := proto.Unmarshal(b, listItem); err != nil {
		panic(err)
	}
	fmt.Println("ItemConfig", listItem.List)
}

func loadFile(filename string) []byte {
	b, err := os.ReadFile(filepath.Join(consts.GameConfigPath, filename))
	if err != nil {
		panic(err)
	}
	return b
}
