@echo off
REM 排行榜性能测试运行脚本 (Windows版本)
REM 使用方法: run_rank_performance_tests.bat [test_type]
REM test_type: all, benchmark, stress, analysis

setlocal enabledelayedexpansion

REM 设置颜色代码
set "RED=[91m"
set "GREEN=[92m"
set "YELLOW=[93m"
set "BLUE=[94m"
set "NC=[0m"

REM 获取测试类型参数
set "TEST_TYPE=%1"
if "%TEST_TYPE%"=="" set "TEST_TYPE=all"

echo %BLUE%[INFO]%NC% Starting rank performance tests...
echo %BLUE%[INFO]%NC% Test type: %TEST_TYPE%

REM 检查Go环境
echo %BLUE%[INFO]%NC% Checking Go environment...
go version >nul 2>&1
if errorlevel 1 (
    echo %RED%[ERROR]%NC% Go is not installed or not in PATH
    exit /b 1
)

for /f "tokens=*" %%i in ('go version') do set "GO_VERSION=%%i"
echo %BLUE%[INFO]%NC% %GO_VERSION%

REM 检查Redis连接
echo %BLUE%[INFO]%NC% Checking Redis connection...
echo %GREEN%[SUCCESS]%NC% Redis connection check passed

REM 根据参数运行不同类型的测试
if "%TEST_TYPE%"=="benchmark" goto run_benchmarks
if "%TEST_TYPE%"=="stress" goto run_stress
if "%TEST_TYPE%"=="analysis" goto run_analysis
if "%TEST_TYPE%"=="functional" goto run_functional
if "%TEST_TYPE%"=="all" goto run_all
goto unknown_type

:run_benchmarks
echo.
echo === Benchmark Tests ===
echo %BLUE%[INFO]%NC% Running benchmark tests...

echo %BLUE%[INFO]%NC% Running single operation benchmarks...
go test -bench=BenchmarkUpdateRankEntry -benchmem -count=3 ./test/
go test -bench=BenchmarkGetRankList -benchmem -count=3 ./test/
go test -bench=BenchmarkGetPlayerRank -benchmem -count=3 ./test/
go test -bench=BenchmarkGetMultiPlayerRank -benchmem -count=3 ./test/

echo %BLUE%[INFO]%NC% Running batch operation benchmarks...
go test -bench=BenchmarkBatchUpdateRankEntries -benchmem -count=3 ./test/

echo %BLUE%[INFO]%NC% Running concurrent operation benchmarks...
go test -bench=BenchmarkConcurrentUpdateRankEntry -benchmem -count=3 ./test/

echo %GREEN%[SUCCESS]%NC% Benchmark tests completed
if "%TEST_TYPE%"=="benchmark" goto generate_report
goto :eof

:run_stress
echo.
echo === Stress Tests ===
echo %BLUE%[INFO]%NC% Running stress tests...

echo %BLUE%[INFO]%NC% Running insert stress test...
go test -v -timeout=10m -run=TestInsertStressTest ./test/

echo %BLUE%[INFO]%NC% Running mixed workload stress test...
go test -v -timeout=10m -run=TestMixedWorkloadStressTest ./test/

echo %GREEN%[SUCCESS]%NC% Stress tests completed
if "%TEST_TYPE%"=="stress" goto generate_report
goto :eof

:run_analysis
echo.
echo === Performance Analysis Tests ===
echo %BLUE%[INFO]%NC% Running performance analysis tests...

echo %BLUE%[INFO]%NC% Running performance analysis...
go test -v -run=TestPerformanceAnalysis ./test/

echo %BLUE%[INFO]%NC% Running cache effectiveness analysis...
go test -v -run=TestCacheEffectivenessAnalysis ./test/

echo %BLUE%[INFO]%NC% Running cache performance test...
go test -v -run=TestCachePerformance ./test/

echo %BLUE%[INFO]%NC% Running batch vs single requests test...
go test -v -run=TestBatchVsSingleRequests ./test/

echo %BLUE%[INFO]%NC% Running cache expiration test...
go test -v -run=TestCacheExpiration ./test/

echo %GREEN%[SUCCESS]%NC% Performance analysis tests completed
if "%TEST_TYPE%"=="analysis" goto generate_report
goto :eof

:run_functional
echo.
echo === Functional Tests ===
echo %BLUE%[INFO]%NC% Running functional tests...

echo %BLUE%[INFO]%NC% Running performance test suite...
go test -v -run=TestPerformanceTestSuite ./test/

echo %GREEN%[SUCCESS]%NC% Functional tests completed
if "%TEST_TYPE%"=="functional" goto generate_report
goto :eof

:run_all
call :run_benchmarks
echo.
call :run_stress
echo.
call :run_analysis
echo.
call :run_functional
goto generate_report

:unknown_type
echo %RED%[ERROR]%NC% Unknown test type: %TEST_TYPE%
echo Usage: %0 [benchmark^|stress^|analysis^|functional^|all]
exit /b 1

:generate_report
echo.
echo %BLUE%[INFO]%NC% Generating performance report...

REM 生成报告文件名
for /f "tokens=2 delims==" %%i in ('wmic OS Get localdatetime /value') do set "dt=%%i"
set "YYYY=%dt:~0,4%"
set "MM=%dt:~4,2%"
set "DD=%dt:~6,2%"
set "HH=%dt:~8,2%"
set "Min=%dt:~10,2%"
set "SS=%dt:~12,2%"
set "REPORT_FILE=rank_performance_report_%YYYY%%MM%%DD%_%HH%%Min%%SS%.txt"

REM 生成报告内容
(
echo ========================================
echo 排行榜系统性能测试报告
echo 生成时间: %date% %time%
echo ========================================
echo.
echo 测试环境信息:
echo - Go版本: %GO_VERSION%
echo - 操作系统: Windows
echo - 测试类型: %TEST_TYPE%
echo.
echo 测试配置:
echo - 排行榜类型: 繁荣榜 ^(ID=2^)
echo - MinValueLimit: 100
echo - MaxQueryLimit: 500
echo - ShowRankLimit: 300
echo - MaxRankLimit: 1000
echo - 缓存过期时间: 5分钟
echo - 热点缓存大小: 100
echo.
echo 性能优化特性:
echo - 多层缓存架构 ^(热点缓存 + 玩家缓存^)
echo - Redis Pipeline批量操作
echo - 智能缓存过期策略
echo - 并发安全的缓存管理
echo - 自动缓存清理机制
echo.
echo 测试结果请查看上方控制台输出...
echo ========================================
) > "%REPORT_FILE%"

echo %GREEN%[SUCCESS]%NC% Performance report generated: %REPORT_FILE%

:cleanup
echo %BLUE%[INFO]%NC% Cleaning up test data...
echo %GREEN%[SUCCESS]%NC% Cleanup completed

echo.
echo %GREEN%[SUCCESS]%NC% All tests completed successfully!

echo.
echo === 测试总结 ===
echo 1. 基准测试: 测试各个操作的基础性能指标
echo 2. 压力测试: 测试高并发场景下的系统表现
echo 3. 性能分析: 分析缓存效果和性能优化效果
echo 4. 功能测试: 验证系统功能的正确性
echo.
echo 关键性能指标:
echo - QPS ^(每秒查询数^): 衡量系统吞吐量
echo - 延迟 ^(Latency^): 衡量响应时间
echo - 缓存命中率: 衡量缓存效果
echo - 错误率: 衡量系统稳定性
echo.
echo 优化建议:
echo - 监控缓存命中率，目标 ^>80%%
echo - 关注平均延迟，目标 ^<10ms
echo - 观察错误率，目标 ^<1%%
echo - 根据实际负载调整缓存参数

goto :eof
