package test

import (
	"context"
	"fmt"
	"kairo_paradise_server/internal/bootstrap"
	"kairo_paradise_server/internal/logger"
	"kairo_paradise_server/services/public/rank"
	"math/rand"
	"sync"
	"sync/atomic"
	"testing"
	"time"

	"go.uber.org/zap"
)

// MockRankEntry Mock rank entry data
type MockRankEntry struct {
	PlayerID   uint64
	Prosperity int32
	PlayerName string
	Level      int32
	VipLevel   int32
	UpdateTime int64
}

// MockDataGenerator Mock data generator
type MockDataGenerator struct {
	playerIDCounter uint64
	rand            *rand.Rand
}

// NewMockDataGenerator creates a new mock data generator
func NewMockDataGenerator() *MockDataGenerator {
	return &MockDataGenerator{
		playerIDCounter: 100000,
		rand:            rand.New(rand.NewSource(time.Now().UnixNano())),
	}
}

// GenerateRankEntry generates a single rank entry
func (m *MockDataGenerator) GenerateRankEntry() *MockRankEntry {
	playerID := atomic.AddUint64(&m.playerIDCounter, 1)
	return &MockRankEntry{
		PlayerID:   playerID,
		Prosperity: m.rand.Int31n(100000) + 1000, // 1000-101000 prosperity
		PlayerName: fmt.Sprintf("Player_%d", playerID),
		Level:      m.rand.Int31n(100) + 1, // level 1-100
		VipLevel:   m.rand.Int31n(15),      // VIP level 0-14
		UpdateTime: time.Now().Unix(),
	}
}

// GenerateBatchRankEntries generates batch rank entries
func (m *MockDataGenerator) GenerateBatchRankEntries(count int) []*MockRankEntry {
	entries := make([]*MockRankEntry, count)
	for i := 0; i < count; i++ {
		entries[i] = m.GenerateRankEntry()
	}
	return entries
}

// GenerateRankEntriesWithDistribution generates rank entries with specific distribution
func (m *MockDataGenerator) GenerateRankEntriesWithDistribution(count int) []*MockRankEntry {
	entries := make([]*MockRankEntry, count)

	for i := 0; i < count; i++ {
		playerID := atomic.AddUint64(&m.playerIDCounter, 1)
		var prosperity int32

		// Simulate real score distribution: few high scores, many low-medium scores
		if i < count/10 { // Top 10% high score players
			prosperity = m.rand.Int31n(50000) + 50000 // 50000-100000
		} else if i < count/3 { // Top 33% medium-high score players
			prosperity = m.rand.Int31n(30000) + 20000 // 20000-50000
		} else { // Other players
			prosperity = m.rand.Int31n(19000) + 1000 // 1000-20000
		}

		entries[i] = &MockRankEntry{
			PlayerID:   playerID,
			Prosperity: prosperity,
			PlayerName: fmt.Sprintf("Player_%d", playerID),
			Level:      m.rand.Int31n(100) + 1,
			VipLevel:   m.rand.Int31n(15),
			UpdateTime: time.Now().Unix(),
		}
	}

	return entries
}

// ConvertToRankEntry converts to system RankEntry
func (m *MockRankEntry) ConvertToRankEntry() *rank.Entry {
	return &rank.Entry{
		PlayerID:   m.PlayerID,
		Prosperity: m.Prosperity,
		PlayerName: m.PlayerName,
		Level:      m.Level,
		UpdateTime: m.UpdateTime,
	}
}

// BenchmarkUpdateRankEntry tests single rank entry update performance
func BenchmarkUpdateRankEntry(b *testing.B) {
	// Initialize test environment
	bootstrap.InitRedis()

	manager := rank.GetRankManager()
	ctx := context.Background()
	rankType := int32(2) // Prosperity rank

	// Generate test data
	mockGen := NewMockDataGenerator()

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		mockEntry := mockGen.GenerateRankEntry()
		entry := mockEntry.ConvertToRankEntry()

		_, err := manager.UpdateRankEntry(ctx, rankType, entry)
		if err != nil {
			b.Errorf("UpdateRankEntry failed: %v", err)
		}
	}
}

// BenchmarkBatchUpdateRankEntries tests batch rank entry update performance
func BenchmarkBatchUpdateRankEntries(b *testing.B) {
	// Initialize test environment
	bootstrap.InitRedis()

	manager := rank.GetRankManager()
	ctx := context.Background()
	rankType := int32(2)
	batchSize := 100

	// Pre-generate test data
	mockGen := NewMockDataGenerator()
	batches := make([][]*rank.Entry, b.N)
	for i := 0; i < b.N; i++ {
		mockEntries := mockGen.GenerateBatchRankEntries(batchSize)
		batch := make([]*rank.Entry, batchSize)
		for j, mockEntry := range mockEntries {
			batch[j] = mockEntry.ConvertToRankEntry()
		}
		batches[i] = batch
	}

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		for _, entry := range batches[i] {
			_, err := manager.UpdateRankEntry(ctx, rankType, entry)
			if err != nil {
				b.Errorf("UpdateRankEntry failed: %v", err)
			}
		}
	}
}

// BenchmarkConcurrentUpdateRankEntry tests concurrent rank entry update performance
func BenchmarkConcurrentUpdateRankEntry(b *testing.B) {
	// Initialize test environment
	bootstrap.InitRedis()

	manager := rank.GetRankManager()
	ctx := context.Background()
	rankType := int32(2)

	b.ResetTimer()
	b.RunParallel(func(pb *testing.PB) {
		mockGen := NewMockDataGenerator()
		for pb.Next() {
			mockEntry := mockGen.GenerateRankEntry()
			entry := mockEntry.ConvertToRankEntry()

			_, err := manager.UpdateRankEntry(ctx, rankType, entry)
			if err != nil {
				b.Errorf("UpdateRankEntry failed: %v", err)
			}
		}
	})
}

// BenchmarkGetRankList tests get rank list performance
func BenchmarkGetRankList(b *testing.B) {
	// Initialize test environment
	bootstrap.InitRedis()

	manager := rank.GetRankManager()
	ctx := context.Background()
	rankType := int32(2) // Prosperity rank

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		_, _, err := manager.GetRankList(ctx, rankType, 1, 50)
		if err != nil {
			b.Errorf("GetRankList failed: %v", err)
		}
	}
}

// BenchmarkGetPlayerRank tests get player rank performance
func BenchmarkGetPlayerRank(b *testing.B) {
	manager := rank.GetRankManager()
	ctx := context.Background()
	rankType := int32(2)
	playerID := uint64(12345)

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		_, err := manager.GetPlayerRank(ctx, rankType, playerID)
		if err != nil {
			b.Errorf("GetPlayerRank failed: %v", err)
		}
	}
}

// BenchmarkGetMultiPlayerRank tests batch get player rank performance
func BenchmarkGetMultiPlayerRank(b *testing.B) {
	manager := rank.GetRankManager()
	ctx := context.Background()
	rankType := int32(2)

	// Generate test player ID list
	playerIDs := make([]uint64, 100)
	for i := range playerIDs {
		playerIDs[i] = uint64(10000 + i)
	}

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		_, err := manager.GetMultiPlayerRank(ctx, rankType, playerIDs)
		if err != nil {
			b.Errorf("GetMultiPlayerRank failed: %v", err)
		}
	}
}

// TestInsertStressTest insert rank stress test
func TestInsertStressTest(t *testing.T) {
	// Initialize test environment
	bootstrap.InitRedis()

	manager := rank.GetRankManager()
	ctx := context.Background()
	rankType := int32(2) // Prosperity rank

	// Clear rank
	err := manager.ClearRank(ctx, rankType)
	if err != nil {
		t.Fatalf("Failed to clear rank: %v", err)
	}

	logger.Info("Starting insert stress test")

	// Test parameters
	testCases := []struct {
		name         string
		concurrency  int
		totalInserts int
		batchSize    int
	}{
		{"Low Load", 5, 1000, 10},
		{"Medium Load", 10, 5000, 50},
		{"High Load", 20, 10000, 100},
		{"Extreme Load", 50, 20000, 200},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			// Clear rank
			err := manager.ClearRank(ctx, rankType)
			if err != nil {
				t.Fatalf("Failed to clear rank: %v", err)
			}

			// Statistics variables
			var successCount int64
			var errorCount int64
			var totalLatency int64

			start := time.Now()

			// Create worker goroutines
			var wg sync.WaitGroup
			insertsPerGoroutine := tc.totalInserts / tc.concurrency

			for i := 0; i < tc.concurrency; i++ {
				wg.Add(1)
				go func(goroutineID int) {
					defer wg.Done()

					mockGen := NewMockDataGenerator()

					for j := 0; j < insertsPerGoroutine; j++ {
						// Generate test data
						mockEntry := mockGen.GenerateRankEntry()
						entry := mockEntry.ConvertToRankEntry()

						// Record single operation latency
						opStart := time.Now()
						_, err := manager.UpdateRankEntry(ctx, rankType, entry)
						opDuration := time.Since(opStart)

						if err != nil {
							atomic.AddInt64(&errorCount, 1)
							t.Logf("Goroutine %d: UpdateRankEntry failed: %v", goroutineID, err)
						} else {
							atomic.AddInt64(&successCount, 1)
							atomic.AddInt64(&totalLatency, opDuration.Nanoseconds())
						}

						// Simulate real scenario intervals
						if j%tc.batchSize == 0 && j > 0 {
							time.Sleep(time.Millisecond * 10)
						}
					}
				}(i)
			}

			// Wait for all goroutines to complete
			wg.Wait()

			duration := time.Since(start)
			totalOps := successCount + errorCount

			// Calculate performance metrics
			qps := float64(successCount) / duration.Seconds()
			avgLatency := time.Duration(totalLatency / successCount)
			errorRate := float64(errorCount) / float64(totalOps) * 100

			// Get final rank status
			entries, total, err := manager.GetRankList(ctx, rankType, 1, 100)
			if err != nil {
				t.Errorf("Failed to get rank list: %v", err)
			}

			// Get cache statistics
			cacheStats := manager.GetCacheStats()

			// Output test results
			logger.Info("Insert stress test completed",
				zap.String("test_case", tc.name),
				zap.Int("concurrency", tc.concurrency),
				zap.Int64("success_count", successCount),
				zap.Int64("error_count", errorCount),
				zap.Float64("error_rate_percent", errorRate),
				zap.Duration("total_duration", duration),
				zap.Float64("qps", qps),
				zap.Duration("avg_latency", avgLatency),
				zap.Int64("final_rank_count", total),
				zap.Int("top_100_entries", len(entries)),
				zap.Any("cache_stats", cacheStats))

			t.Logf("=== %s Results ===", tc.name)
			t.Logf("Concurrency: %d", tc.concurrency)
			t.Logf("Total Operations: %d", totalOps)
			t.Logf("Success: %d, Errors: %d", successCount, errorCount)
			t.Logf("Error Rate: %.2f%%", errorRate)
			t.Logf("Duration: %v", duration)
			t.Logf("QPS: %.2f", qps)
			t.Logf("Average Latency: %v", avgLatency)
			t.Logf("Final Rank Count: %d", total)
			t.Logf("Cache Stats: %+v", cacheStats)

			// Verify data consistency
			if total > 0 && len(entries) > 0 {
				// Check if sorting is correct
				for i := 1; i < len(entries); i++ {
					if entries[i-1].Prosperity < entries[i].Prosperity {
						t.Errorf("Rank order is incorrect at position %d", i)
					}
				}
				t.Logf("Top 5 players: %v", entries[:min(5, len(entries))])
			}
		})
	}
}

// TestMixedWorkloadStressTest mixed workload stress test
func TestMixedWorkloadStressTest(t *testing.T) {
	// Initialize test environment
	bootstrap.InitRedis()

	manager := rank.GetRankManager()
	ctx := context.Background()
	rankType := int32(2)

	// Pre-fill some data
	mockGen := NewMockDataGenerator()
	initialEntries := mockGen.GenerateRankEntriesWithDistribution(1000)
	for _, mockEntry := range initialEntries {
		entry := mockEntry.ConvertToRankEntry()
		_, err := manager.UpdateRankEntry(ctx, rankType, entry)
		if err != nil {
			t.Fatalf("Failed to insert initial data: %v", err)
		}
	}

	logger.Info("Starting mixed workload stress test")

	// Statistics variables
	var insertCount int64
	var queryCount int64
	var batchQueryCount int64
	var errorCount int64

	concurrency := 20
	testDuration := 30 * time.Second

	start := time.Now()
	stopCh := make(chan struct{})

	// Start timer
	go func() {
		time.Sleep(testDuration)
		close(stopCh)
	}()

	var wg sync.WaitGroup

	// Create worker goroutines
	for i := 0; i < concurrency; i++ {
		wg.Add(1)
		go func(goroutineID int) {
			defer wg.Done()

			localMockGen := NewMockDataGenerator()

			for {
				select {
				case <-stopCh:
					return
				default:
					// Randomly select operation type
					switch rand.Intn(10) {
					case 0, 1, 2: // 30% insert operations
						mockEntry := localMockGen.GenerateRankEntry()
						entry := mockEntry.ConvertToRankEntry()
						_, err := manager.UpdateRankEntry(ctx, rankType, entry)
						if err != nil {
							atomic.AddInt64(&errorCount, 1)
						} else {
							atomic.AddInt64(&insertCount, 1)
						}

					case 3, 4, 5, 6: // 40% single queries
						playerID := uint64(100000 + rand.Intn(2000))
						_, err := manager.GetPlayerRank(ctx, rankType, playerID)
						if err != nil {
							atomic.AddInt64(&errorCount, 1)
						} else {
							atomic.AddInt64(&queryCount, 1)
						}

					case 7, 8: // 20% rank list queries
						start := rand.Int31n(100) + 1
						count := rand.Int31n(50) + 10
						_, _, err := manager.GetRankList(ctx, rankType, start, count)
						if err != nil {
							atomic.AddInt64(&errorCount, 1)
						} else {
							atomic.AddInt64(&queryCount, 1)
						}

					case 9: // 10% batch queries
						playerIDs := make([]uint64, 20)
						for j := range playerIDs {
							playerIDs[j] = uint64(100000 + rand.Intn(2000))
						}
						_, err := manager.GetMultiPlayerRank(ctx, rankType, playerIDs)
						if err != nil {
							atomic.AddInt64(&errorCount, 1)
						} else {
							atomic.AddInt64(&batchQueryCount, 1)
						}
					}

					// Short rest
					time.Sleep(time.Millisecond * time.Duration(rand.Intn(10)))
				}
			}
		}(i)
	}

	// Wait for all goroutines to complete
	wg.Wait()

	actualDuration := time.Since(start)
	totalOps := insertCount + queryCount + batchQueryCount + errorCount

	// Calculate performance metrics
	totalQPS := float64(totalOps) / actualDuration.Seconds()
	insertQPS := float64(insertCount) / actualDuration.Seconds()
	queryQPS := float64(queryCount) / actualDuration.Seconds()
	batchQueryQPS := float64(batchQueryCount) / actualDuration.Seconds()
	errorRate := float64(errorCount) / float64(totalOps) * 100

	// Get final status
	entries, total, _ := manager.GetRankList(ctx, rankType, 1, 10)
	cacheStats := manager.GetCacheStats()

	// Output test results
	logger.Info("Mixed workload stress test completed",
		zap.Int("concurrency", concurrency),
		zap.Duration("duration", actualDuration),
		zap.Int64("total_operations", totalOps),
		zap.Int64("insert_count", insertCount),
		zap.Int64("query_count", queryCount),
		zap.Int64("batch_query_count", batchQueryCount),
		zap.Int64("error_count", errorCount),
		zap.Float64("error_rate_percent", errorRate),
		zap.Float64("total_qps", totalQPS),
		zap.Float64("insert_qps", insertQPS),
		zap.Float64("query_qps", queryQPS),
		zap.Float64("batch_query_qps", batchQueryQPS),
		zap.Int64("final_rank_count", total),
		zap.Any("cache_stats", cacheStats))

	t.Logf("=== Mixed Workload Stress Test Results ===")
	t.Logf("Concurrency: %d", concurrency)
	t.Logf("Duration: %v", actualDuration)
	t.Logf("Total Operations: %d", totalOps)
	t.Logf("  - Inserts: %d (%.1f QPS)", insertCount, insertQPS)
	t.Logf("  - Queries: %d (%.1f QPS)", queryCount, queryQPS)
	t.Logf("  - Batch Queries: %d (%.1f QPS)", batchQueryCount, batchQueryQPS)
	t.Logf("  - Errors: %d (%.2f%%)", errorCount, errorRate)
	t.Logf("Total QPS: %.2f", totalQPS)
	t.Logf("Final Rank Count: %d", total)
	t.Logf("Cache Stats: %+v", cacheStats)

	if len(entries) > 0 {
		t.Logf("Top 3 players:")
		for i, entry := range entries[:min(3, len(entries))] {
			t.Logf("  %d. Player %d: %d prosperity", i+1, entry.PlayerID, entry.Prosperity)
		}
	}
}
