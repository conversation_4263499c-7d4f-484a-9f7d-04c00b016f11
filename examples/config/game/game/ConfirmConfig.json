{"1": {"Id": 1, "isPopUp": true, "isSystem": true, "repeat": 0, "checkTextFmt": "", "contentFmt": "$ui_confirm_desc_login_failure", "okTitle": "$btn_confirm", "cancelTitle": "$btn_cancel", "title": "$ui_common_confirm_view_title", "titleImg": "", "showClose": false, "okBtnCD": 0}, "2": {"Id": 2, "isPopUp": false, "isSystem": true, "repeat": 0, "checkTextFmt": "", "contentFmt": "$ui_confirm_desc_network_error", "okTitle": "$btn_confirm", "cancelTitle": "", "title": "$ui_common_confirm_view_title", "titleImg": "", "showClose": false, "okBtnCD": 0}, "3": {"Id": 3, "isPopUp": false, "isSystem": true, "repeat": 0, "checkTextFmt": "", "contentFmt": "$ui_confirm_desc_duplicate_login", "okTitle": "$btn_confirm", "cancelTitle": "", "title": "$ui_common_confirm_view_title", "titleImg": "", "showClose": false, "okBtnCD": 0}, "8": {"Id": 8, "isPopUp": true, "isSystem": false, "repeat": 0, "checkTextFmt": "", "contentFmt": "$ui_confirm_desc_password_error", "okTitle": "$btn_confirm", "cancelTitle": "", "title": "$ui_common_confirm_view_title", "titleImg": "", "showClose": false, "okBtnCD": 0}, "11": {"Id": 11, "isPopUp": false, "isSystem": true, "repeat": 0, "checkTextFmt": "", "contentFmt": "$ui_confirm_desc_network_timeout_retry", "okTitle": "$btn_retry", "cancelTitle": "", "title": "$ui_common_confirm_view_title", "titleImg": "", "showClose": false, "okBtnCD": 0}, "14": {"Id": 14, "isPopUp": false, "isSystem": true, "repeat": 0, "checkTextFmt": "", "contentFmt": "$ui_confirm_desc_connect_to_login_server_failure", "okTitle": "$btn_retry", "cancelTitle": "", "title": "$ui_common_confirm_view_title", "titleImg": "", "showClose": false, "okBtnCD": 0}, "15": {"Id": 15, "isPopUp": false, "isSystem": false, "repeat": 0, "checkTextFmt": "", "contentFmt": "$ui_confirm_desc_logout_double_check", "okTitle": "$btn_confirm", "cancelTitle": "$btn_cancel", "title": "$ui_common_confirm_view_title", "titleImg": "", "showClose": false, "okBtnCD": 0}, "19": {"Id": 19, "isPopUp": false, "isSystem": false, "repeat": 0, "checkTextFmt": "", "contentFmt": "$ui_confirm_desc_reboot_on_language_change", "okTitle": "$btn_confirm", "cancelTitle": "$btn_cancel", "title": "$ui_common_confirm_view_title", "titleImg": "", "showClose": false, "okBtnCD": 0}, "20": {"Id": 20, "isPopUp": false, "isSystem": true, "repeat": 0, "checkTextFmt": "", "contentFmt": "$ui_confirm_desc_client_new_version", "okTitle": "$btn_confirm", "cancelTitle": "", "title": "$ui_common_confirm_view_title", "titleImg": "", "showClose": false, "okBtnCD": 0}, "26": {"Id": 26, "isPopUp": false, "isSystem": true, "repeat": 0, "checkTextFmt": "", "contentFmt": "$ui_confirm_desc_connect_server_failure_retry", "okTitle": "$btn_confirm", "cancelTitle": "", "title": "$ui_common_confirm_view_title", "titleImg": "", "showClose": false, "okBtnCD": 0}, "29": {"Id": 29, "isPopUp": false, "isSystem": true, "repeat": 0, "checkTextFmt": "", "contentFmt": "$ui_confirm_desc_login_timeout_check_network_and_retry", "okTitle": "$btn_retry", "cancelTitle": "", "title": "$ui_common_confirm_view_title", "titleImg": "", "showClose": false, "okBtnCD": 0}, "30": {"Id": 30, "isPopUp": true, "isSystem": false, "repeat": 0, "checkTextFmt": "", "contentFmt": "$ui_confirm_desc_replace_hero_dispatch", "okTitle": "$btn_confirm", "cancelTitle": "$btn_cancel", "title": "$ui_common_confirm_view_title", "titleImg": "", "showClose": false, "okBtnCD": 0}, "31": {"Id": 31, "isPopUp": true, "isSystem": true, "repeat": 0, "checkTextFmt": "", "contentFmt": "$ui_confirm_desc_is_flee_fight", "okTitle": "$btn_confirm", "cancelTitle": "$btn_cancel", "title": "$ui_common_confirm_view_title", "titleImg": "", "showClose": false, "okBtnCD": 0}, "40": {"Id": 40, "isPopUp": true, "isSystem": true, "repeat": 0, "checkTextFmt": "", "contentFmt": "$ui_confirm_desc_user_diff", "okTitle": "$btn_confirm", "cancelTitle": "$btn_cancel", "title": "$ui_common_confirm_view_title", "titleImg": "", "showClose": false, "okBtnCD": 0}, "43": {"Id": 43, "isPopUp": true, "isSystem": true, "repeat": 0, "checkTextFmt": "", "contentFmt": "$ui_confirm_desc_get_srv_list_failure", "okTitle": "$btn_confirm", "cancelTitle": "$btn_cancel", "title": "$ui_common_confirm_view_title", "titleImg": "", "showClose": false, "okBtnCD": 0}, "49": {"Id": 49, "isPopUp": true, "isSystem": true, "repeat": 0, "checkTextFmt": "", "contentFmt": "$ui_confirm_desc_duplicate_login", "okTitle": "$btn_confirm", "cancelTitle": "$btn_cancel", "title": "$ui_common_confirm_view_title", "titleImg": "", "showClose": false, "okBtnCD": 0}, "50": {"Id": 50, "isPopUp": false, "isSystem": true, "repeat": 0, "checkTextFmt": "", "contentFmt": "$ui_confirm_desc_client_new_version", "okTitle": "$btn_confirm", "cancelTitle": "", "title": "$ui_common_confirm_view_title", "titleImg": "", "showClose": false, "okBtnCD": 0}, "51": {"Id": 51, "isPopUp": true, "isSystem": false, "repeat": 0, "checkTextFmt": "", "contentFmt": "$ui_confirm_desc_order_refresh_high_quality", "okTitle": "$btn_confirm", "cancelTitle": "$btn_cancel", "title": "$ui_common_confirm_view_title", "titleImg": "", "showClose": false, "okBtnCD": 0}, "52": {"Id": 52, "isPopUp": false, "isSystem": false, "repeat": 0, "checkTextFmt": "", "contentFmt": "$ui_confirm_desc_rename_notify", "okTitle": "$btn_confirm", "cancelTitle": "$btn_cancel", "title": "$ui_common_confirm_view_title", "titleImg": "", "showClose": false, "okBtnCD": 0}, "_ids": [1, 2, 3, 8, 11, 14, 15, 19, 20, 26, 29, 30, 31, 40, 43, 49, 50, 51, 52]}