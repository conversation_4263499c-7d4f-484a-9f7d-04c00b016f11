package rank

import (
	"kairo_paradise_server/services/pb"
	"time"
)

// Type represents the type of leaderboard
type Type int32

// Config represents the configuration for a leaderboard
type Config struct {
	RankType       int32 // 排行榜类型，对应 ERankType
	MinScoreLimit  int32 // 最低分数限制，低于此分数不能上榜
	MaxQueryLimit  int32 // 查询人数上限
	ShowRankLimit  int32 // 榜上显示数量
	MaxRankLimit   int32 // 排行数量
	UpdateInterval int64 // 更新间隔（秒），0表示实时更新
	IsRealtime     bool  // 是否为实时排行榜
}

// Entry represents an entry in the leaderboard
type Entry struct {
	PlayerID   uint64 // 玩家ID
	PlayerName string // 玩家名称
	Level      int32  // 玩家等级
	Prosperity int32  // 繁荣度
	UpdateTime int64  // 更新时间
}

// ToProto converts a RankEntry to a protobuf RankEntryData
func (e *Entry) ToProto() *pb.RankEntryData {
	return &pb.RankEntryData{
		PlayerId:   &e.PlayerID,
		PlayerName: &e.PlayerName,
		Level:      &e.Level,
		Prosperity: &e.Prosperity,
	}
}

// FromProto converts a protobuf RankEntryData to a RankEntry
func (e *Entry) FromProto(data *pb.RankEntryData) {
	if data.PlayerId != nil {
		e.PlayerID = *data.PlayerId
	}
	e.UpdateTime = time.Now().Unix()
}

// NewRankEntryFromProto creates a new RankEntry from a protobuf RankEntryData
func NewRankEntryFromProto(data *pb.RankEntryData) *Entry {
	entry := &Entry{}
	entry.FromProto(data)
	return entry
}

// RankInfo represents information about a player's rank
type RankInfo struct {
	Rank  int32 // 排名
	Entry Entry // 排行榜条目
}

// Cache represents cached rank data
type Cache struct {
	Entries    []*Entry // 排行榜条目列表
	TotalCount int64    // 总条目数
	UpdateTime int64    // 缓存更新时间
}

// PlayerRankCache represents cached player rank data
type PlayerRankCache struct {
	Rank       int32  // 玩家排名
	Entry      *Entry // 玩家排行榜条目
	UpdateTime int64  // 缓存更新时间
}
