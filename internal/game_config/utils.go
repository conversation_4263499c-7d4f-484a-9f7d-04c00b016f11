package game_config

import (
	"fmt"
	"github.com/golang/protobuf/proto"
	"kairo_paradise_server/internal/consts"
	"kairo_paradise_server/internal/logger"
	"os"
	"path/filepath"
	"reflect"
)

// LoadProtoConfig is a generic function to load protobuf config files
// P is the protobuf message type (e.g., *pb.ListMainTaskConfig)
// T is the config struct type (e.g., *MainTaskConfig)
// I is the item type in the protobuf list (e.g., *pb.ListMainTaskConfig_MainTaskConfig)
// configName is the name of the config file without extension (e.g., "MainTaskConfig")
// getList is a function that extracts the list field from the protobuf message
// getId is a function that extracts the ID from an item in the list
func loadProtoConfig[P proto.Message, T any, I any](
	configName string,
	protoFactory func() P,
	newConfig func() T,
	getList func(P) []I,
	getId func(I) int32,
) T {
	logger.Debug(fmt.Sprintf("load %s", configName))

	// Load the protobuf file
	listConfig, err := loadProtoFile(filepath.Join(consts.GameConfigPath, configName+".bytes"), protoFactory)
	if err != nil {
		logger.Fatalf("load %s.bytes error: %v", configName, err)
		return reflect.Zero(reflect.TypeOf((*T)(nil)).Elem()).Interface().(T)
	}

	// Create a new config instance
	result := newConfig()

	// Get the reflect.Value of the result
	resultValue := reflect.ValueOf(result).Elem()

	// Find the Data field
	dataField := resultValue.FieldByName("Data")
	if !dataField.IsValid() {
		logger.Fatalf("%s struct does not have a Data field", configName)
		return result
	}

	// Find the Ids field
	idsField := resultValue.FieldByName("Ids")
	if !idsField.IsValid() {
		logger.Fatalf("%s struct does not have an Ids field", configName)
		return result
	}

	// Get the list of items from the protobuf message
	items := getList(listConfig)

	// Process each item in the list
	for _, item := range items {
		id := getId(item)

		// Add the item to the Data map
		key := reflect.ValueOf(id)
		value := reflect.ValueOf(item)
		dataField.SetMapIndex(key, value)

		// Add the ID to the Ids slice
		idValue := reflect.ValueOf(id)
		newIds := reflect.Append(idsField, idValue)
		idsField.Set(newIds)
	}

	return result
}

// loadProtoConfigSimple is a simplified version of LoadProtoConfig for common cases
// where the protobuf message has a List field and each item has an Id field
// P is the protobuf message type (e.g., *pb.ListMainTaskConfig)
// T is the config struct type (e.g., *MainTaskConfig)
// I is the item type in the protobuf list (e.g., *pb.ListMainTaskConfig_MainTaskConfig)
func loadProtoConfigSimple[P proto.Message, T any, I any](
	configName string,
	protoFactory func() P,
	newConfig func() T,
	getIdField func(I) *int32,
) T {
	// Use reflection to get the List field from the protobuf message
	getList := func(p P) []I {
		pValue := reflect.ValueOf(p).Elem()
		listField := pValue.FieldByName("List")
		if !listField.IsValid() {
			logger.Fatalf("%s protobuf message does not have a List field", configName)
			return nil
		}

		// Convert the list field to []I
		listInterface := listField.Interface()
		list, ok := listInterface.([]I)
		if !ok {
			logger.Fatalf("%s List field is not of the expected type", configName)
			return nil
		}

		return list
	}

	// Extract the ID from the item
	getId := func(item I) int32 {
		idField := getIdField(item)
		if idField != nil {
			return *idField
		}
		return 0
	}

	return loadProtoConfig(configName, protoFactory, newConfig, getList, getId)
}

func unmarshalProtoBytes[T proto.Message](data []byte, factory func() T) (T, error) {
	message := factory()
	if err := proto.Unmarshal(data, message); err != nil {
		return message, fmt.Errorf("failed to unmarshal proto: %w", err)
	}
	return message, nil
}

func loadProtoFile[T proto.Message](fileName string, factory func() T) (T, error) {
	var empty T

	if _, err := os.Stat(fileName); os.IsNotExist(err) {
		fileName = filepath.Join("../bin", fileName)
	}

	// Read the binary file
	b, err := os.ReadFile(fileName)
	if err != nil {
		return empty, fmt.Errorf("failed to read file: %w", err)
	}

	// Unmarshal the binary data
	return unmarshalProtoBytes(b, factory)
}
