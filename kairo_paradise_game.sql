/*
 Navicat Premium Dump SQL

 Source Server         : local-mysql
 Source Server Type    : MySQL
 Source Server Version : 80029 (8.0.29)
 Source Host           : 127.0.0.1:3306
 Source Schema         : kairo_paradise_game

 Target Server Type    : MySQL
 Target Server Version : 80029 (8.0.29)
 File Encoding         : 65001

 Date: 23/05/2025 18:16:23
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for t_account
-- ----------------------------
DROP TABLE IF EXISTS `t_account`;
CREATE TABLE `t_account`  (
  `user_id` int UNSIGNED NOT NULL,
  `account` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '账号名',
  `password` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '密码',
  `salt` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '密码盐值',
  PRIMARY KEY (`user_id`) USING BTREE,
  UNIQUE INDEX `account_idx`(`account` ASC) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '账号登录表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of t_account
-- ----------------------------
INSERT INTO `t_account` VALUES (1, 'test123', '7a13afd828ac9dfe7665c9bf9c4ad0f6', 'qCyrYbvj48');
INSERT INTO `t_account` VALUES (2, 'test1234', '9664c7dcb424f766546db8c67a032a0a', 'y84dvWJo7K');
INSERT INTO `t_account` VALUES (3, 'test12345', '05a5d9143b56c5e521ef7a589ef9d198', 'UBbIvg2pSJ');
INSERT INTO `t_account` VALUES (4, 'test123456', '155e5f6aa7061b838c6deb68d5718716', 'gWB2G0JQnB');
INSERT INTO `t_account` VALUES (5, 'test123457', '03af91033de305f8a5dafcff3f650faa', '6maUJ6h99b');
INSERT INTO `t_account` VALUES (6, 'test', '9f106e7ee909e94c377aab029d56df5a', '83tPKubLH3');
INSERT INTO `t_account` VALUES (10000, 'test1', '20de5bad95b2c7036173c413384a5efc', 'D2z8tb74yU');
INSERT INTO `t_account` VALUES (10001, 'test2', '64c5b93c47eb387d7f08afcf35658d4d', 'BePxroumXN');
INSERT INTO `t_account` VALUES (10002, 'test3', 'dbfc09f28550000afcac57a4f3bf7538', 'XVi68iQRGc');
INSERT INTO `t_account` VALUES (10003, 'test4', '6beb7d38a2136b57ba3a209a692c76e7', 'zNCR8T7Zs4');

-- ----------------------------
-- Table structure for t_game
-- ----------------------------
DROP TABLE IF EXISTS `t_game`;
CREATE TABLE `t_game`  (
  `id` int UNSIGNED NOT NULL AUTO_INCREMENT,
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '游戏名',
  `descript` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '游戏描述',
  `price` decimal(10, 2) UNSIGNED NOT NULL DEFAULT 0.00 COMMENT '价格',
  `type` tinyint NOT NULL DEFAULT 1 COMMENT '类型，1：单机，2：内购游戏',
  `resource_url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '资源地址',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '内嵌游戏列表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of t_game
-- ----------------------------

-- ----------------------------
-- Table structure for t_game_purchases
-- ----------------------------
DROP TABLE IF EXISTS `t_game_purchases`;
CREATE TABLE `t_game_purchases`  (
  `player_id` int UNSIGNED NOT NULL DEFAULT 0,
  `game_id` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '游戏ID',
  `price` decimal(10, 2) UNSIGNED NOT NULL DEFAULT 0.00 COMMENT '购买金额',
  `purchase_date` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`player_id`, `game_id` DESC) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '游戏购买信息表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of t_game_purchases
-- ----------------------------

-- ----------------------------
-- Table structure for t_oauth
-- ----------------------------
DROP TABLE IF EXISTS `t_oauth`;
CREATE TABLE `t_oauth`  (
  `user_id` int UNSIGNED NOT NULL,
  `account` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '渠道账号',
  `channel_no` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '渠道号',
  `channel` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '渠道标识',
  PRIMARY KEY (`user_id`) USING BTREE,
  UNIQUE INDEX `accout_idx`(`account` ASC, `channel_no` ASC) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '账号登录信息表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of t_oauth
-- ----------------------------

-- ----------------------------
-- Table structure for t_player
-- ----------------------------
DROP TABLE IF EXISTS `t_player`;
CREATE TABLE `t_player`  (
  `id` int UNSIGNED NOT NULL AUTO_INCREMENT,
  `user_id` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '账号id',
  `server_id` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '区服ID',
  `nick` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '昵称',
  `gender` tinyint UNSIGNED NOT NULL DEFAULT 0 COMMENT '性别，0：男，1：女',
  `icon` int UNSIGNED NOT NULL DEFAULT 1 COMMENT '头像ID',
  `level` int UNSIGNED NOT NULL DEFAULT 1 COMMENT '世界等级',
  `prosperity` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '繁荣度',
  `created_at` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '创建时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `user_idx`(`user_id` ASC, `server_id` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 10009 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '玩家信息表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of t_player
-- ----------------------------
INSERT INTO `t_player` VALUES (10000, 1, 0, '无敌风火轮', 1, 1, 1, 0, 0);
INSERT INTO `t_player` VALUES (10003, 6, 0, 'tes', 1, 1, 1, 0, 1747032435);
INSERT INTO `t_player` VALUES (10004, 10000, 0, '1', 0, 1, 1, 0, 1747643035);
INSERT INTO `t_player` VALUES (10005, 10001, 0, '', 0, 1, 1, 0, 1747648330);
INSERT INTO `t_player` VALUES (10006, 2, 0, '', 0, 1, 1, 0, 1747649927);
INSERT INTO `t_player` VALUES (10007, 10002, 0, 'test12', 1, 1, 1, 0, 1747650145);
INSERT INTO `t_player` VALUES (10008, 10003, 0, '习近平', 1, 1, 1, 0, 1747657215);

-- ----------------------------
-- Table structure for t_player_asset
-- ----------------------------
DROP TABLE IF EXISTS `t_player_asset`;
CREATE TABLE `t_player_asset`  (
  `player_id` int UNSIGNED NOT NULL,
  `coin` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '钻石货币',
  `max_item_id` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '最大物品id',
  `item_list` blob NULL COMMENT '物品列表',
  `item_list_json` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '物品列表-json格式',
  `scene` blob NULL COMMENT '场景',
  PRIMARY KEY (`player_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '玩家物品表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of t_player_asset
-- ----------------------------
INSERT INTO `t_player_asset` VALUES (10000, 0, 0, 0x0A11080110E707180420B9ACAAC106280030020A11080B109103180420D8ACAAC10628003002, '', NULL);
INSERT INTO `t_player_asset` VALUES (10003, 0, 0, '', '', NULL);
INSERT INTO `t_player_asset` VALUES (10004, 0, 0, '', '', NULL);
INSERT INTO `t_player_asset` VALUES (10005, 0, 0, NULL, '', NULL);
INSERT INTO `t_player_asset` VALUES (10006, 0, 1, '', '', '');
INSERT INTO `t_player_asset` VALUES (10007, 0, 0, '', '', NULL);
INSERT INTO `t_player_asset` VALUES (10008, 0, 0, '', '', '');

-- ----------------------------
-- Table structure for t_player_mail
-- ----------------------------
DROP TABLE IF EXISTS `t_player_mail`;
CREATE TABLE `t_player_mail`  (
  `id` int UNSIGNED NOT NULL AUTO_INCREMENT,
  `player_id` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '玩家ID',
  `mail_id` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '邮件ID',
  `title` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '邮件标题',
  `content` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '邮件内容',
  `sender` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '发送者',
  `status` tinyint UNSIGNED NOT NULL DEFAULT 1 COMMENT '状态，1：未读，2：已读，3：已领取',
  `attachments` blob NULL COMMENT '附件物品信息',
  `created_at` int UNSIGNED NOT NULL DEFAULT 0,
  `expire_at` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '有效期时间戳',
  `deleted_at` tinyint NOT NULL COMMENT '是否删除',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `player_idx`(`player_id` ASC) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '玩家邮箱' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of t_player_mail
-- ----------------------------

-- ----------------------------
-- Table structure for t_player_time
-- ----------------------------
DROP TABLE IF EXISTS `t_player_time`;
CREATE TABLE `t_player_time`  (
  `player_id` int NOT NULL COMMENT '玩家ID',
  `create_time` bigint NOT NULL DEFAULT 0 COMMENT '创建时间',
  `create_local_time` datetime(3) NOT NULL COMMENT '创建本地时间',
  `login_time` bigint NOT NULL DEFAULT 0 COMMENT '上次登录时间',
  `logout_time` bigint NOT NULL DEFAULT 0 COMMENT '上次登出时间',
  `daily_reset_time` bigint NOT NULL DEFAULT 0 COMMENT '每日重置时间',
  `zone_daily_reset_time` bigint NOT NULL DEFAULT 0 COMMENT '按时区每日重置时间',
  `stay_flag` int NOT NULL DEFAULT 0 COMMENT '留存标记',
  PRIMARY KEY (`player_id`) USING BTREE,
  INDEX `idx_t_user_time_stay_flag`(`stay_flag` ASC) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of t_player_time
-- ----------------------------

-- ----------------------------
-- Table structure for t_user_info
-- ----------------------------
DROP TABLE IF EXISTS `t_user_info`;
CREATE TABLE `t_user_info`  (
  `id` int UNSIGNED NOT NULL AUTO_INCREMENT,
  `state` tinyint UNSIGNED NOT NULL DEFAULT 0 COMMENT '用户状态 0离线 1在线 2活动中',
  `status` tinyint UNSIGNED NOT NULL DEFAULT 0 COMMENT '账号状态 0：正常，1：被封禁',
  `age` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '年龄',
  `auth` int UNSIGNED NOT NULL DEFAULT 2 COMMENT '防沉迷，0：未成年，1：已成年，2：未实名',
  `birthday` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '生日',
  `user_type` tinyint UNSIGNED NOT NULL DEFAULT 0 COMMENT '用户类型：0：普通用户，1：内部用户',
  `register_ts` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '注册时间戳',
  `last_login_ts` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '最后登录时间戳',
  `last_login_ip` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '最后登录ip',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 10004 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '玩家信息表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of t_user_info
-- ----------------------------
INSERT INTO `t_user_info` VALUES (1, 0, 1, 0, 2, '', 2, 1745808693, 1745808693, '127.0.0.1');
INSERT INTO `t_user_info` VALUES (2, 0, 1, 0, 2, '', 2, 1745810602, 1745810602, '127.0.0.1');
INSERT INTO `t_user_info` VALUES (3, 0, 1, 0, 2, '', 2, 1745810760, 1745810760, '127.0.0.1');
INSERT INTO `t_user_info` VALUES (4, 0, 1, 0, 2, '', 2, 1745810788, 1745810788, '127.0.0.1');
INSERT INTO `t_user_info` VALUES (5, 0, 1, 0, 2, '', 2, 1745810988, 1745810988, '127.0.0.1');
INSERT INTO `t_user_info` VALUES (6, 0, 1, 0, 2, '', 2, 1746512577, 1746512577, '**************');
INSERT INTO `t_user_info` VALUES (10000, 0, 1, 0, 2, '', 2, 1747643035, 1747643035, '');
INSERT INTO `t_user_info` VALUES (10001, 0, 1, 0, 2, '', 2, 1747648330, 1747648330, '');
INSERT INTO `t_user_info` VALUES (10002, 0, 1, 0, 2, '', 2, 1747650144, 1747650144, '');
INSERT INTO `t_user_info` VALUES (10003, 0, 1, 0, 2, '', 2, 1747657215, 1747657215, '');

SET FOREIGN_KEY_CHECKS = 1;
