package game_config
import (
	"kairo_paradise_server/internal/consts"
	"kairo_paradise_server/internal/logger"
	"kairo_paradise_server/services/pb"
	"path/filepath"
)

// auto generated by tools, do not edit it manually
// P排行榜.xlsx WeeklyRankConfig

type weeklyRankConfig struct {
	data map[int32]*pb.ListWeeklyRankConfig_WeeklyRankConfig
	ids  []int32
}

var weeklyRankConfigFunc = func() *pb.ListWeeklyRankConfig { return &pb.ListWeeklyRankConfig{} }

var WeeklyRankConfig *weeklyRankConfig

func newWeeklyRankConfig() *weeklyRankConfig {
	result := &weeklyRankConfig{
		data: make(map[int32]*pb.ListWeeklyRankConfig_WeeklyRankConfig),
		ids:  make([]int32, 0),
	}

	listConfig, err := loadProtoFile(filepath.Join(consts.GameConfigPath, "WeeklyRankConfig.bytes"), weeklyRankConfigFunc)
	if err != nil {
		logger.Errorf("load WeeklyRankConfig.bytes error: %v", err)
		return result
	}
	for _, item := range listConfig.List {
		if item.Id != nil {
			id := *item.Id
			result.data[id] = item
			result.ids = append(result.ids, id)
		}
	}
	return result
}

func (c *weeklyRankConfig) Item(id int32) *pb.ListWeeklyRankConfig_WeeklyRankConfig {
	return c.data[id]
}

func (c *weeklyRankConfig) Items() []*pb.ListWeeklyRankConfig_WeeklyRankConfig {
	items := make([]*pb.ListWeeklyRankConfig_WeeklyRankConfig, 0, len(c.data))
	for _, item := range c.data {
		items = append(items, item)
	}
	return items
}

func (c *weeklyRankConfig) GetIds() []int32 {
	return c.ids
}
