package bootstrap

import (
	"context"
	"github.com/redis/go-redis/v9"
	"gorm.io/gorm"
	"kairo_paradise_server/internal/config"
	"kairo_paradise_server/internal/db/mysql"
	"kairo_paradise_server/internal/db/redis_client"
	"kairo_paradise_server/internal/discovery"
	"kairo_paradise_server/internal/event"
	"kairo_paradise_server/internal/jwt"
	"kairo_paradise_server/internal/logger"
	"kairo_paradise_server/internal/sdk_platform/leiting"
	"log"
	"sync"
	"time"
)

var MysqlDb *gorm.DB
var RedisClient *redis.Client
var JwtService *jwt.Service
var SafeClient *leiting.SafeWordClient

var once sync.Once

func BaseInit(serverName string) {
	config.InitServerConfig(serverName)
	initJwt()
	initDiscovery()
	initSafeClient()
}

// Bootstrap 初始化配置文件，初始化数据库连接
func Bootstrap(serverName string) {
	BaseInit(serverName)
	initMysql()
	InitRedis()
}

func initMysql() {
	mysqlConf := config.Config.MysqlConf
	client, err := mysql.DbClient("default", mysql.Config{
		Host:               mysqlConf.Host,
		Port:               mysqlConf.Port,
		User:               mysqlConf.User,
		Password:           mysqlConf.Password,
		Database:           mysqlConf.Database,
		Loc:                mysqlConf.Loc,
		Prefix:             mysqlConf.Prefix,
		Charset:            mysqlConf.Charset,
		SetMaxIdleConn:     mysqlConf.SetMaxIdleConn,
		SetMaxOpenConn:     mysqlConf.SetMaxOpenConn,
		SetConnMaxLifeTime: mysqlConf.SetConnMaxLifeTime,
		SetConnMaxIdleTime: mysqlConf.SetConnMaxIdleTime,
		LogLevel:           mysqlConf.LogLevel,
	})
	if err != nil {
		log.Fatalf("Failed to connect to database: %v", err)
	}
	MysqlDb = client
}

func InitRedis() {
	once.Do(func() {
		redisConf := config.Config.RedisConf
		client, err := redis_client.ConnectRedis("default", redis_client.Config{
			Host:   redisConf.Host,
			Port:   redisConf.Port,
			Passwd: redisConf.Passwd,
			Index:  redisConf.Index,
		})
		if err != nil {
			log.Fatalf("Failed to connect to redis: %v", err)
		}
		RedisClient = client
	})
}

func initJwt() {
	JwtService = jwt.NewService(jwt.Config{
		SecretKey:          config.Config.JWTConf.SecretKey,
		AccessTokenExpiry:  time.Minute * config.Config.JWTConf.AccessTokenExpiry,
		RefreshTokenExpiry: time.Hour * 24 * config.Config.JWTConf.RefreshTokenExpiry,
		Issuer:             config.Config.JWTConf.Issuer,
	})
}

func initDiscovery() {
	etcdConf := config.Config.EtcdConf

	// Convert config to discovery config
	discoveryConfig := discovery.Config{
		Endpoints:        etcdConf.Endpoints,
		DialTimeout:      etcdConf.DialTimeout,
		Username:         etcdConf.Username,
		Password:         etcdConf.Password,
		ServiceTTL:       etcdConf.ServiceTTL,
		ServicePrefix:    etcdConf.ServicePrefix,
		AutoSync:         etcdConf.AutoSync,
		AutoSyncInterval: etcdConf.AutoSyncInterval,
	}

	if err := discovery.Initialize(discoveryConfig); err != nil {
		logger.Fatalf("Failed to initialize discovery: %v", err)
	}

	// Register shutdown event
	event.RegisterFunc(event.Shutdown, "discovery-close", event.PriorityNormal, func(ctx context.Context, args ...interface{}) error {
		_ = discovery.Shutdown()
		return nil
	})
}

func initSafeClient() {
	detectionConf := config.Config.DetectionConf
	SafeClient = leiting.NewSafeWordClient(detectionConf.Domain, detectionConf.Game, detectionConf.Key, detectionConf.AppId)
}
