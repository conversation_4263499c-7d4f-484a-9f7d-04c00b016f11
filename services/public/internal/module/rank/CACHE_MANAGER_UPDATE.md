# 排行榜缓存管理器独立化更新

## 更新概述

本次更新实现了每个排行榜拥有独立的CacheManager，允许为不同排行榜配置不同的缓存策略，包括缓存更新时间和缓存排行榜数量等参数。

## 主要变更

### 1. 数据结构变更

#### Config结构新增字段
```go
type Config struct {
    // ... 原有字段
    
    // 新增缓存配置
    HotCacheSize    int32 // 热点缓存大小
    CacheExpireTime int64 // 缓存过期时间（秒）
}
```

#### Manager结构变更
```go
type Manager struct {
    // ... 原有字段
    
    // 变更：从单个缓存管理器改为map
    cacheManagers map[int32]*CacheManager // 每个排行榜有独立的缓存管理器
}
```

### 2. 新增方法

#### CacheManager构造函数
```go
// 使用配置创建缓存管理器
func NewCacheManagerWithConfig(hotCacheSize int32, cacheExpireTime int64) *CacheManager
```

#### 缓存配置方法
```go
// 为特定排行榜设置缓存配置
func (m *Manager) SetCacheExpireTime(rankType int32, expireTime int64)
func (m *Manager) SetHotCacheSize(rankType int32, size int32)

// 为所有排行榜设置相同配置
func (m *Manager) SetAllCacheExpireTime(expireTime int64)
func (m *Manager) SetAllHotCacheSize(size int32)
```

#### 内部辅助方法
```go
// 获取指定排行榜的缓存管理器
func (m *Manager) getCacheManager(rankType int32) *CacheManager

// 启动缓存清理任务
func (m *Manager) startCacheCleanupTasks()
```

### 3. 配置加载逻辑更新

在`loadRankConfigs`方法中，为每个排行榜创建独立的缓存管理器：

```go
// 设置缓存配置默认值
if rankConfig.HotCacheSize == 0 {
    if m.realtimeRanks[id] {
        rankConfig.HotCacheSize = 100 // 实时排行榜缓存前100名
    } else {
        rankConfig.HotCacheSize = 300 // 定时排行榜缓存前300名
    }
}

if rankConfig.CacheExpireTime == 0 {
    if m.realtimeRanks[id] {
        rankConfig.CacheExpireTime = 60 // 实时排行榜缓存1分钟
    } else {
        rankConfig.CacheExpireTime = 300 // 定时排行榜缓存5分钟
    }
}

// 为每个排行榜创建独立的缓存管理器
m.cacheManagers[id] = NewCacheManagerWithConfig(rankConfig.HotCacheSize, rankConfig.CacheExpireTime)
```

## 使用方法

### 1. 基本配置

```go
manager := GetRankManager()

// 为特定排行榜设置缓存配置
rankType := int32(2) // 繁荣榜
manager.SetCacheExpireTime(rankType, 300)  // 5分钟缓存
manager.SetHotCacheSize(rankType, 100)     // 缓存前100名

// 或者为所有排行榜设置相同配置
manager.SetAllCacheExpireTime(300)  // 所有排行榜5分钟缓存
manager.SetAllHotCacheSize(100)     // 所有排行榜缓存前100名
```

### 2. 获取缓存统计

```go
// 获取所有排行榜的缓存统计
stats := manager.GetCacheStats()
fmt.Printf("Cache stats: %+v\n", stats)

// 输出示例：
// {
//   "rank_2": {
//     "hot_rank_cache_count": 1,
//     "player_rank_cache_count": 50,
//     "hot_cache_size": 100,
//     "cache_expire_time": 300
//   },
//   "rank_3": {
//     "hot_rank_cache_count": 1,
//     "player_rank_cache_count": 30,
//     "hot_cache_size": 300,
//     "cache_expire_time": 300
//   }
// }
```

## 配置建议

### 实时排行榜（如繁荣榜）
- `HotCacheSize`: 100（缓存前100名）
- `CacheExpireTime`: 60秒（1分钟过期，保证实时性）

### 定时排行榜（如充值榜）
- `HotCacheSize`: 300（缓存前300名）
- `CacheExpireTime`: 300秒（5分钟过期，减少Redis访问）

## 兼容性说明

### API变更
- `SetCacheExpireTime(expireTime int64)` → `SetCacheExpireTime(rankType int32, expireTime int64)`
- `SetHotCacheSize(size int32)` → `SetHotCacheSize(rankType int32, size int32)`

### 新增API
- `SetAllCacheExpireTime(expireTime int64)` - 为所有排行榜设置缓存过期时间
- `SetAllHotCacheSize(size int32)` - 为所有排行榜设置热点缓存大小

### 内部变更
- 所有缓存操作现在都会自动使用对应排行榜的缓存管理器
- 缓存清理任务为每个排行榜独立运行
- 缓存统计信息按排行榜分组显示

## 性能影响

### 优势
1. **精细化配置**：不同排行榜可以根据访问模式配置不同的缓存策略
2. **资源优化**：实时排行榜使用较短缓存时间，定时排行榜使用较长缓存时间
3. **隔离性**：一个排行榜的缓存问题不会影响其他排行榜

### 注意事项
1. **内存使用**：每个排行榜都有独立的缓存，总内存使用量可能增加
2. **管理复杂度**：需要为每个排行榜单独配置和监控缓存参数

## 迁移指南

### 现有代码更新
如果您的代码中使用了以下方法，需要进行更新：

```go
// 旧代码
manager.SetCacheExpireTime(300)
manager.SetHotCacheSize(100)

// 新代码 - 为特定排行榜设置
manager.SetCacheExpireTime(rankType, 300)
manager.SetHotCacheSize(rankType, 100)

// 或者 - 为所有排行榜设置
manager.SetAllCacheExpireTime(300)
manager.SetAllHotCacheSize(100)
```

### 测试代码更新
性能测试代码中直接访问`cacheManager`字段的地方需要更新为使用`getCacheManager`方法。

## 总结

本次更新实现了排行榜缓存管理器的独立化，为不同类型的排行榜提供了更精细的缓存控制能力。通过合理配置，可以在保证性能的同时优化内存使用和数据一致性。
