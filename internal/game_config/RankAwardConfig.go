package game_config
import (
	"kairo_paradise_server/internal/consts"
	"kairo_paradise_server/internal/logger"
	"kairo_paradise_server/services/pb"
	"path/filepath"
)

// auto generated by tools, do not edit it manually
// P排行榜.xlsx RankAwardConfig

type rankAwardConfig struct {
	data map[int32]*pb.ListRankAwardConfig_RankAwardConfig
	ids  []int32
}

var rankAwardConfigFunc = func() *pb.ListRankAwardConfig { return &pb.ListRankAwardConfig{} }

var RankAwardConfig *rankAwardConfig

func newRankAwardConfig() *rankAwardConfig {
	result := &rankAwardConfig{
		data: make(map[int32]*pb.ListRankAwardConfig_RankAwardConfig),
		ids:  make([]int32, 0),
	}

	listConfig, err := loadProtoFile(filepath.Join(consts.GameConfigPath, "RankAwardConfig.bytes"), rankAwardConfigFunc)
	if err != nil {
		logger.Errorf("load RankAwardConfig.bytes error: %v", err)
		return result
	}
	for _, item := range listConfig.List {
		if item.Id != nil {
			id := *item.Id
			result.data[id] = item
			result.ids = append(result.ids, id)
		}
	}
	return result
}

func (c *rankAwardConfig) Item(id int32) *pb.ListRankAwardConfig_RankAwardConfig {
	return c.data[id]
}

func (c *rankAwardConfig) Items() []*pb.ListRankAwardConfig_RankAwardConfig {
	items := make([]*pb.ListRankAwardConfig_RankAwardConfig, 0, len(c.data))
	for _, item := range c.data {
		items = append(items, item)
	}
	return items
}

func (c *rankAwardConfig) GetIds() []int32 {
	return c.ids
}
